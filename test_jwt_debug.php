<?php

require_once 'vendor/autoload.php';

use <PERSON><PERSON>\JWTAuth\Facades\JWTAuth;
use <PERSON><PERSON>\JWTAuth\Facades\JWTFactory;
use App\Models\Employee;

// Initialize Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== JWT Debug Test ===\n";

// Test 1: Find employee
echo "1. Finding employee...\n";
$employee = Employee::where('username', 'test_employee')->first();

if ($employee) {
    echo "Employee found: ID {$employee->id}, Name: {$employee->full_name}\n";
    
    // Test 2: Generate JWT token manually
    echo "\n2. Generating JWT token...\n";
    try {
        $token = JWTAuth::fromUser($employee);
        echo "Token generated: " . substr($token, 0, 50) . "...\n";
        
        // Test 3: Verify token
        echo "\n3. Verifying token...\n";
        JWTAuth::setToken($token);
        $payload = JWTAuth::getPayload();
        echo "Token payload: " . json_encode($payload->toArray()) . "\n";
        
        // Test 4: Get user from token
        echo "\n4. Getting user from token...\n";
        $userFromToken = JWTAuth::authenticate($token);
        if ($userFromToken) {
            echo "User from token: ID {$userFromToken->id}, Name: {$userFromToken->full_name}\n";
        } else {
            echo "Failed to get user from token\n";
        }
        
    } catch (Exception $e) {
        echo "JWT Error: " . $e->getMessage() . "\n";
    }
    
} else {
    echo "Employee not found!\n";
}

echo "\n=== Test Complete ===\n";
