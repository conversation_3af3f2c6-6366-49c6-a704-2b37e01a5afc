// 🧪 Flutter Notification Test - FIXED VERSIONS
// Copy this code to test the fixes for multiple recipients and attachments

import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:file_picker/file_picker.dart';
import 'package:path/path.dart';

class NotificationTestFixes {
  static const String baseUrl = 'http://localhost/appnote-api/public/api';
  static const String token = 'YOUR_TOKEN_HERE'; // Replace with actual token

  static Map<String, String> get headers => {
    'Authorization': 'Bearer $token',
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  static Map<String, String> get multipartHeaders => {
    'Authorization': 'Bearer $token',
    'Accept': 'application/json',
  };

  /// ✅ FIXED: Send notification with multiple recipients
  static Future<void> testMultipleRecipients() async {
    print('🧪 Testing multiple recipients...');
    
    try {
      final requestBody = {
        'title': 'Test Multiple Recipients - FIXED',
        'message': 'This notification should go to multiple recipients',
        'sender_id': 1,
        'sender_name': 'Flutter Test App',
        'sender_type': 'admin',
        'recipient_type': 'students',
        'recipient_ids': [1, 2, 3, 4, 5], // ✅ Multiple recipients
        'priority': 'high',
      };

      print('📤 Sending data: ${jsonEncode(requestBody)}');

      final response = await http.post(
        Uri.parse('$baseUrl/notifications'),
        headers: headers,
        body: jsonEncode(requestBody),
      );

      print('📥 Response status: ${response.statusCode}');
      print('📥 Response body: ${response.body}');

      if (response.statusCode == 201) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          print('✅ SUCCESS: Notification sent to multiple recipients');
          print('✅ Notification ID: ${data['data']['id']}');
          print('✅ Recipients: ${data['data']['recipient_ids']}');
        } else {
          print('❌ FAILED: ${data['message']}');
        }
      } else {
        print('❌ HTTP Error: ${response.statusCode}');
      }
    } catch (e) {
      print('❌ Exception: $e');
    }
  }

  /// ✅ FIXED: Send notification with file attachments
  static Future<void> testFileAttachments() async {
    print('\n🧪 Testing file attachments...');
    
    try {
      // Create test files
      final testFile1 = await _createTestFile('test_document.txt', 'This is a test document content.');
      final testFile2 = await _createTestFile('test_image.txt', 'This simulates an image file.');
      
      final files = [testFile1, testFile2];
      final recipientIds = [1, 2, 3];

      var request = http.MultipartRequest(
        'POST',
        Uri.parse('$baseUrl/notifications'),
      );

      request.headers.addAll(multipartHeaders);

      // Add form fields
      request.fields.addAll({
        'title': 'Test File Attachments - FIXED',
        'message': 'This notification has file attachments',
        'sender_id': '1',
        'sender_name': 'Flutter Test App',
        'sender_type': 'admin',
        'recipient_type': 'students',
        'priority': 'high',
      });

      // ✅ FIXED: Add recipient_ids as array
      for (int i = 0; i < recipientIds.length; i++) {
        request.fields['recipient_ids[$i]'] = recipientIds[i].toString();
      }

      print('📤 Form fields: ${request.fields}');
      print('📤 Recipients: $recipientIds');
      print('📤 Files count: ${files.length}');

      // Add files
      for (int i = 0; i < files.length; i++) {
        File file = files[i];
        String filename = basename(file.path);
        
        print('📎 Adding file $i: $filename');
        
        request.files.add(await http.MultipartFile.fromPath(
          'attachments[]', // ✅ FIXED: Use attachments[] not attachments
          file.path,
          filename: filename,
        ));
      }

      print('📤 Total files added: ${request.files.length}');

      final response = await request.send();
      final responseBody = await response.stream.bytesToString();

      print('📥 Response status: ${response.statusCode}');
      print('📥 Response body: $responseBody');

      if (response.statusCode == 201) {
        final data = jsonDecode(responseBody);
        if (data['success'] == true) {
          print('✅ SUCCESS: Notification with attachments sent');
          print('✅ Notification ID: ${data['data']['id']}');
          print('✅ Recipients: ${data['data']['recipient_ids']}');
          print('✅ Attachments: ${data['data']['attachment_path']}');
          print('✅ Attachment names: ${data['data']['attachment_name']}');
        } else {
          print('❌ FAILED: ${data['message']}');
        }
      } else {
        print('❌ HTTP Error: ${response.statusCode}');
      }

      // Clean up test files
      for (File file in files) {
        if (await file.exists()) {
          await file.delete();
        }
      }

    } catch (e) {
      print('❌ Exception: $e');
    }
  }

  /// ✅ FIXED: Send notification with attachment paths
  static Future<void> testAttachmentPaths() async {
    print('\n🧪 Testing attachment paths...');
    
    try {
      final requestBody = {
        'title': 'Test Attachment Paths - FIXED',
        'message': 'This notification has predefined attachment paths',
        'sender_id': 1,
        'sender_name': 'Flutter Test App',
        'sender_type': 'admin',
        'recipient_type': 'students',
        'recipient_ids': [1, 2, 3, 4], // ✅ Multiple recipients
        'priority': 'medium',
        'attachment_paths': [
          'notifications/test_document.pdf',
          'notifications/sample_image.jpg'
        ],
        'attachment_names': [
          'Test Document.pdf',
          'Sample Image.jpg'
        ],
      };

      print('📤 Sending data: ${jsonEncode(requestBody)}');

      final response = await http.post(
        Uri.parse('$baseUrl/notifications'),
        headers: headers,
        body: jsonEncode(requestBody),
      );

      print('📥 Response status: ${response.statusCode}');
      print('📥 Response body: ${response.body}');

      if (response.statusCode == 201) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          print('✅ SUCCESS: Notification with attachment paths sent');
          print('✅ Notification ID: ${data['data']['id']}');
          print('✅ Recipients: ${data['data']['recipient_ids']}');
          print('✅ Attachment paths: ${data['data']['attachment_path']}');
          print('✅ Attachment names: ${data['data']['attachment_name']}');
        } else {
          print('❌ FAILED: ${data['message']}');
        }
      } else {
        print('❌ HTTP Error: ${response.statusCode}');
      }
    } catch (e) {
      print('❌ Exception: $e');
    }
  }

  /// Helper: Create test file
  static Future<File> _createTestFile(String filename, String content) async {
    final directory = Directory.systemTemp;
    final file = File('${directory.path}/$filename');
    await file.writeAsString(content);
    return file;
  }

  /// Run all tests
  static Future<void> runAllTests() async {
    print('🚀 Starting Flutter Notification Tests - FIXED VERSIONS\n');
    
    await testMultipleRecipients();
    await testFileAttachments();
    await testAttachmentPaths();
    
    print('\n✅ All tests completed!');
    print('\n📋 Check your Laravel API logs and database to verify:');
    print('   - Multiple recipients are saved correctly');
    print('   - File attachments are uploaded and saved');
    print('   - Attachment paths and names are stored properly');
  }
}

// Usage example:
void main() async {
  // Update the token before running
  // NotificationTestFixes.token = 'your_actual_token_here';
  
  await NotificationTestFixes.runAllTests();
}

/*
🔧 FIXES APPLIED:

1. ✅ Multiple Recipients:
   - Use recipient_ids as array: [1, 2, 3, 4, 5]
   - In multipart: use recipient_ids[0], recipient_ids[1], etc.

2. ✅ File Attachments:
   - Use 'attachments[]' field name (with brackets)
   - Add proper debugging output
   - Validate files before sending

3. ✅ Attachment Paths:
   - Send attachment_paths and attachment_names as arrays
   - Support for predefined file paths

4. ✅ Debugging:
   - Added comprehensive console output
   - Clear success/failure messages
   - Response body logging

🧪 TO TEST:
1. Update the token in the code
2. Run the tests
3. Check console output for success/failure
4. Verify in Laravel database that:
   - recipient_ids contains multiple IDs
   - attachment_path contains file paths
   - attachment_name contains file names
*/
