@php
use Illuminate\Support\Facades\Auth;
@endphp
@extends('layouts.app')

@section('styles')
<style>
    .notification-title {
        color: var(--costa-del-sol);
        transition: color 0.2s ease;
        text-decoration: none;
        position: relative;
    }
    
    .notification-title:hover {
        color: var(--locust);
    }
    
    .notification-title:after {
        content: '';
        position: absolute;
        width: 100%;
        transform: scaleX(0);
        height: 1px;
        bottom: -2px;
        left: 0;
        background-color: var(--locust);
        transform-origin: bottom right;
        transition: transform 0.2s ease-out;
    }
    
    .notification-title:hover:after {
        transform: scaleX(1);
        transform-origin: bottom left;
    }
    
    .stats-card {
        border-radius: 10px;
        overflow: hidden;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        box-shadow: 0 4px 6px rgba(0,0,0,0.05);
        height: 100%;
    }
    
    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    
    .stats-card .card-body {
        padding: 1.5rem;
    }
    
    .stats-icon {
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 10px;
        margin-bottom: 15px;
    }
    
    .stats-title {
        font-size: 0.9rem;
        color: var(--mid-gray);
        margin-bottom: 5px;
    }
    
    .stats-value {
        font-size: 1.8rem;
        font-weight: 700;
        color: var(--costa-del-sol);
        margin-bottom: 0;
    }
    
    .notification-list {
        max-height: 350px;
        overflow-y: auto;
    }
    
    .notification-card {
        margin-bottom: 15px;
        border-radius: 10px;
        overflow: hidden;
        border: none;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        transition: transform 0.2s ease;
    }
    
    .notification-card:hover {
        transform: translateX(5px);
    }
    
    .notification-card .card-body {
        padding: 1rem;
    }
    
    .notification-date {
        font-size: 0.8rem;
        color: var(--mid-gray);
    }
    
    .notification-indicator {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 5px;
    }
    
    .unread {
        background-color: var(--locust);
    }
    
    .read {
        background-color: var(--light-gray);
    }
    
    .welcome-section {
        background-color: #f8f9fa;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    }
    
    .welcome-heading {
        color: var(--costa-del-sol);
        font-weight: 700;
    }
    
    .welcome-subheading {
        color: var(--gurkha);
    }
</style>
@endsection

@section('content')
<div class="container-fluid main-content px-4">
    <div class="row">
        <div class="col-md-9">
            <div class="d-flex justify-content-between align-items-center mb-4 mt-2">
                <div>
                    <h2 class="mb-1" style="color: var(--costa-del-sol); font-weight: 700;">Employee Dashboard</h2>
                    <p class="mb-0" style="color: var(--gurkha);">Welcome, {{ $employee->full_name ?? $employee->username }}</p>
                </div>
                <div>
                    <span class="badge bg-primary p-2">Employee Portal</span>
                </div>
            </div>
            
            <!-- Personal Information, Employment Information and Notifications in same row -->
            <div class="row mb-4">
                <!-- Personal Information Card -->
                <div class="col-md-4">
                    <div class="card h-100">
                        <div class="card-header" style="background-color: var(--thistle-green);">
                            <i class="fas fa-user-circle me-2"></i> Personal Information
                        </div>
                        <div class="card-body" style="background-color: var(--parchment);">
                            <div class="row mb-2">
                                <div class="col-md-5 fw-bold">Full Name:</div>
                                <div class="col-md-7">{{ $employee->full_name }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-md-5 fw-bold">Username:</div>
                                <div class="col-md-7">{{ $employee->username }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-md-5 fw-bold">Phone:</div>
                                <div class="col-md-7">{{ $employee->phone }}</div>
                            </div>
                            <div class="mt-3 d-flex flex-wrap justify-content-center gap-2">
                                <a href="{{ route('employee.profile.edit') }}" class="btn btn-sm" style="background-color: var(--costa-del-sol); color: white;">
                                    <i class="fas fa-user-edit me-1"></i> Update Profile
                                </a>
                                <a href="{{ route('employee.profile.change-password') }}" class="btn btn-sm" style="background-color: var(--costa-del-sol); color: white;">
                                    <i class="fas fa-key me-1"></i> Change Password
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Employment Information Card -->
                <div class="col-md-4">
                    <div class="card h-100">
                        <div class="card-header" style="background-color: var(--thistle-green);">
                            <i class="fas fa-briefcase me-2"></i> Employment Information
                        </div>
                        <div class="card-body" style="background-color: var(--parchment);">
                            <div class="row mb-2">
                                <div class="col-md-5 fw-bold">Type:</div>
                                <div class="col-md-7">{{ $employee->employee_type ?? 'Not specified' }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-md-5 fw-bold">Contract:</div>
                                <div class="col-md-7">{{ $employee->contract_type ?? 'Not specified' }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-md-5 fw-bold">Status:</div>
                                <div class="col-md-7">
                                    @php
                                        $statusText = $employee->job_status ?? 'Active';
                                        $statusColor = '#28a745'; // Default green for Active
                                        if ($statusText == 'Inactive' || $statusText == 'Terminated') {
                                            $statusColor = '#dc3545'; // Red
                                        } elseif ($statusText == 'On Leave') {
                                            $statusColor = '#fd7e14'; // Orange
                                        }
                                    @endphp
                                    <span style="color: {{ $statusColor }}; font-weight: bold;">{{ $statusText }}</span>
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-md-5 fw-bold">Financial #:</div>
                                <div class="col-md-7">{{ $employee->financial_number ?? 'Not specified' }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-md-5 fw-bold">Auto #:</div>
                                <div class="col-md-7">{{ $employee->automatic_number ?? 'Not specified' }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-md-5 fw-bold">State Coop #:</div>
                                <div class="col-md-7">{{ $employee->state_cooperative_number ?? 'Not specified' }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-md-5 fw-bold">Bank Account:</div>
                                <div class="col-md-7">{{ $employee->bank_account_number ?? 'Not specified' }}</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Notification Icon Card -->
                <div class="col-md-4">
                    <div class="card h-100">
                        <div class="card-header" style="background-color: var(--thistle-green);">
                            <i class="fas fa-bell me-2"></i> Notifications
                        </div>
                        <div class="card-body d-flex align-items-center justify-content-center" style="background-color: var(--parchment);">
                            <a href="{{ route('employee.notifications') }}" style="text-decoration: none; display: block; text-align: center;">
                                <div class="position-relative d-inline-block mb-3">
                                    <i class="fas fa-bell fa-5x" style="color: var(--costa-del-sol);"></i>
                                    
                                    @php
                                    // Count unread notifications
                                    $badgeCount = 0;
                                    if (Auth::guard('employee')->check()) {
                                        $employeeId = Auth::guard('employee')->id();
                                        $badgeCount = \App\Models\NotificationRecipient::where('recipient_id', $employeeId)
                                            ->where('recipient_type', 'employee')
                                            ->whereNull('read_at')
                                            ->count();
                                    }
                                    @endphp
                                    
                                    <!-- Only show badge if there are notifications -->
                                    @if($badgeCount > 0)
                                    <div class="position-absolute" 
                                         style="top: -10px; right: -10px; 
                                         min-width: 28px; height: 28px; 
                                         background-color: #ff0000; 
                                         border-radius: 50%; 
                                         border: 2px solid white; 
                                         color: white; 
                                         font-weight: bold; 
                                         display: flex; 
                                         justify-content: center; 
                                         align-items: center; 
                                         font-size: 16px; 
                                         box-shadow: 0 2px 5px rgba(0,0,0,0.3); 
                                         padding: 2px;">
                                        {{ $badgeCount }}
                                    </div>
                                    @endif
                                </div>
                                <div>
                                    <span class="btn btn-sm" style="background-color: var(--costa-del-sol); color: white; border: none;">
                                        <i class="fas fa-eye me-1"></i> View Notifications
                                    </span>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Dashboard content will continue here -->
        </div>
        
        <!-- Right sidebar sections removed as requested -->
    </div>
</div>
@endsection
