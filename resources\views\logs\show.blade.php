@extends('layouts.app')

@section('content')
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4 mt-3">
        <div>
            <h2 class="mb-1" style="color: var(--costa-del-sol); font-weight: 700;">Log Details</h2>
            <p class="mb-0" style="color: var(--gurkha);">Viewing: {{ $filename }}</p>
        </div>
        <div>
            <a href="{{ route('logs.index') }}" class="btn btn-sm btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Logs
            </a>
        </div>
    </div>

    <div class="card modern-card mb-3">
        <div class="card-header bg-white">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-info-circle me-2"></i> Log File Information
                </div>
                <div>
                    <form action="{{ route('logs.destroy', $filename) }}" method="POST" class="d-inline">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-sm btn-danger" 
                            onclick="return confirm('Are you sure you want to delete this log file? This action cannot be undone.')">
                            <i class="fas fa-trash me-1"></i> Delete
                        </button>
                    </form>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <p><strong>File:</strong> {{ $filename }}</p>
                </div>
                <div class="col-md-4">
                    <p><strong>Size:</strong> {{ $size }}</p>
                </div>
                <div class="col-md-4">
                    <p><strong>Last Modified:</strong> {{ $modified }}</p>
                </div>
            </div>
        </div>
    </div>

    <div class="card modern-card">
        <div class="card-header bg-white">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-file-alt me-2"></i> Log Contents
                </div>
                <div>
                    <button id="toggleFilterBtn" class="btn btn-sm btn-outline-secondary me-2">
                        <i class="fas fa-filter me-1"></i> Filter
                    </button>
                    <button id="refreshBtn" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-sync-alt me-1"></i> Refresh
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div id="filterOptions" class="bg-light p-3 border-bottom" style="display: none;">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <div class="form-check">
                            <input class="form-check-input log-filter" type="checkbox" id="filterError" data-level="error" checked>
                            <label class="form-check-label" for="filterError">
                                <span class="badge bg-danger">Error</span>
                            </label>
                        </div>
                    </div>
                    <div class="col-md-3 mb-2">
                        <div class="form-check">
                            <input class="form-check-input log-filter" type="checkbox" id="filterWarning" data-level="warning" checked>
                            <label class="form-check-label" for="filterWarning">
                                <span class="badge bg-warning text-dark">Warning</span>
                            </label>
                        </div>
                    </div>
                    <div class="col-md-3 mb-2">
                        <div class="form-check">
                            <input class="form-check-input log-filter" type="checkbox" id="filterInfo" data-level="info" checked>
                            <label class="form-check-label" for="filterInfo">
                                <span class="badge bg-info">Info</span>
                            </label>
                        </div>
                    </div>
                    <div class="col-md-3 mb-2">
                        <div class="form-check">
                            <input class="form-check-input log-filter" type="checkbox" id="filterDebug" data-level="debug" checked>
                            <label class="form-check-label" for="filterDebug">
                                <span class="badge bg-secondary">Debug</span>
                            </label>
                        </div>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-6">
                        <div class="input-group input-group-sm">
                            <input type="text" id="textFilter" class="form-control" placeholder="Filter by text...">
                            <button class="btn btn-outline-secondary" type="button" id="clearFilterBtn">Clear</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th class="ps-3" style="width: 180px;">Timestamp</th>
                            <th>Message</th>
                        </tr>
                    </thead>
                    <tbody id="logContent">
                        @forelse($content as $entry)
                        <tr class="log-entry {{ $entry['level'] }}">
                            <td class="ps-3 log-date" style="white-space: nowrap;">
                                {{ $entry['date'] }}
                            </td>
                            <td class="log-message">
                                @php
                                    $levelClass = 'secondary';
                                    if ($entry['level'] === 'error') $levelClass = 'danger';
                                    if ($entry['level'] === 'warning') $levelClass = 'warning';
                                    if ($entry['level'] === 'info') $levelClass = 'info';
                                @endphp
                                <span class="badge bg-{{ $levelClass }} me-2">{{ strtoupper($entry['level'] ?: 'LOG') }}</span>
                                <span>{{ $entry['content'] }}</span>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="2" class="text-center py-4">
                                <div class="empty-state">
                                    <i class="fas fa-file-alt fa-2x mb-3 text-muted"></i>
                                    <p>No log entries found or the log format is not recognized</p>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle filter options
    document.getElementById('toggleFilterBtn').addEventListener('click', function() {
        const filterOptions = document.getElementById('filterOptions');
        filterOptions.style.display = filterOptions.style.display === 'none' ? 'block' : 'none';
    });
    
    // Refresh button
    document.getElementById('refreshBtn').addEventListener('click', function() {
        window.location.reload();
    });
    
    // Filter functionality
    const logFilters = document.querySelectorAll('.log-filter');
    const textFilter = document.getElementById('textFilter');
    const logEntries = document.querySelectorAll('.log-entry');
    const clearFilterBtn = document.getElementById('clearFilterBtn');
    
    function filterLogs() {
        // Get selected levels
        const selectedLevels = [];
        logFilters.forEach(filter => {
            if (filter.checked) {
                selectedLevels.push(filter.dataset.level);
            }
        });
        
        // Get text filter
        const filterText = textFilter.value.toLowerCase();
        
        // Filter log entries
        logEntries.forEach(entry => {
            const level = entry.classList[1];
            const message = entry.querySelector('.log-message').textContent.toLowerCase();
            
            const levelMatch = selectedLevels.includes(level);
            const textMatch = !filterText || message.includes(filterText);
            
            entry.style.display = levelMatch && textMatch ? 'table-row' : 'none';
        });
    }
    
    // Add event listeners
    logFilters.forEach(filter => {
        filter.addEventListener('change', filterLogs);
    });
    
    textFilter.addEventListener('input', filterLogs);
    
    clearFilterBtn.addEventListener('click', function() {
        textFilter.value = '';
        logFilters.forEach(filter => {
            filter.checked = true;
        });
        filterLogs();
    });
});
</script>
@endsection
