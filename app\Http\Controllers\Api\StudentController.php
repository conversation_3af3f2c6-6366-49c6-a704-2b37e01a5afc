<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Student;
use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Tymon\JWTAuth\Facades\JWTAuth;

class StudentController extends Controller
{
    /**
     * Display a listing of students.
     */
    public function index()
    {
        try {
            $students = Student::orderBy('created_at', 'desc')->get();
            
            return response()->json([
                'success' => true,
                'data' => $students,
                'message' => 'Students retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve students: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created student.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'username' => 'required|string|max:50|unique:students,username',
            'full_name' => 'required|string|max:255',
            'phone' => 'nullable|string|max:50',
            'class' => 'nullable|string|max:100',
            'nationality' => 'nullable|string|max:100',
            'specialization' => 'nullable|string|max:100',
            'section' => 'nullable|string|max:50',
            'level' => 'nullable|string|max:50',
            'result' => 'nullable|string|max:50',
            'password' => 'nullable|string|min:3',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $student = Student::create([
                'username' => $request->username,
                'full_name' => $request->full_name,
                'phone' => $request->phone,
                'class' => $request->class,
                'nationality' => $request->nationality,
                'specialization' => $request->specialization,
                'section' => $request->section,
                'level' => $request->level,
                'result' => $request->result,
                'password' => Hash::make($request->password ?? 'stu123'),
                'is_active' => true,
            ]);

            return response()->json([
                'success' => true,
                'data' => $student,
                'message' => 'Student created successfully'
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create student: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified student.
     */
    public function show(string $id)
    {
        try {
            $student = Student::findOrFail($id);
            
            return response()->json([
                'success' => true,
                'data' => $student,
                'message' => 'Student retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Student not found'
            ], 404);
        }
    }

    /**
     * Update the specified student.
     */
    public function update(Request $request, string $id)
    {
        $validator = Validator::make($request->all(), [
            'username' => 'required|string|max:50|unique:students,username,' . $id,
            'full_name' => 'required|string|max:255',
            'phone' => 'nullable|string|max:50',
            'class' => 'nullable|string|max:100',
            'nationality' => 'nullable|string|max:100',
            'specialization' => 'nullable|string|max:100',
            'section' => 'nullable|string|max:50',
            'level' => 'nullable|string|max:50',
            'result' => 'nullable|string|max:50',
            'password' => 'nullable|string|min:3',
            'is_active' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $student = Student::findOrFail($id);

            $updateData = [
                // Username is not included as it should not be editable
                'full_name' => $request->full_name,
                'phone' => $request->phone,
                'class' => $request->class,
                'nationality' => $request->nationality,
                'specialization' => $request->specialization,
                'section' => $request->section,
                'level' => $request->level,
                'result' => $request->result,
                'is_active' => $request->is_active ?? true,
            ];

            if ($request->filled('password')) {
                $updateData['password'] = Hash::make($request->password);
            }

            $student->update($updateData);

            return response()->json([
                'success' => true,
                'data' => $student,
                'message' => 'Student updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update student: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified student.
     */
    public function destroy(string $id)
    {
        try {
            $student = Student::findOrFail($id);
            $student->delete();

            return response()->json([
                'success' => true,
                'message' => 'Student deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete student: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all unique classes.
     */
    public function getClasses()
    {
        try {
            $classes = Student::select('class')
                             ->distinct()
                             ->whereNotNull('class')
                             ->orderBy('class')
                             ->pluck('class')
                             ->toArray();

            return response()->json([
                'success' => true,
                'data' => $classes,
                'message' => 'Classes retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve classes: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete all students (Admin only)
     */
    public function deleteAll()
    {
        try {
            $count = Student::count();

            if ($count === 0) {
                return response()->json([
                    'success' => true,
                    'message' => 'No students to delete',
                    'deleted_count' => 0
                ]);
            }

            Student::truncate(); // This will delete all records and reset auto-increment

            return response()->json([
                'success' => true,
                'message' => "Successfully deleted all $count students",
                'deleted_count' => $count
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete all students: ' . $e->getMessage()
            ], 500);
        }
    }

    // ========== STUDENT DASHBOARD APIs ==========

    /**
     * Get current student's personal information
     */
    public function getPersonalInfo()
    {
        try {
            $student = Auth::user();

            if (!$student instanceof Student) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized - Student access required'
                ], 403);
            }

            $personalInfo = [
                'id' => $student->id,
                'username' => $student->username,
                'full_name' => $student->full_name,
                'phone' => $student->phone,
                'nationality' => $student->nationality,
                'is_active' => $student->is_active,
                'created_at' => $student->created_at,
                'updated_at' => $student->updated_at,
            ];

            return response()->json([
                'success' => true,
                'data' => $personalInfo,
                'message' => 'Personal information retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve personal information: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update current student's personal information
     */
    public function updatePersonalInfo(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'full_name' => 'required|string|max:255',
            'phone' => 'nullable|string|max:50',
            'nationality' => 'required|string|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $student = Auth::user();

            if (!$student instanceof Student) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized - Student access required'
                ], 403);
            }

            $student->update([
                'full_name' => $request->full_name,
                'phone' => $request->phone,
                'nationality' => $request->nationality,
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'id' => $student->id,
                    'username' => $student->username,
                    'full_name' => $student->full_name,
                    'phone' => $student->phone,
                    'nationality' => $student->nationality,
                    'is_active' => $student->is_active,
                    'created_at' => $student->created_at,
                    'updated_at' => $student->updated_at,
                ],
                'message' => 'Personal information updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update personal information: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get current student's academic information
     */
    public function getAcademicInfo()
    {
        try {
            $student = Auth::user();

            if (!$student instanceof Student) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized - Student access required'
                ], 403);
            }

            $academicInfo = [
                'specialization' => $student->specialization,
                'section' => $student->section,
                'class' => $student->class,
                'level' => $student->level,
                'result' => $student->result,
                'enrollment_date' => $student->created_at,
            ];

            return response()->json([
                'success' => true,
                'data' => $academicInfo,
                'message' => 'Academic information retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve academic information: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test authentication
     */
    public function testAuth()
    {
        try {
            $student = Auth::guard('api')->user();

            if (!$student) {
                return response()->json([
                    'success' => false,
                    'message' => 'Token is invalid or expired',
                    'authenticated' => false
                ], 401);
            }

            return response()->json([
                'success' => true,
                'message' => 'Authentication successful',
                'authenticated' => true,
                'student' => [
                    'id' => $student->id,
                    'username' => $student->username,
                    'full_name' => $student->full_name,
                    'specialization' => $student->specialization,
                    'class' => $student->class,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication error: ' . $e->getMessage(),
                'authenticated' => false
            ], 500);
        }
    }

    /**
     * Get current student's dashboard data
     */
    public function getDashboard()
    {
        try {
            // Get the authenticated student from JWT
            $student = Auth::guard('api')->user();

            if (!$student) {
                return response()->json([
                    'success' => false,
                    'message' => 'Token is invalid or expired'
                ], 401);
            }

            // Get notifications count for this student
            $notificationsCount = Notification::where(function($query) use ($student) {
                $query->where('target_audience', 'جميع الطلاب')
                      ->orWhere('target_audience', 'like', '%طلاب%')
                      ->orWhere('target_audience', 'all')  // Handle English 'all'
                      ->orWhere('target_audience', 'students')  // Handle English 'students'
                      ->orWhere('target_audience', $student->class)
                      ->orWhere('target_audience', $student->specialization);
            })->where('is_active', true)->count();

            // For now, assume all notifications are unread since we don't have read tracking yet
            $unreadNotificationsCount = $notificationsCount;

            $dashboardData = [
                'student_info' => [
                    'name' => $student->full_name,
                    'username' => $student->username,
                    'class' => $student->class,
                    'specialization' => $student->specialization,
                ],
                'notifications' => [
                    'total' => $notificationsCount,
                    'unread' => $unreadNotificationsCount,
                ],
                'academic' => [
                    'specialization' => $student->specialization,
                    'section' => $student->section,
                    'class' => $student->class,
                    'level' => $student->level,
                    'result' => $student->result,
                ],
                'statistics' => [
                    'courses' => [
                        'total' => 6, // Mock data - replace with actual course count
                        'active' => 5,
                    ],
                    'assignments' => [
                        'total' => 12, // Mock data - replace with actual assignment count
                        'pending' => 2,
                    ],
                    'grades' => [
                        'average' => 85.5, // Mock data - replace with actual grade average
                        'latest' => 'A',
                    ],
                ],
            ];

            return response()->json([
                'success' => true,
                'data' => $dashboardData,
                'message' => 'Dashboard data retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve dashboard data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get current student's notifications
     */
    public function getNotifications(Request $request)
    {
        try {
            // Get the authenticated student from JWT
            $student = Auth::guard('api')->user();

            if (!$student) {
                return response()->json([
                    'success' => false,
                    'message' => 'Token is invalid or expired'
                ], 401);
            }

            // Get notifications for this specific student using NotificationRecipient table
            $query = \App\Models\NotificationRecipient::where('recipient_id', $student->id)
                ->where('recipient_type', 'student')
                ->with('notification');

            // Filter by status if provided
            if ($request->has('status')) {
                if ($request->status === 'read') {
                    $query->whereNotNull('read_at');
                } elseif ($request->status === 'unread') {
                    $query->whereNull('read_at');
                }
            }

            $recipients = $query->orderBy('created_at', 'desc')
                               ->paginate($request->get('per_page', 20));

            // Transform recipients to notification format
            $notifications = $recipients->getCollection()->map(function ($recipient) {
                $notification = $recipient->notification;
                if ($notification) {
                    $notification->is_read = !is_null($recipient->read_at);
                    $notification->status = is_null($recipient->read_at) ? 'unread' : 'read';
                    $notification->read_at = $recipient->read_at;
                    return $notification;
                }
                return null;
            })->filter(); // Remove null values

            return response()->json([
                'success' => true,
                'data' => $notifications->values(), // Use values() to reset array keys
                'pagination' => [
                    'current_page' => $recipients->currentPage(),
                    'last_page' => $recipients->lastPage(),
                    'per_page' => $recipients->perPage(),
                    'total' => $recipients->total(),
                ],
                'message' => 'Notifications retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve notifications: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mark notification as read for current student
     */
    public function markNotificationAsRead($notificationId)
    {
        try {
            // Get the authenticated student from JWT
            $student = Auth::guard('api')->user();

            if (!$student) {
                return response()->json([
                    'success' => false,
                    'message' => 'Token is invalid or expired'
                ], 401);
            }

            $notification = Notification::findOrFail($notificationId);

            // Check if notification is relevant to this student
            $isRelevant = $notification->target_audience === 'جميع الطلاب' ||
                         str_contains($notification->target_audience, 'طلاب') ||
                         $notification->target_audience === 'all' ||
                         $notification->target_audience === 'students' ||
                         $notification->target_audience === $student->class ||
                         $notification->target_audience === $student->specialization;

            if (!$isRelevant) {
                return response()->json([
                    'success' => false,
                    'message' => 'Notification not accessible'
                ], 403);
            }

            // TODO: Implement proper read tracking with notification_reads table
            // For now, we'll just return success without actually tracking reads

            return response()->json([
                'success' => true,
                'message' => 'Notification marked as read (tracking not implemented yet)'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to mark notification as read: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mark notification as unread for current student
     */
    public function markNotificationAsUnread($notificationId)
    {
        try {
            // Get the authenticated student from JWT
            $student = Auth::guard('api')->user();

            if (!$student) {
                return response()->json([
                    'success' => false,
                    'message' => 'Token is invalid or expired'
                ], 401);
            }

            $notification = Notification::findOrFail($notificationId);

            // Check if notification is relevant to this student
            $isRelevant = $notification->target_audience === 'جميع الطلاب' ||
                         str_contains($notification->target_audience, 'طلاب') ||
                         $notification->target_audience === 'all' ||
                         $notification->target_audience === 'students' ||
                         $notification->target_audience === $student->class ||
                         $notification->target_audience === $student->specialization;

            if (!$isRelevant) {
                return response()->json([
                    'success' => false,
                    'message' => 'Notification not accessible'
                ], 403);
            }

            // TODO: Implement proper read tracking with notification_reads table
            // For now, we'll just return success without actually tracking reads

            return response()->json([
                'success' => true,
                'message' => 'Notification marked as unread (tracking not implemented yet)'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to mark notification as unread: ' . $e->getMessage()
            ], 500);
        }
    }
}
