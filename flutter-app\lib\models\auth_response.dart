class AuthResponse {
  final String token;
  final String userType;
  final Map<String, dynamic> user;
  final String? message;

  AuthResponse({
    required this.token,
    required this.userType,
    required this.user,
    this.message,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json, String userType) {
    // Handle Laravel API response structure
    Map<String, dynamic> userData = {};
    String token = '';

    // Extract token from data.token
    if (json['data'] != null && json['data']['token'] != null) {
      token = json['data']['token'];
    } else {
      token = json['token'] ?? json['access_token'] ?? '';
    }

    // Extract user data based on user type
    if (json['data'] != null) {
      switch (userType) {
        case 'admin':
          // Admin data is in data.user
          userData = json['data']['user'] ?? {};
          break;
        case 'student':
          // Student data is in data.student
          userData = json['data']['student'] ?? {};
          break;
        case 'employee':
          // Employee data is in data.employee
          userData = json['data']['employee'] ?? {};
          break;
        default:
          // Fallback to user or data
          userData = json['data']['user'] ?? json['data'] ?? {};
      }
    } else if (json['user'] != null) {
      userData = json['user'];
    }

    return AuthResponse(
      token: token,
      userType: userType,
      user: userData,
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'token': token,
      'user_type': userType,
      'user': user,
      'message': message,
    };
  }

  @override
  String toString() {
    return 'AuthResponse(token: ${token.substring(0, 20)}..., userType: $userType, message: $message)';
  }
}
