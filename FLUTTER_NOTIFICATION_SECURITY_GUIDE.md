# 🔐 Flutter Notification API Security & Best Practices

## 🛡️ **Security Implementation**

### **Token Management:**
```dart
class TokenManager {
  static const String _tokenKey = 'auth_token';
  static const String _refreshTokenKey = 'refresh_token';

  static Future<void> saveToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_tokenKey, token);
  }

  static Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_tokenKey);
  }

  static Future<void> clearTokens() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_tokenKey);
    await prefs.remove(_refreshTokenKey);
  }

  static Future<bool> isTokenValid() async {
    final token = await getToken();
    if (token == null) return false;
    
    // Decode JWT and check expiration
    try {
      final parts = token.split('.');
      if (parts.length != 3) return false;
      
      final payload = json.decode(
        utf8.decode(base64Url.decode(base64Url.normalize(parts[1])))
      );
      
      final exp = payload['exp'] as int;
      final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      
      return exp > now;
    } catch (e) {
      return false;
    }
  }
}
```

### **Secure HTTP Client:**
```dart
class SecureHttpClient {
  static http.Client? _client;
  
  static http.Client get client {
    if (_client == null) {
      _client = http.Client();
    }
    return _client!;
  }

  static Future<http.Response> securePost(
    String url,
    Map<String, dynamic> body, {
    Map<String, String>? additionalHeaders,
  }) async {
    final token = await TokenManager.getToken();
    if (token == null) {
      throw UnauthorizedException('No authentication token found');
    }

    final headers = {
      'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...?additionalHeaders,
    };

    final response = await client.post(
      Uri.parse(url),
      headers: headers,
      body: jsonEncode(body),
    );

    if (response.statusCode == 401) {
      await TokenManager.clearTokens();
      throw UnauthorizedException('Token expired or invalid');
    }

    return response;
  }

  static Future<http.StreamedResponse> secureMultipartPost(
    String url,
    Map<String, String> fields,
    List<http.MultipartFile> files, {
    Map<String, String>? additionalHeaders,
  }) async {
    final token = await TokenManager.getToken();
    if (token == null) {
      throw UnauthorizedException('No authentication token found');
    }

    var request = http.MultipartRequest('POST', Uri.parse(url));
    
    request.headers.addAll({
      'Authorization': 'Bearer $token',
      'Accept': 'application/json',
      ...?additionalHeaders,
    });

    request.fields.addAll(fields);
    request.files.addAll(files);

    final response = await request.send();

    if (response.statusCode == 401) {
      await TokenManager.clearTokens();
      throw UnauthorizedException('Token expired or invalid');
    }

    return response;
  }
}

class UnauthorizedException implements Exception {
  final String message;
  UnauthorizedException(this.message);
  
  @override
  String toString() => 'UnauthorizedException: $message';
}
```

---

## 📱 **Offline Support & Caching**

### **Notification Cache Manager:**
```dart
class NotificationCacheManager {
  static const String _cacheKey = 'cached_notifications';
  static const String _lastSyncKey = 'last_sync_time';

  static Future<void> cacheNotifications(List<NotificationModel> notifications) async {
    final prefs = await SharedPreferences.getInstance();
    final jsonList = notifications.map((n) => n.toJson()).toList();
    await prefs.setString(_cacheKey, jsonEncode(jsonList));
    await prefs.setInt(_lastSyncKey, DateTime.now().millisecondsSinceEpoch);
  }

  static Future<List<NotificationModel>> getCachedNotifications() async {
    final prefs = await SharedPreferences.getInstance();
    final jsonString = prefs.getString(_cacheKey);
    
    if (jsonString == null) return [];
    
    final jsonList = jsonDecode(jsonString) as List;
    return jsonList.map((json) => NotificationModel.fromJson(json)).toList();
  }

  static Future<DateTime?> getLastSyncTime() async {
    final prefs = await SharedPreferences.getInstance();
    final timestamp = prefs.getInt(_lastSyncKey);
    return timestamp != null ? DateTime.fromMillisecondsSinceEpoch(timestamp) : null;
  }

  static Future<bool> shouldSync() async {
    final lastSync = await getLastSyncTime();
    if (lastSync == null) return true;
    
    final now = DateTime.now();
    final difference = now.difference(lastSync);
    
    return difference.inMinutes > 5; // Sync every 5 minutes
  }
}
```

### **Offline-First Notification Service:**
```dart
class OfflineNotificationService extends NotificationService {
  OfflineNotificationService({required String baseUrl, required String token})
      : super(baseUrl: baseUrl, token: token);

  @override
  Future<NotificationListResponse> getNotifications({
    int page = 1,
    int perPage = 15,
  }) async {
    try {
      // Check if we should sync
      if (await NotificationCacheManager.shouldSync()) {
        final response = await super.getNotifications(page: page, perPage: perPage);
        await NotificationCacheManager.cacheNotifications(response.data);
        return response;
      } else {
        // Return cached data
        final cachedNotifications = await NotificationCacheManager.getCachedNotifications();
        return NotificationListResponse(
          success: true,
          message: 'Loaded from cache',
          data: cachedNotifications,
        );
      }
    } catch (e) {
      // Fallback to cache on network error
      final cachedNotifications = await NotificationCacheManager.getCachedNotifications();
      if (cachedNotifications.isNotEmpty) {
        return NotificationListResponse(
          success: true,
          message: 'Loaded from cache (offline)',
          data: cachedNotifications,
        );
      }
      rethrow;
    }
  }

  Future<void> syncWhenOnline() async {
    try {
      final response = await super.getNotifications();
      await NotificationCacheManager.cacheNotifications(response.data);
    } catch (e) {
      print('Background sync failed: $e');
    }
  }
}
```

---

## 🔄 **Background Sync & Push Notifications**

### **Background Service:**
```dart
class NotificationBackgroundService {
  static const String _taskName = 'notification_sync';

  static Future<void> initialize() async {
    await Workmanager().initialize(callbackDispatcher);
    await schedulePeriodicSync();
  }

  static Future<void> schedulePeriodicSync() async {
    await Workmanager().registerPeriodicTask(
      _taskName,
      _taskName,
      frequency: Duration(minutes: 15),
      constraints: Constraints(
        networkType: NetworkType.connected,
      ),
    );
  }

  static void callbackDispatcher() {
    Workmanager().executeTask((task, inputData) async {
      try {
        final token = await TokenManager.getToken();
        if (token != null) {
          final service = OfflineNotificationService(
            baseUrl: 'your-api-url',
            token: token,
          );
          await service.syncWhenOnline();
        }
        return Future.value(true);
      } catch (e) {
        print('Background sync error: $e');
        return Future.value(false);
      }
    });
  }
}
```

### **Push Notification Handler:**
```dart
class PushNotificationHandler {
  static FirebaseMessaging? _messaging;

  static Future<void> initialize() async {
    _messaging = FirebaseMessaging.instance;

    // Request permission
    await _messaging!.requestPermission(
      alert: true,
      badge: true,
      sound: true,
    );

    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);

    // Handle notification taps
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);
  }

  static void _handleForegroundMessage(RemoteMessage message) {
    // Show local notification
    _showLocalNotification(message);
    
    // Trigger sync
    _triggerSync();
  }

  static Future<void> _handleBackgroundMessage(RemoteMessage message) async {
    // Trigger background sync
    await _triggerSync();
  }

  static void _handleNotificationTap(RemoteMessage message) {
    // Navigate to notification details
    final notificationId = message.data['notification_id'];
    if (notificationId != null) {
      // Navigate to specific notification
      navigatorKey.currentState?.pushNamed(
        '/notification-details',
        arguments: int.parse(notificationId),
      );
    }
  }

  static void _showLocalNotification(RemoteMessage message) {
    // Implementation depends on your local notification package
    // Example with flutter_local_notifications
  }

  static Future<void> _triggerSync() async {
    final token = await TokenManager.getToken();
    if (token != null) {
      final service = OfflineNotificationService(
        baseUrl: 'your-api-url',
        token: token,
      );
      await service.syncWhenOnline();
    }
  }
}
```

---

## 📊 **Performance Optimization**

### **Image Caching for Attachments:**
```dart
class AttachmentImageCache {
  static final Map<String, Uint8List> _cache = {};
  static const int _maxCacheSize = 50; // Max 50 images

  static Future<Uint8List?> getImage(String url) async {
    if (_cache.containsKey(url)) {
      return _cache[url];
    }

    try {
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        final bytes = response.bodyBytes;
        
        // Manage cache size
        if (_cache.length >= _maxCacheSize) {
          _cache.remove(_cache.keys.first);
        }
        
        _cache[url] = bytes;
        return bytes;
      }
    } catch (e) {
      print('Failed to cache image: $e');
    }
    
    return null;
  }

  static void clearCache() {
    _cache.clear();
  }
}
```

### **Lazy Loading List:**
```dart
class LazyNotificationList extends StatefulWidget {
  @override
  _LazyNotificationListState createState() => _LazyNotificationListState();
}

class _LazyNotificationListState extends State<LazyNotificationList> {
  final ScrollController _scrollController = ScrollController();
  final List<NotificationModel> _notifications = [];
  bool _loading = false;
  bool _hasMore = true;
  int _currentPage = 1;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _loadNotifications();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200) {
      if (!_loading && _hasMore) {
        _loadMoreNotifications();
      }
    }
  }

  Future<void> _loadNotifications() async {
    setState(() => _loading = true);
    
    try {
      final service = context.read<NotificationService>();
      final response = await service.getNotifications(page: 1);
      
      setState(() {
        _notifications.clear();
        _notifications.addAll(response.data);
        _currentPage = 1;
        _hasMore = response.data.length >= 15;
      });
    } catch (e) {
      _showError(e.toString());
    } finally {
      setState(() => _loading = false);
    }
  }

  Future<void> _loadMoreNotifications() async {
    setState(() => _loading = true);
    
    try {
      final service = context.read<NotificationService>();
      final response = await service.getNotifications(page: _currentPage + 1);
      
      setState(() {
        _notifications.addAll(response.data);
        _currentPage++;
        _hasMore = response.data.length >= 15;
      });
    } catch (e) {
      _showError(e.toString());
    } finally {
      setState(() => _loading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      controller: _scrollController,
      itemCount: _notifications.length + (_hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index >= _notifications.length) {
          return Center(child: CircularProgressIndicator());
        }
        
        return NotificationCard(notification: _notifications[index]);
      },
    );
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Error: $message')),
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }
}
```

---

## 🧪 **Testing**

### **Unit Tests:**
```dart
// test/notification_service_test.dart
void main() {
  group('NotificationService', () {
    late NotificationService service;
    late MockClient mockClient;

    setUp(() {
      mockClient = MockClient();
      service = NotificationService(
        baseUrl: 'http://test.com/api',
        token: 'test-token',
      );
    });

    test('should send notification successfully', () async {
      // Arrange
      when(mockClient.post(any, headers: anyNamed('headers'), body: anyNamed('body')))
          .thenAnswer((_) async => http.Response(
                jsonEncode({
                  'success': true,
                  'message': 'Notification sent',
                  'data': {'id': 1, 'title': 'Test'}
                }),
                201,
              ));

      // Act
      final result = await service.sendTextNotification(
        title: 'Test',
        message: 'Test message',
        recipientIds: [1, 2, 3],
      );

      // Assert
      expect(result.success, true);
      expect(result.data?.id, 1);
    });

    test('should handle network errors', () async {
      // Arrange
      when(mockClient.post(any, headers: anyNamed('headers'), body: anyNamed('body')))
          .thenThrow(SocketException('Network error'));

      // Act & Assert
      expect(
        () => service.sendTextNotification(
          title: 'Test',
          message: 'Test message',
          recipientIds: [1, 2, 3],
        ),
        throwsA(isA<NotificationException>()),
      );
    });
  });
}
```

---

## 🔧 **Configuration**

### **Environment Configuration:**
```dart
class AppConfig {
  static const String _prodBaseUrl = 'https://your-domain.com/api';
  static const String _devBaseUrl = 'http://localhost/appnote-api/public/api';
  
  static String get baseUrl {
    return kDebugMode ? _devBaseUrl : _prodBaseUrl;
  }
  
  static const Duration requestTimeout = Duration(seconds: 30);
  static const Duration cacheExpiry = Duration(minutes: 5);
  static const int maxRetries = 3;
}
```

**Production-ready implementation!** 🚀
