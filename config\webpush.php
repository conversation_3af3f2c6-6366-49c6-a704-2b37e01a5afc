<?php

return [
    /*
    |--------------------------------------------------------------------------
    | VAPID Configuration
    |--------------------------------------------------------------------------
    |
    | VAPID (Voluntary Application Server Identification) keys are used to
    | identify your application to push services. You need to generate
    | these keys and add them to your .env file.
    |
    | To generate VAPID keys, you can use the following command:
    | php artisan webpush:vapid
    |
    */

    'vapid' => [
        'subject' => env('VAPID_SUBJECT', env('APP_URL')),
        'public_key' => env('VAPID_PUBLIC_KEY'),
        'private_key' => env('VAPID_PRIVATE_KEY'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Default Notification Settings
    |--------------------------------------------------------------------------
    |
    | These are the default settings for push notifications.
    |
    */

    'defaults' => [
        'ttl' => env('WEBPUSH_TTL', 3600), // Time to live in seconds
        'urgency' => env('WEBPUSH_URGENCY', 'normal'), // very-low, low, normal, high
        'topic' => env('WEBPUSH_TOPIC', 'appnote'),
        'batch_size' => env('WEBPUSH_BATCH_SIZE', 100), // Number of notifications to send in one batch
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Icons and Assets
    |--------------------------------------------------------------------------
    |
    | Default icons and assets for push notifications.
    |
    */

    'assets' => [
        'icon' => env('WEBPUSH_ICON', '/icons/icon-192x192.png'),
        'badge' => env('WEBPUSH_BADGE', '/icons/icon-72x72.png'),
        'default_image' => env('WEBPUSH_IMAGE', '/icons/icon-512x512.png'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Cleanup Settings
    |--------------------------------------------------------------------------
    |
    | Settings for cleaning up expired subscriptions.
    |
    */

    'cleanup' => [
        'expired_days' => env('WEBPUSH_EXPIRED_DAYS', 30), // Days after which unused subscriptions are considered expired
        'auto_cleanup' => env('WEBPUSH_AUTO_CLEANUP', true), // Automatically cleanup expired subscriptions
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging
    |--------------------------------------------------------------------------
    |
    | Enable or disable logging for push notifications.
    |
    */

    'logging' => [
        'enabled' => env('WEBPUSH_LOGGING', true),
        'level' => env('WEBPUSH_LOG_LEVEL', 'info'), // debug, info, warning, error
    ],
];
