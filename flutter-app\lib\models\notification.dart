import 'dart:convert';

class AppNotification {
  final int id;
  final String title;
  final String content;
  final String type;
  final String priority;
  final String targetAudience;
  final List<int>? recipientIds;
  final DateTime? scheduledAt;
  final DateTime? expiresAt;
  final String? attachmentPath;
  final String? attachmentName;
  final int? attachmentSize;
  final List<Map<String, dynamic>>? attachments; // Multiple attachments support
  final bool isActive;
  final String? status; // read/unread
  final DateTime? createdAt;
  final DateTime? updatedAt;

  AppNotification({
    required this.id,
    required this.title,
    required this.content,
    required this.type,
    required this.priority,
    required this.targetAudience,
    this.recipientIds,
    this.scheduledAt,
    this.expiresAt,
    this.attachmentPath,
    this.attachmentName,
    this.attachmentSize,
    this.attachments,
    required this.isActive,
    this.status,
    this.createdAt,
    this.updatedAt,
  });

  factory AppNotification.fromJson(Map<String, dynamic> json) {
    return AppNotification(
      id: json['id'] ?? 0,
      title: json['title'] ?? '',
      content:
          json['content'] ??
          json['message'] ??
          '', // Support both content and message
      type: json['type'] ?? 'info',
      priority: json['priority'] ?? 'medium',
      targetAudience: json['target_audience'] ?? 'all',
      recipientIds: json['recipient_ids'] != null
          ? _parseRecipientIds(json['recipient_ids'])
          : null,
      scheduledAt: json['scheduled_at'] != null
          ? DateTime.tryParse(json['scheduled_at'])
          : null,
      expiresAt: json['expires_at'] != null
          ? DateTime.tryParse(json['expires_at'])
          : null,
      attachmentPath: _parseAttachmentPath(json['attachment_path']) ??
          (json['attachments'] != null && json['attachments'].isNotEmpty
              ? json['attachments'][0]['path']
              : null),
      attachmentName: _parseAttachmentName(json['attachment_name']) ??
          (json['attachments'] != null && json['attachments'].isNotEmpty
              ? json['attachments'][0]['name']
              : null),
      attachmentSize: json['attachment_size'] != null
          ? int.tryParse(json['attachment_size'].toString())
          : (json['attachments'] != null && json['attachments'].isNotEmpty
                ? json['attachments'][0]['size']
                : null),
      attachments: _parseAttachments(json),
      isActive: json['is_active'] == 1 || json['is_active'] == true,
      status: json['status'] ?? (json['is_read'] == true ? 'read' : 'unread'),
      createdAt: json['created_at'] != null
          ? DateTime.tryParse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.tryParse(json['updated_at'])
          : null,
    );
  }

  static List<int>? _parseRecipientIds(dynamic recipientIds) {
    if (recipientIds == null) return null;

    if (recipientIds is List) {
      return recipientIds
          .map((id) => int.tryParse(id.toString()) ?? 0)
          .toList();
    }

    if (recipientIds is String) {
      try {
        // Try to parse as JSON array string
        final decoded = jsonDecode(recipientIds);
        if (decoded is List) {
          return decoded.map((id) => int.tryParse(id.toString()) ?? 0).toList();
        }
      } catch (e) {
        // If JSON parsing fails, try to split by comma
        return recipientIds
            .split(',')
            .map((id) => int.tryParse(id.trim()) ?? 0)
            .where((id) => id > 0)
            .toList();
      }
    }

    return null;
  }

  static String? _parseAttachmentPath(dynamic attachmentPath) {
    if (attachmentPath == null) return null;

    if (attachmentPath is String) {
      return attachmentPath.isNotEmpty ? attachmentPath : null;
    }

    if (attachmentPath is List && attachmentPath.isNotEmpty) {
      return attachmentPath.first?.toString();
    }

    return null;
  }

  static String? _parseAttachmentName(dynamic attachmentName) {
    if (attachmentName == null) return null;

    if (attachmentName is String) {
      return attachmentName.isNotEmpty ? attachmentName : null;
    }

    if (attachmentName is List && attachmentName.isNotEmpty) {
      return attachmentName.first?.toString();
    }

    return null;
  }

  static List<Map<String, dynamic>>? _parseAttachments(Map<String, dynamic> json) {
    // First, try to get from 'attachments' field
    if (json['attachments'] != null && json['attachments'] is List) {
      return List<Map<String, dynamic>>.from(
        json['attachments'].map(
          (attachment) => Map<String, dynamic>.from(attachment),
        ),
      );
    }

    // If no 'attachments' field, try to build from attachment_path and attachment_name arrays
    final attachmentPaths = json['attachment_path'];
    final attachmentNames = json['attachment_name'];

    if (attachmentPaths != null && attachmentNames != null) {
      List<String> paths = [];
      List<String> names = [];

      // Parse paths
      if (attachmentPaths is List) {
        paths = attachmentPaths.map((p) => p.toString()).toList();
      } else if (attachmentPaths is String && attachmentPaths.isNotEmpty) {
        paths = [attachmentPaths];
      }

      // Parse names
      if (attachmentNames is List) {
        names = attachmentNames.map((n) => n.toString()).toList();
      } else if (attachmentNames is String && attachmentNames.isNotEmpty) {
        names = [attachmentNames];
      }

      // Build attachments list
      if (paths.isNotEmpty && names.isNotEmpty) {
        List<Map<String, dynamic>> attachments = [];
        final maxLength = paths.length > names.length ? paths.length : names.length;

        for (int i = 0; i < maxLength; i++) {
          final path = i < paths.length ? paths[i] : '';
          final name = i < names.length ? names[i] : 'ملف مرفق';

          if (path.isNotEmpty) {
            attachments.add({
              'path': path,
              'name': name,
              'size': 0, // Default size
              'type': _getFileTypeFromNameStatic(name),
            });
          }
        }

        return attachments.isNotEmpty ? attachments : null;
      }
    }

    return null;
  }

  static String _getFileTypeFromNameStatic(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    switch (extension) {
      case 'pdf':
        return 'pdf';
      case 'doc':
      case 'docx':
        return 'document';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return 'image';
      case 'mp4':
      case 'avi':
      case 'mov':
        return 'video';
      case 'mp3':
      case 'wav':
        return 'audio';
      default:
        return 'file';
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'type': type,
      'priority': priority,
      'target_audience': targetAudience,
      'recipient_ids': recipientIds,
      'scheduled_at': scheduledAt?.toIso8601String(),
      'expires_at': expiresAt?.toIso8601String(),
      'attachment_path': attachmentPath,
      'attachment_name': attachmentName,
      'attachment_size': attachmentSize,
      'attachments': attachments,
      'is_active': isActive,
      'status': status,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  AppNotification copyWith({
    int? id,
    String? title,
    String? content,
    String? type,
    String? priority,
    String? targetAudience,
    List<int>? recipientIds,
    DateTime? scheduledAt,
    DateTime? expiresAt,
    String? attachmentPath,
    String? attachmentName,
    int? attachmentSize,
    List<Map<String, dynamic>>? attachments,
    bool? isActive,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AppNotification(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      targetAudience: targetAudience ?? this.targetAudience,
      recipientIds: recipientIds ?? this.recipientIds,
      scheduledAt: scheduledAt ?? this.scheduledAt,
      expiresAt: expiresAt ?? this.expiresAt,
      attachmentPath: attachmentPath ?? this.attachmentPath,
      attachmentName: attachmentName ?? this.attachmentName,
      attachmentSize: attachmentSize ?? this.attachmentSize,
      attachments: attachments ?? this.attachments,
      isActive: isActive ?? this.isActive,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'AppNotification(id: $id, title: $title, type: $type, priority: $priority)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppNotification && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  // Helper methods for UI
  String get typeDisplay {
    switch (type) {
      case 'info':
        return 'معلومات';
      case 'warning':
        return 'تحذير';
      case 'success':
        return 'نجاح';
      case 'error':
        return 'خطأ';
      default:
        return type;
    }
  }

  String get priorityDisplay {
    switch (priority) {
      case 'low':
        return 'منخفضة';
      case 'medium':
        return 'متوسطة';
      case 'high':
        return 'عالية';
      default:
        return priority;
    }
  }

  String get targetAudienceDisplay {
    switch (targetAudience) {
      case 'all':
        return 'الجميع';
      case 'students':
        return 'الطلاب';
      case 'employees':
        return 'الموظفين';
      case 'specific':
        return 'محدد';
      default:
        return targetAudience;
    }
  }

  String get statusDisplay {
    switch (status) {
      case 'read':
        return 'مقروء';
      case 'unread':
        return 'غير مقروء';
      default:
        return status ?? 'غير محدد';
    }
  }

  bool get isRead => status == 'read';
  bool get isUnread => status == 'unread';
  bool get hasAttachment =>
      (attachmentPath != null && attachmentPath!.isNotEmpty) ||
      (attachments != null && attachments!.isNotEmpty);
  bool get isScheduled => scheduledAt != null;
  bool get isExpired => expiresAt != null && DateTime.now().isAfter(expiresAt!);

  String get formattedAttachmentSize {
    if (attachmentSize == null) return '';

    if (attachmentSize! < 1024) {
      return '$attachmentSize B';
    } else if (attachmentSize! < 1024 * 1024) {
      return '${(attachmentSize! / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(attachmentSize! / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }

  // Multiple attachments getters
  int get attachmentCount => attachments?.length ?? (hasAttachment ? 1 : 0);

  bool get hasMultipleAttachments => attachmentCount > 1;

  List<Map<String, dynamic>> get allAttachments {
    if (attachments != null && attachments!.isNotEmpty) {
      return attachments!;
    } else if (hasAttachment) {
      // Convert single attachment to list format
      return [
        {
          'name': attachmentName ?? 'ملف مرفق',
          'path': attachmentPath ?? '',
          'size': attachmentSize ?? 0,
          'type': _getFileTypeFromName(attachmentName ?? ''),
        },
      ];
    }
    return [];
  }

  String _getFileTypeFromName(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    switch (extension) {
      case 'pdf':
        return 'application/pdf';
      case 'doc':
      case 'docx':
        return 'application/msword';
      case 'xls':
      case 'xlsx':
        return 'application/vnd.ms-excel';
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      default:
        return 'application/octet-stream';
    }
  }
}

// Alias for backward compatibility
typedef NotificationModel = AppNotification;
