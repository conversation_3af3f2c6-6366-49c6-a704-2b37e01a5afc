# اختبار API الإشعارات مع المرفقات
$baseUrl = "http://localhost/appnote-api/public/api"

Write-Host "=== اختبار API الإشعارات مع المرفقات ===" -ForegroundColor Green

# الحصول على admin token أولاً
Write-Host "`n1. تسجيل دخول المدير..." -ForegroundColor Yellow
try {
    $loginData = @{
        username = "admin123"
        password = "admin123"
    } | ConvertTo-Json

    $loginResponse = Invoke-WebRequest -Uri "$baseUrl/auth/admin/login" -Method POST -ContentType "application/json" -Body $loginData
    $loginResult = $loginResponse.Content | ConvertFrom-Json
    
    if ($loginResult.success) {
        $token = $loginResult.data.token
        Write-Host "✅ تم تسجيل الدخول بنجاح" -ForegroundColor Green
    } else {
        Write-Host "❌ فشل تسجيل الدخول: $($loginResult.message)" -ForegroundColor Red
        exit
    }
} catch {
    Write-Host "❌ خطأ في تسجيل الدخول: $($_.Exception.Message)" -ForegroundColor Red
    exit
}

# إنشاء ملف تجريبي
Write-Host "`n2. إنشاء ملف تجريبي..." -ForegroundColor Yellow
$testContent = @"
هذا ملف تجريبي لاختبار رفع المرفقات مع الإشعارات.

محتويات الملف:
- السطر الأول: نص تجريبي
- السطر الثاني: المزيد من النص التجريبي  
- السطر الثالث: نهاية الملف التجريبي

تاريخ الإنشاء: $(Get-Date)
"@

$testFile = "test_attachment.txt"
$testContent | Out-File -FilePath $testFile -Encoding UTF8
Write-Host "✅ تم إنشاء الملف: $testFile" -ForegroundColor Green

# اختبار 1: إرسال إشعار مع مسارات مباشرة
Write-Host "`n3. اختبار إرسال إشعار مع مسارات مباشرة..." -ForegroundColor Yellow
try {
    $notificationData = @{
        title = "إشعار تجريبي مع مرفقات"
        message = "هذا إشعار تجريبي يحتوي على مرفقات من PowerShell"
        sender_id = 1
        sender_name = "PowerShell Test"
        sender_type = "admin"
        recipient_type = "students"
        recipient_ids = @(1, 2, 3)
        priority = "high"
        attachment_paths = @("notifications/test_document.pdf", "notifications/sample_file.docx")
        attachment_names = @("مستند تجريبي.pdf", "ملف عينة.docx")
    } | ConvertTo-Json -Depth 3

    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }

    $response = Invoke-WebRequest -Uri "$baseUrl/notifications" -Method POST -Headers $headers -Body $notificationData
    $result = $response.Content | ConvertFrom-Json
    
    if ($result.success) {
        Write-Host "✅ تم إرسال الإشعار بنجاح (ID: $($result.data.id))" -ForegroundColor Green
        Write-Host "   المرفقات: $($result.data.attachment_path -join ', ')" -ForegroundColor Gray
    } else {
        Write-Host "❌ فشل إرسال الإشعار: $($result.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ خطأ في إرسال الإشعار: $($_.Exception.Message)" -ForegroundColor Red
}

# اختبار 2: إرسال إشعار مع رفع ملف (محاكاة multipart)
Write-Host "`n4. اختبار إرسال إشعار مع رفع ملف..." -ForegroundColor Yellow
try {
    # إنشاء multipart form data
    $boundary = [System.Guid]::NewGuid().ToString()
    $LF = "`r`n"
    
    $bodyLines = @(
        "--$boundary",
        "Content-Disposition: form-data; name=`"title`"$LF",
        "إشعار مع ملف مرفوع",
        "--$boundary",
        "Content-Disposition: form-data; name=`"message`"$LF", 
        "هذا إشعار يحتوي على ملف تم رفعه من PowerShell",
        "--$boundary",
        "Content-Disposition: form-data; name=`"sender_id`"$LF",
        "1",
        "--$boundary",
        "Content-Disposition: form-data; name=`"sender_name`"$LF",
        "PowerShell File Upload Test",
        "--$boundary",
        "Content-Disposition: form-data; name=`"sender_type`"$LF",
        "admin",
        "--$boundary",
        "Content-Disposition: form-data; name=`"recipient_type`"$LF",
        "students",
        "--$boundary",
        "Content-Disposition: form-data; name=`"recipient_ids[0]`"$LF",
        "1",
        "--$boundary",
        "Content-Disposition: form-data; name=`"recipient_ids[1]`"$LF",
        "2",
        "--$boundary",
        "Content-Disposition: form-data; name=`"priority`"$LF",
        "medium",
        "--$boundary",
        "Content-Disposition: form-data; name=`"attachments[]`"; filename=`"$testFile`"",
        "Content-Type: text/plain$LF",
        $testContent,
        "--$boundary--"
    )
    
    $body = $bodyLines -join $LF
    
    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "multipart/form-data; boundary=$boundary"
    }

    $response = Invoke-WebRequest -Uri "$baseUrl/notifications" -Method POST -Headers $headers -Body $body
    $result = $response.Content | ConvertFrom-Json
    
    if ($result.success) {
        Write-Host "✅ تم رفع الملف وإرسال الإشعار بنجاح (ID: $($result.data.id))" -ForegroundColor Green
        Write-Host "   المرفقات: $($result.data.attachment_path -join ', ')" -ForegroundColor Gray
    } else {
        Write-Host "❌ فشل رفع الملف: $($result.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ خطأ في رفع الملف: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "   التفاصيل: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
}

# اختبار 3: التحقق من الإشعارات المرسلة
Write-Host "`n5. التحقق من الإشعارات المرسلة..." -ForegroundColor Yellow
try {
    $headers = @{
        "Authorization" = "Bearer $token"
    }

    $response = Invoke-WebRequest -Uri "$baseUrl/notifications" -Method GET -Headers $headers
    $result = $response.Content | ConvertFrom-Json
    
    if ($result.success) {
        $notificationsWithAttachments = $result.data | Where-Object { $_.attachment_path -ne $null }
        Write-Host "✅ تم العثور على $($notificationsWithAttachments.Count) إشعارات مع مرفقات" -ForegroundColor Green
        
        foreach ($notification in $notificationsWithAttachments | Select-Object -First 3) {
            Write-Host "   - ID: $($notification.id), العنوان: $($notification.title)" -ForegroundColor Gray
            if ($notification.attachment_path) {
                Write-Host "     المرفقات: $($notification.attachment_path)" -ForegroundColor Gray
            }
        }
    } else {
        Write-Host "❌ فشل في جلب الإشعارات: $($result.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ خطأ في جلب الإشعارات: $($_.Exception.Message)" -ForegroundColor Red
}

# تنظيف الملفات التجريبية
Write-Host "`n6. تنظيف الملفات التجريبية..." -ForegroundColor Yellow
if (Test-Path $testFile) {
    Remove-Item $testFile
    Write-Host "✅ تم حذف الملف التجريبي" -ForegroundColor Green
}

Write-Host "`n=== انتهى الاختبار ===" -ForegroundColor Green
Write-Host "✅ تم اختبار جميع طرق إرسال المرفقات مع الإشعارات!" -ForegroundColor Green
