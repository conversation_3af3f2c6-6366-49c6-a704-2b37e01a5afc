<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Http\Response;
use Carbon\Carbon;
use Symfony\Component\Process\Process;
use Symfony\Component\Process\Exception\ProcessFailedException;

class BackupController extends Controller
{
    /**
     * Backup directory path
     */
    protected $backupPath;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->backupPath = storage_path('app/backups');
        
        // Create backups directory if it doesn't exist
        if (!File::exists($this->backupPath)) {
            File::makeDirectory($this->backupPath, 0755, true);
        }
    }

    /**
     * Display a listing of database backups.
     */
    public function index()
    {
        $backupFiles = collect(File::files($this->backupPath))->filter(function ($file) {
            return in_array($file->getExtension(), ['sql', 'gz', 'zip']);
        })->map(function ($file) {
            return [
                'name' => $file->getFilename(),
                'size' => $this->formatFileSize($file->getSize()),
                'modified' => date('Y-m-d H:i:s', $file->getMTime()),
            ];
        })->sortByDesc('modified');

        return view('backups.index', compact('backupFiles'));
    }

    /**
     * Create a new database backup.
     */
    public function create(Request $request)
    {
        try {
            // Get database config
            $host = config('database.connections.mysql.host');
            $port = config('database.connections.mysql.port');
            $database = config('database.connections.mysql.database');
            $username = config('database.connections.mysql.username');
            $password = config('database.connections.mysql.password');
            
            // Set filename with timestamp
            $filename = "backup-" . Carbon::now()->format('Y-m-d_H-i-s') . ".sql";
            $filepath = $this->backupPath . '/' . $filename;
            
            // Build mysqldump command
            $command = "mysqldump -h {$host} -P {$port} -u {$username}";
            
            if ($password) {
                $command .= " -p'{$password}'";
            }
            
            $command .= " {$database} > {$filepath}";
            
            // Execute backup command
            $process = Process::fromShellCommandline($command);
            $process->setTimeout(300); // 5 minutes
            $process->run();
            
            // Check if process was successful
            if (!$process->isSuccessful()) {
                throw new ProcessFailedException($process);
            }
            
            Log::info('Database backup created successfully: ' . $filename);
            return redirect()->route('backups.index')->with('success', 'Database backup created successfully.');
            
        } catch (\Exception $e) {
            Log::error('Error creating database backup: ' . $e->getMessage());
            return redirect()->route('backups.index')->with('error', 'Error creating database backup: ' . $e->getMessage());
        }
    }

    /**
     * Download a database backup file.
     */
    public function download(string $filename)
    {
        $filePath = $this->backupPath . '/' . $filename;
        
        if (!File::exists($filePath)) {
            return redirect()->route('backups.index')->with('error', 'Backup file not found.');
        }
        
        return response()->download($filePath);
    }

    /**
     * Delete a database backup file.
     */
    public function destroy(string $filename)
    {
        $filePath = $this->backupPath . '/' . $filename;
        
        if (!File::exists($filePath)) {
            return redirect()->route('backups.index')->with('error', 'Backup file not found.');
        }
        
        try {
            File::delete($filePath);
            return redirect()->route('backups.index')->with('success', "Backup file '{$filename}' has been deleted successfully.");
        } catch (\Exception $e) {
            return redirect()->route('backups.index')->with('error', "Failed to delete backup file: {$e->getMessage()}");
        }
    }

    /**
     * Format file size to readable format
     */
    protected function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
