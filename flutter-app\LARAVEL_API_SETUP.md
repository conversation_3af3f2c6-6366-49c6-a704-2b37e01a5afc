# إعداد Laravel API - Laravel API Setup

## ✅ **تم تحديث Flutter App للاتصال بـ Laravel API!**

### 🔧 **الإعدادات الحالية:**

#### عناوين API المحدثة:
```dart
// في lib/utils/constants.dart
static const String baseUrl = 'http://localhost/appnote-api/public/api';

// Authentication endpoints (متطابقة مع Laravel routes)
static const String adminLogin = '$baseUrl/auth/admin/login';
static const String studentLogin = '$baseUrl/auth/student/login';
static const String employeeLogin = '$baseUrl/auth/employee/login';
```

### 🚀 **خطوات تشغيل Laravel API:**

#### 1. تشغيل Laragon:
```bash
# تأكد من تشغيل Laragon
# افتح Laragon Control Panel
# اضغط "Start All"
```

#### 2. التحقق من Laravel API:
```bash
# افتح المتصفح واذهب إلى:
http://localhost/appnote-api/public/api/health

# يجب أن ترى:
{
  "success": true,
  "message": "AppNote API is running",
  "timestamp": "2024-...",
  "version": "1.0.0"
}
```

#### 3. اختبار قاعدة البيانات:
```bash
# اذهب إلى:
http://localhost/appnote-api/public/api/test-db

# يجب أن ترى:
{
  "success": true,
  "message": "Database connection successful",
  "database": "appnote_db",
  "host": "127.0.0.1"
}
```

### 🔐 **إعداد تسجيل دخول الأدمن:**

#### 1. إنشاء أدمن في قاعدة البيانات:
```sql
-- في phpMyAdmin أو MySQL
INSERT INTO users (name, email, password, role, created_at, updated_at) 
VALUES (
  'مدير النظام',
  '<EMAIL>',
  '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: password
  'admin',
  NOW(),
  NOW()
);
```

#### 2. أو استخدام Laravel Seeder:
```php
// في database/seeders/AdminSeeder.php
use App\Models\User;
use Illuminate\Support\Facades\Hash;

User::create([
    'name' => 'مدير النظام',
    'email' => '<EMAIL>',
    'password' => Hash::make('admin123'),
    'role' => 'admin'
]);
```

```bash
# تشغيل الـ seeder
php artisan db:seed --class=AdminSeeder
```

### 📱 **اختبار الاتصال من Flutter:**

#### 1. في التطبيق:
- افتح التطبيق
- اضغط "Admin Login"
- اضغط "اختبار الاتصال بالخادم"
- يجب أن ترى: "✅ الاتصال بالخادم وقاعدة البيانات ناجح!"

#### 2. تسجيل الدخول:
```
البريد: <EMAIL>
كلمة المرور: admin123 (أو password حسب ما أنشأت)
```

### 🛠️ **استكشاف الأخطاء:**

#### إذا فشل الاتصال:
1. **تحقق من Laragon:**
   - تأكد من تشغيل Apache و MySQL
   - تحقق من أن المشروع في `C:\laragon\www\appnote-api`

2. **تحقق من Laravel:**
   ```bash
   # في مجلد المشروع
   cd C:\laragon\www\appnote-api
   php artisan config:clear
   php artisan cache:clear
   ```

3. **تحقق من قاعدة البيانات:**
   - افتح phpMyAdmin
   - تأكد من وجود قاعدة البيانات `appnote_db`
   - تأكد من وجود جدول `users`

#### إذا كان العنوان مختلف:
```dart
// في lib/utils/constants.dart
// للاستخدام مع Virtual Host
static const String baseUrl = 'http://appnote-api.test/api';

// أو للاستخدام مع Port مختلف
static const String baseUrl = 'http://localhost:8080/appnote-api/public/api';
```

### 📊 **Laravel Routes المتاحة:**

```php
// Authentication
POST /api/auth/admin/login
POST /api/auth/student/login  
POST /api/auth/employee/login
POST /api/auth/logout

// Health Check
GET /api/health
GET /api/test-db
GET /api/docs

// Students (requires auth)
GET /api/students
POST /api/students
GET /api/students/{id}
PUT /api/students/{id}
DELETE /api/students/{id}

// Employees (requires auth)
GET /api/employees
POST /api/employees
GET /api/employees/{id}
PUT /api/employees/{id}
DELETE /api/employees/{id}

// Notifications (requires auth)
GET /api/notifications
POST /api/notifications
POST /api/notifications/send
```

### 🎯 **الخطوات التالية:**

1. **تشغيل Laravel API** ✅
2. **إنشاء أدمن في قاعدة البيانات** ⏳
3. **اختبار تسجيل الدخول** ⏳
4. **تطوير Dashboard** ⏳

### 📞 **للمساعدة:**

إذا واجهت أي مشاكل:
1. تحقق من logs Laravel في `storage/logs/laravel.log`
2. تحقق من Flutter console للأخطاء
3. استخدم زر "اختبار الاتصال" في التطبيق

🚀 **بمجرد تشغيل Laravel API، ستتمكن من تسجيل الدخول بالبيانات الحقيقية!**
