<?php
// This script updates the AuthController.php file to redirect students to their dashboard

$authController = file_get_contents(__DIR__ . '/app/Http/Controllers/AuthController.php');

// Replace 'dashboard' route with 'student.dashboard' for student logins
$authController = str_replace(
    "Auth::guard('student')->login(\$student, \$request->filled('remember'));
                \$request->session()->regenerate();
                return redirect()->route('dashboard');",
    "Auth::guard('student')->login(\$student, \$request->filled('remember'));
                \$request->session()->regenerate();
                return redirect()->route('student.dashboard');",
    $authController
);

// Write the updated content back to the file
file_put_contents(__DIR__ . '/app/Http/Controllers/AuthController.php', $authController);

echo "AuthController.php updated successfully!\n";
