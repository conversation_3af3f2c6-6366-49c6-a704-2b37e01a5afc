<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => Hash::make('admin12345'),
            'remember_token' => \Illuminate\Support\Str::random(10),
        ]);

        $this->command->info('Admin user created with email: <EMAIL> and password: admin12345');
    }
}
