<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\PushNotificationService;
use App\Models\PushSubscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class PushNotificationController extends Controller
{
    private $pushService;

    public function __construct(PushNotificationService $pushService)
    {
        $this->pushService = $pushService;
    }

    /**
     * Subscribe to push notifications.
     */
    public function subscribe(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'subscription' => 'required|array',
                'subscription.endpoint' => 'required|string',
                'subscription.keys' => 'required|array',
                'subscription.keys.p256dh' => 'required|string',
                'subscription.keys.auth' => 'required|string',
                'user_type' => 'required|in:student,employee,admin',
                'user_id' => 'required|integer',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $subscription = $this->pushService->subscribe(
                $request->user_id,
                $request->user_type,
                $request->subscription,
                $request->header('User-Agent'),
                $request->ip()
            );

            return response()->json([
                'success' => true,
                'message' => 'Successfully subscribed to push notifications',
                'subscription_id' => $subscription->id
            ]);

        } catch (\Exception $e) {
            Log::error('Push subscription failed: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to subscribe to push notifications',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Unsubscribe from push notifications.
     */
    public function unsubscribe(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'user_type' => 'required|in:student,employee,admin',
                'user_id' => 'required|integer',
                'endpoint' => 'nullable|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $this->pushService->unsubscribe(
                $request->user_id,
                $request->user_type,
                $request->endpoint
            );

            return response()->json([
                'success' => true,
                'message' => 'Successfully unsubscribed from push notifications'
            ]);

        } catch (\Exception $e) {
            Log::error('Push unsubscription failed: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to unsubscribe from push notifications',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send test push notification.
     */
    public function sendTest(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'user_type' => 'required|in:student,employee,admin',
                'user_id' => 'required|integer',
                'title' => 'required|string|max:255',
                'body' => 'required|string|max:500',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $result = $this->pushService->sendToUsers(
                [$request->user_id],
                $request->user_type,
                $request->title,
                $request->body,
                ['test' => true, 'timestamp' => now()->toISOString()]
            );

            return response()->json([
                'success' => true,
                'message' => 'Test notification sent',
                'sent' => $result['sent'],
                'errors' => $result['errors']
            ]);

        } catch (\Exception $e) {
            Log::error('Test push notification failed: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to send test notification',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send push notification to multiple users.
     */
    public function sendToUsers(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'user_ids' => 'required|array',
                'user_ids.*' => 'integer',
                'user_type' => 'required|in:student,employee,admin',
                'title' => 'required|string|max:255',
                'body' => 'required|string|max:500',
                'data' => 'nullable|array',
                'icon' => 'nullable|string',
                'badge' => 'nullable|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $result = $this->pushService->sendToUsers(
                $request->user_ids,
                $request->user_type,
                $request->title,
                $request->body,
                $request->data ?? [],
                $request->icon,
                $request->badge
            );

            return response()->json([
                'success' => true,
                'message' => 'Push notifications sent',
                'sent' => $result['sent'],
                'errors' => $result['errors']
            ]);

        } catch (\Exception $e) {
            Log::error('Push notification sending failed: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to send push notifications',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send push notification for a specific notification ID.
     */
    public function sendForNotification(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'notification_id' => 'required|integer|exists:notifications,id',
                'title' => 'required|string|max:255',
                'body' => 'required|string|max:500',
                'data' => 'nullable|array',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $result = $this->pushService->sendToNotificationRecipients(
                $request->notification_id,
                $request->title,
                $request->body,
                array_merge($request->data ?? [], [
                    'notification_id' => $request->notification_id,
                    'action_url' => '/notifications/' . $request->notification_id
                ])
            );

            return response()->json([
                'success' => true,
                'message' => 'Push notifications sent for notification',
                'sent' => $result['sent'],
                'errors' => $result['errors']
            ]);

        } catch (\Exception $e) {
            Log::error('Push notification for notification failed: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to send push notifications',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get push notification statistics.
     */
    public function getStats()
    {
        try {
            $stats = $this->pushService->getStats();

            return response()->json([
                'success' => true,
                'stats' => $stats
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get push stats: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to get statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get user's push subscriptions.
     */
    public function getUserSubscriptions(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'user_type' => 'required|in:student,employee,admin',
                'user_id' => 'required|integer',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $subscriptions = PushSubscription::where('user_id', $request->user_id)
                ->where('user_type', $request->user_type)
                ->active()
                ->get(['id', 'endpoint', 'created_at', 'last_used_at']);

            return response()->json([
                'success' => true,
                'subscriptions' => $subscriptions
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get user subscriptions: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to get subscriptions',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get VAPID public key for client-side subscription.
     */
    public function getVapidPublicKey()
    {
        try {
            $publicKey = config('webpush.vapid.public_key');
            
            if (!$publicKey) {
                return response()->json([
                    'success' => false,
                    'message' => 'VAPID public key not configured'
                ], 500);
            }

            return response()->json([
                'success' => true,
                'public_key' => $publicKey
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get VAPID public key: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to get VAPID public key',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
