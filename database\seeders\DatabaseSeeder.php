<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create admin user
        \App\Models\User::create([
            'name' => 'admin',
            'email' => '<EMAIL>',
            'password' => \Hash::make('admin123'),
        ]);

        // Create test students
        \App\Models\Student::create([
            'full_name' => 'أحمد محمد علي',
            'mobile_number' => '70654321',
            'class_name' => 'الصف الأول',
            'nationality' => 'لبناني',
            'parent_mobile' => '71123456',
            'password' => \Hash::make('stu123'),
            'is_active' => true,
        ]);

        \App\Models\Student::create([
            'full_name' => 'فاطمة حسن أحمد',
            'mobile_number' => '71987654',
            'class_name' => 'الصف الثاني',
            'nationality' => 'لبناني',
            'parent_mobile' => '70555666',
            'password' => \Hash::make('stu123'),
            'is_active' => true,
        ]);

        // Create test employees
        \App\Models\Employee::create([
            'full_name' => 'محمد أحمد الخطيب',
            'contract_type' => 'دوام كامل',
            'mobile_number' => '81234567',
            'employee_type' => 'مدرس',
            'job_status' => 'نشط',
            'password' => \Hash::make('emp123'),
        ]);

        \App\Models\Employee::create([
            'full_name' => 'سارة علي حسن',
            'contract_type' => 'دوام جزئي',
            'mobile_number' => '76543210',
            'employee_type' => 'إداري',
            'job_status' => 'نشط',
            'password' => \Hash::make('emp123'),
        ]);

        // Call additional seeders
        $this->call([
            NotificationSeeder::class,
        ]);

        echo "✅ Database seeded successfully!\n";
        echo "👨‍💼 Admin: admin / admin123\n";
        echo "👨‍🎓 Student: 70654321 / stu123\n";
        echo "👨‍💼 Employee: 81234567 / emp123\n";
    }
}
