<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Employee;
use Illuminate\Support\Facades\DB;

echo "=== Fix All Employee Data ===\n";

// Set database charset
DB::statement("SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci");

// Count current employees
$count = Employee::count();
echo "Current employees with corrupted data: {$count}\n";

if ($count > 0) {
    echo "Deleting all corrupted employee data...\n";
    Employee::truncate();
    echo "✅ Deleted all corrupted employee records.\n";
}

echo "\nCreating comprehensive employee data with correct Arabic encoding...\n";

$employees = [
    // إداريين
    ['أحمد محمد علي السيد', 'عقد دائم', 'مدير إداري', '71234567', 'نشط', 'emp001'],
    ['فاطمة حسن أحمد الزهراء', 'عقد دائم', 'سكرتيرة', '71234568', 'نشط', 'emp002'],
    ['محمد علي حسن الأمين', 'عقد مؤقت', 'محاسب', '71234569', 'نشط', 'emp003'],
    ['سارة أحمد محمد النور', 'عقد دائم', 'موظف إداري', '71234570', 'نشط', 'emp004'],
    ['علي محمود حسن الكريم', 'شراء خدمات', 'مساعد إداري', '71234571', 'نشط', 'emp005'],
    
    // أساتذة
    ['نور الدين أحمد الفاضل', 'عقد دائم', 'أستاذ تعليم فني', '71234572', 'نشط', 'emp006'],
    ['ليلى محمد حسن الجميلة', 'عقد مؤقت', 'أستاذ تعليم عام', '71234573', 'نشط', 'emp007'],
    ['خالد عبد الله الصادق', 'عقد دائم', 'أستاذ تخصص', '71234574', 'نشط', 'emp008'],
    ['زينب أحمد محمد الطاهرة', 'عقد مؤقت', 'أستاذ مساعد', '71234575', 'نشط', 'emp009'],
    ['حسن علي حسن الشريف', 'عقد دائم', 'رئيس قسم', '71234576', 'نشط', 'emp010'],
    
    // فنيين
    ['مريم محمد علي الزكية', 'شراء خدمات', 'فني مختبر', '71234577', 'نشط', 'emp011'],
    ['يوسف أحمد حسن الصبور', 'عقد مؤقت', 'فني كهرباء', '71234578', 'نشط', 'emp012'],
    ['فاديا علي محمد الحكيمة', 'عقد دائم', 'فني حاسوب', '71234579', 'نشط', 'emp013'],
    ['عمر محمد أحمد الرشيد', 'عقد مؤقت', 'فني ميكانيك', '71234580', 'نشط', 'emp014'],
    ['رنا حسن علي الجميلة', 'شراء خدمات', 'فني مكتبة', '71234581', 'نشط', 'emp015'],
    
    // خدمات
    ['جمال عبد الله محمد', 'شراء خدمات', 'حارس', '71234582', 'نشط', 'emp016'],
    ['سعاد أحمد حسن', 'شراء خدمات', 'عامل نظافة', '71234583', 'نشط', 'emp017'],
    ['كريم محمد علي', 'عقد مؤقت', 'سائق', '71234584', 'نشط', 'emp018'],
    ['نادية حسن أحمد', 'شراء خدمات', 'طباخة', '71234585', 'نشط', 'emp019'],
    ['وليد علي محمد', 'عقد مؤقت', 'عامل صيانة', '71234586', 'نشط', 'emp020'],
];

$created = 0;
foreach ($employees as $index => $employeeData) {
    try {
        Employee::create([
            'full_name' => $employeeData[0],
            'contract_type' => $employeeData[1],
            'employee_type' => $employeeData[2],
            'phone' => $employeeData[3],
            'job_status' => $employeeData[4],
            'username' => $employeeData[5],
            'password' => bcrypt('emp123'),
            'automatic_number' => str_pad($index + 1, 3, '0', STR_PAD_LEFT),
            'financial_number' => '10' . str_pad($index + 1, 3, '0', STR_PAD_LEFT),
            'state_cooperative_number' => '20' . str_pad($index + 1, 3, '0', STR_PAD_LEFT),
            'bank_account_number' => '30' . str_pad($index + 1, 3, '0', STR_PAD_LEFT),
        ]);
        echo "✅ Created: {$employeeData[0]}\n";
        $created++;
    } catch (\Exception $e) {
        echo "❌ Failed to create {$employeeData[0]}: " . $e->getMessage() . "\n";
    }
}

echo "\n=== Verification ===\n";
$newCount = Employee::count();
echo "Total employees created: {$newCount}\n";

if ($newCount > 0) {
    $sample = Employee::first();
    echo "\nSample employee data:\n";
    echo "Full Name: {$sample->full_name}\n";
    echo "Contract Type: {$sample->contract_type}\n";
    echo "Employee Type: {$sample->employee_type}\n";
    echo "Phone: {$sample->phone}\n";
    
    // Check if still contains question marks
    if (strpos($sample->full_name, '?') !== false) {
        echo "❌ Still contains question marks!\n";
    } else {
        echo "✅ Arabic text is displaying correctly!\n";
    }
}

echo "\n=== Fix Complete ===\n";
echo "Created {$created} employees with correct Arabic encoding.\n";
echo "Now test the web interface to see if the display is fixed.\n";
