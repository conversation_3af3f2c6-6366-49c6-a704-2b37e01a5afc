import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/employee.dart';
import '../models/notification.dart';
import '../utils/constants.dart';

class EmployeeDashboardService {
  static const String _baseUrl = ApiConstants.baseUrl;

  /// Get stored JWT token
  static Future<String?> _getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('user_token');
  }

  /// Store JWT token
  static Future<void> _storeToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('user_token', token);
  }

  /// Remove JWT token
  static Future<void> _removeToken() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('user_token');
  }

  /// Get authorization headers
  static Future<Map<String, String>> _getHeaders() async {
    final token = await _getToken();
    final prefs = await SharedPreferences.getInstance();
    final userData = prefs.getString(StorageKeys.userData);

    String? employeeUsername;
    String? employeeId;

    if (userData != null) {
      try {
        final userJson = jsonDecode(userData);
        employeeUsername = userJson['username'];
        employeeId = userJson['id']?.toString();
      } catch (e) {
        if (kDebugMode) {
          print('❌ Error parsing user data: $e');
        }
      }
    }

    if (kDebugMode) {
      print('🔑 Getting headers for employee dashboard:');
      print('   Token: ${token != null ? "${token.substring(0, 20)}..." : "null"}');
      print('   Employee Username: $employeeUsername');
      print('   Employee ID: $employeeId');
    }

    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
      if (employeeUsername != null) 'X-Employee-Username': employeeUsername,
      if (employeeId != null) 'X-Employee-ID': employeeId,
    };
  }

  /// Employee login
  static Future<Map<String, dynamic>> login(String identifier, String password) async {
    try {
      if (kDebugMode) {
        print('🔐 Employee login attempt for: $identifier');
      }

      final response = await http.post(
        Uri.parse('$_baseUrl/auth/employee/login'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode({
          'identifier': identifier,
          'password': password,
        }),
      );

      if (kDebugMode) {
        print('🔐 Employee login response: ${response.statusCode}');
        print('🔐 Employee login body: ${response.body}');
      }

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        // Store token
        await _storeToken(data['token']);
        
        return {
          'success': true,
          'employee': Employee.fromJson(data['employee']),
          'token': data['token'],
          'message': data['message'] ?? 'تم تسجيل الدخول بنجاح',
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'فشل في تسجيل الدخول',
        };
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Employee login error: $e');
      }
      return {
        'success': false,
        'message': 'خطأ في الاتصال بالخادم: $e',
      };
    }
  }

  /// Employee logout
  static Future<Map<String, dynamic>> logout() async {
    try {
      final headers = await _getHeaders();
      
      final response = await http.post(
        Uri.parse('$_baseUrl/auth/logout'),
        headers: headers,
      );

      // Remove token regardless of response
      await _removeToken();

      if (response.statusCode == 200) {
        return {
          'success': true,
          'message': 'تم تسجيل الخروج بنجاح',
        };
      } else {
        return {
          'success': true, // Still success since token is removed
          'message': 'تم تسجيل الخروج محلياً',
        };
      }
    } catch (e) {
      // Remove token even if request fails
      await _removeToken();
      return {
        'success': true,
        'message': 'تم تسجيل الخروج محلياً',
      };
    }
  }

  /// Test authentication
  static Future<Map<String, dynamic>> testAuth() async {
    try {
      final headers = await _getHeaders();
      
      final response = await http.get(
        Uri.parse('$_baseUrl/employee/test-auth'),
        headers: headers,
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return {
          'success': true,
          'employee': data['employee'],
          'message': data['message'],
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'فشل في التحقق من الهوية',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في الاتصال بالخادم: $e',
      };
    }
  }

  /// Get employee dashboard data
  static Future<Map<String, dynamic>> getDashboardData() async {
    try {
      if (kDebugMode) {
        print('📱 Fetching employee dashboard data...');
      }

      final headers = await _getHeaders();

      if (kDebugMode) {
        print('📱 Request headers: $headers');
        print('📱 Request URL: $_baseUrl/employee/dashboard');
      }

      final response = await http.get(
        Uri.parse('$_baseUrl/employee/dashboard'),
        headers: headers,
      );

      if (kDebugMode) {
        print('📱 Employee dashboard response: ${response.statusCode}');
        print('📱 Employee dashboard body: ${response.body}');
      }

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (kDebugMode) {
          print('📱 Parsed data: $data');
        }
        if (data['success'] == true) {
          if (kDebugMode) {
            print('📱 Dashboard data: ${data['data']}');
          }
          return {
            'success': true,
            'data': data['data'],
            'message': data['message'],
          };
        } else {
          throw Exception(data['message'] ?? 'فشل في تحميل البيانات');
        }
      } else if (response.statusCode == 401) {
        throw Exception('غير مصرح لك بالوصول. يرجى تسجيل الدخول مرة أخرى.');
      } else {
        throw Exception('خطأ في الخادم: ${response.statusCode}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error fetching employee dashboard data: $e');
      }
      throw Exception('فشل في تحميل بيانات لوحة التحكم: $e');
    }
  }

  /// Get employee notifications
  static Future<Map<String, dynamic>> getNotifications({
    int page = 1,
    int perPage = 20,
    String? status,
  }) async {
    try {
      if (kDebugMode) {
        print('📱 Fetching employee notifications...');
      }

      final headers = await _getHeaders();

      String url = '${ApiConstants.baseUrl}/employee/notifications?page=$page&per_page=$perPage';
      if (status != null) {
        url += '&status=$status';
      }

      if (kDebugMode) {
        print('📱 Request headers: $headers');
        print('📱 Request URL: $url');
      }

      final response = await http.get(
        Uri.parse(url),
        headers: headers,
      );

      if (kDebugMode) {
        print('📱 Employee notifications response: ${response.statusCode}');
        print('📱 Employee notifications body: ${response.body}');
      }

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (kDebugMode) {
          print('📱 Parsed data: $data');
        }
        if (data['success'] == true) {
          if (kDebugMode) {
            print('📱 Raw notifications data: ${data['data']}');
          }
          final notifications = (data['data'] as List)
              .map((item) {
                if (kDebugMode) {
                  print('📱 Processing notification item: $item');
                }
                return NotificationModel.fromJson(item);
              })
              .toList();
          
          return {
            'success': true,
            'notifications': notifications,
            'pagination': data['pagination'],
            'message': data['message'],
          };
        } else {
          throw Exception(data['message'] ?? 'فشل في تحميل الإشعارات');
        }
      } else if (response.statusCode == 401) {
        throw Exception('غير مصرح لك بالوصول. يرجى تسجيل الدخول مرة أخرى.');
      } else {
        throw Exception('خطأ في الخادم: ${response.statusCode}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error fetching employee notifications: $e');
      }
      throw Exception('فشل في تحميل الإشعارات: $e');
    }
  }

  /// Mark notification as read
  static Future<Map<String, dynamic>> markNotificationAsRead(int notificationId) async {
    try {
      final headers = await _getHeaders();

      final response = await http.post(
        Uri.parse('${ApiConstants.baseUrl}/employee/notifications/$notificationId/mark-read'),
        headers: headers,
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return {
          'success': true,
          'message': data['message'],
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'فشل في تحديث حالة الإشعار',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في الاتصال بالخادم: $e',
      };
    }
  }

  /// Mark notification as unread
  static Future<Map<String, dynamic>> markNotificationAsUnread(int notificationId) async {
    try {
      final headers = await _getHeaders();

      final response = await http.post(
        Uri.parse('${ApiConstants.baseUrl}/employee/notifications/$notificationId/mark-unread'),
        headers: headers,
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200 && data['success'] == true) {
        return {
          'success': true,
          'message': data['message'],
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'فشل في تحديث حالة الإشعار',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'خطأ في الاتصال بالخادم: $e',
      };
    }
  }

  /// Check if employee is logged in
  static Future<bool> isLoggedIn() async {
    final token = await _getToken();
    return token != null;
  }
}
