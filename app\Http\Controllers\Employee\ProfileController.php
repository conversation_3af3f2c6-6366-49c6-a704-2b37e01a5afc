<?php

namespace App\Http\Controllers\Employee;

use App\Http\Controllers\Controller;
use App\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class ProfileController extends Controller
{
    /**
     * Show the form for editing the employee's profile.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function edit()
    {
        $employee = Auth::guard('employee')->user();
        
        // Define dropdown options for select fields
        $employeeTypes = [
            'غير موظف' => 'غير موظف',
            'موظف دائم' => 'موظف دائم',
            'موظف مؤقت' => 'موظف مؤقت',
            'متعاقد' => 'متعاقد',
            'متدرب' => 'متدرب'
        ];
        
        $contractTypes = [
            'تعاقد عادي' => 'تعاقد عادي',
            'عقد مفتوح' => 'عقد مفتوح',
            'عقد محدد المدة' => 'عقد محدد المدة',
            'عقد موسمي' => 'عقد موسمي',
            'عقد بالساعة' => 'عقد بالساعة'
        ];
        
        $jobStatuses = [
            'غير موظف في القطاع العام' => 'غير موظف في القطاع العام',
            'موظف في القطاع العام' => 'موظف في القطاع العام',
            'موظف في القطاع الخاص' => 'موظف في القطاع الخاص',
            'متقاعد' => 'متقاعد',
            'طالب' => 'طالب'
        ];
        
        return view('employee.profile.edit', compact('employee', 'employeeTypes', 'contractTypes', 'jobStatuses'));
    }

    /**
     * Update the employee's profile.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request)
    {
        $employee = Auth::guard('employee')->user();
        
        $validator = Validator::make($request->all(), [
            'full_name' => 'required|string|max:255',
            'phone' => 'nullable|string|max:20',
            'employee_type' => 'required|string|max:50',
            'contract_type' => 'required|string|max:50',
            'job_status' => 'required|string|max:50',
            'financial_number' => 'nullable|string|max:50',
            'automatic_number' => 'nullable|string|max:50',
            'state_cooperative_number' => 'nullable|string|max:50',
            'bank_account_number' => 'nullable|string|max:50',
        ]);
        
        if ($validator->fails()) {
            return back()
                ->withErrors($validator)
                ->withInput();
        }
        
        $employee->full_name = $request->full_name;
        $employee->phone = $request->phone;
        $employee->employee_type = $request->employee_type;
        $employee->contract_type = $request->contract_type;
        $employee->job_status = $request->job_status;
        $employee->financial_number = $request->financial_number;
        $employee->automatic_number = $request->automatic_number;
        $employee->state_cooperative_number = $request->state_cooperative_number;
        $employee->bank_account_number = $request->bank_account_number;
        $employee->save();
        
        return back()->with('success', 'Profile updated successfully.');
    }

    /**
     * Show the change password form.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function showChangePasswordForm()
    {
        return view('employee.profile.change-password');
    }

    /**
     * Change the employee's password.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function changePassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'current_password' => 'required',
            'password' => 'required|string|min:6|confirmed',
        ]);
        
        if ($validator->fails()) {
            return back()
                ->withErrors($validator)
                ->withInput();
        }
        
        $employee = Auth::guard('employee')->user();
        
        // Check current password
        if (!Hash::check($request->current_password, $employee->password)) {
            return back()->withErrors(['current_password' => 'The current password is incorrect.']);
        }
        
        $employee->password = Hash::make($request->password);
        $employee->save();
        
        return back()->with('success', 'Password changed successfully.');
    }
}
