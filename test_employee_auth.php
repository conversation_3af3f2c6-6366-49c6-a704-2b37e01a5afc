<?php

echo "=== Testing Employee Authentication ===\n";

// Get all employees
$employees = \App\Models\Employee::select('id', 'username', 'full_name', 'phone')->get();

echo "Available employees:\n";
foreach ($employees as $emp) {
    echo "ID: {$emp->id}, Username: {$emp->username}, Name: {$emp->full_name}, Phone: {$emp->phone}\n";
}

echo "\n=== Testing Notifications for Each Employee ===\n";

foreach ($employees as $employee) {
    echo "\n--- Employee: {$employee->full_name} (ID: {$employee->id}) ---\n";
    
    // Get notifications for this employee
    $notifications = \App\Models\NotificationRecipient::where('recipient_id', $employee->id)
        ->where('recipient_type', 'employee')
        ->with('notification')
        ->count();
    
    $unread = \App\Models\NotificationRecipient::where('recipient_id', $employee->id)
        ->where('recipient_type', 'employee')
        ->whereNull('read_at')
        ->count();
    
    echo "Total notifications: $notifications\n";
    echo "Unread notifications: $unread\n";
    
    // Get recent notifications
    $recent = \App\Models\NotificationRecipient::where('recipient_id', $employee->id)
        ->where('recipient_type', 'employee')
        ->with('notification')
        ->orderBy('created_at', 'desc')
        ->limit(3)
        ->get();
    
    echo "Recent notifications:\n";
    foreach ($recent as $recipient) {
        $notification = $recipient->notification;
        $status = $recipient->read_at ? 'Read' : 'Unread';
        echo "  - {$notification->title} ({$status})\n";
    }
}

echo "\n=== Testing General Notifications ===\n";

// Get all notifications for employees
$allNotifications = \App\Models\Notification::where(function($q) {
    $q->where('target_audience', 'all')
      ->orWhere('target_audience', 'employees');
})->count();

echo "Total notifications for employees: $allNotifications\n";

// Get notification recipients
$totalRecipients = \App\Models\NotificationRecipient::where('recipient_type', 'employee')->count();
echo "Total notification recipients (employees): $totalRecipients\n";
