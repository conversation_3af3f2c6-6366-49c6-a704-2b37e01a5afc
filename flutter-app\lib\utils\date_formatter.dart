
class DateFormatter {
  // Arabic month names
  static const List<String> arabicMonths = [
    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
    'يوليو', 'أغسطس', 'سب<PERSON><PERSON><PERSON><PERSON>', 'أكتوبر', 'نوفمبر', 'ديسمبر'
  ];

  // Arabic day names
  static const List<String> arabicDays = [
    'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'
  ];

  /// Format date to Arabic relative time (منذ X دقائق/ساعات/أيام)
  static String formatRelativeTime(DateTime? dateTime) {
    if (dateTime == null) return '';

    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      if (difference.inDays == 1) {
        return 'منذ يوم واحد';
      } else if (difference.inDays < 7) {
        return 'منذ ${difference.inDays} أيام';
      } else if (difference.inDays < 30) {
        final weeks = (difference.inDays / 7).floor();
        return weeks == 1 ? 'منذ أسبوع واحد' : 'منذ $weeks أسابيع';
      } else if (difference.inDays < 365) {
        final months = (difference.inDays / 30).floor();
        return months == 1 ? 'منذ شهر واحد' : 'منذ $months أشهر';
      } else {
        final years = (difference.inDays / 365).floor();
        return years == 1 ? 'منذ سنة واحدة' : 'منذ $years سنوات';
      }
    } else if (difference.inHours > 0) {
      if (difference.inHours == 1) {
        return 'منذ ساعة واحدة';
      } else {
        return 'منذ ${difference.inHours} ساعات';
      }
    } else if (difference.inMinutes > 0) {
      if (difference.inMinutes == 1) {
        return 'منذ دقيقة واحدة';
      } else {
        return 'منذ ${difference.inMinutes} دقائق';
      }
    } else {
      return 'الآن';
    }
  }

  /// Format date to Arabic date string (الأحد، 15 يناير 2024)
  static String formatArabicDate(DateTime? dateTime) {
    if (dateTime == null) return '';

    final dayName = arabicDays[dateTime.weekday % 7];
    final monthName = arabicMonths[dateTime.month - 1];
    
    return '$dayName، ${dateTime.day} $monthName ${dateTime.year}';
  }

  /// Format date to Arabic date and time (الأحد، 15 يناير 2024 - 3:30 م)
  static String formatArabicDateTime(DateTime? dateTime) {
    if (dateTime == null) return '';

    final dateStr = formatArabicDate(dateTime);
    final timeStr = formatArabicTime(dateTime);
    
    return '$dateStr - $timeStr';
  }

  /// Format time to Arabic time (3:30 م)
  static String formatArabicTime(DateTime? dateTime) {
    if (dateTime == null) return '';

    final hour = dateTime.hour;
    final minute = dateTime.minute;
    
    String period;
    int displayHour;
    
    if (hour == 0) {
      displayHour = 12;
      period = 'ص';
    } else if (hour < 12) {
      displayHour = hour;
      period = 'ص';
    } else if (hour == 12) {
      displayHour = 12;
      period = 'م';
    } else {
      displayHour = hour - 12;
      period = 'م';
    }
    
    final minuteStr = minute.toString().padLeft(2, '0');
    return '$displayHour:$minuteStr $period';
  }

  /// Format date to short Arabic format (15/1/2024)
  static String formatShortDate(DateTime? dateTime) {
    if (dateTime == null) return '';
    
    return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
  }

  /// Format date to medium Arabic format (15 يناير 2024)
  static String formatMediumDate(DateTime? dateTime) {
    if (dateTime == null) return '';
    
    final monthName = arabicMonths[dateTime.month - 1];
    return '${dateTime.day} $monthName ${dateTime.year}';
  }

  /// Format date for notifications (today, yesterday, or date)
  static String formatNotificationDate(DateTime? dateTime) {
    if (dateTime == null) return '';

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final dateOnly = DateTime(dateTime.year, dateTime.month, dateTime.day);

    if (dateOnly == today) {
      return 'اليوم ${formatArabicTime(dateTime)}';
    } else if (dateOnly == yesterday) {
      return 'أمس ${formatArabicTime(dateTime)}';
    } else if (now.difference(dateTime).inDays < 7) {
      return formatRelativeTime(dateTime);
    } else {
      return formatMediumDate(dateTime);
    }
  }

  /// Parse ISO date string to DateTime
  static DateTime? parseISODate(String? dateString) {
    if (dateString == null || dateString.isEmpty) return null;
    
    try {
      return DateTime.parse(dateString);
    } catch (e) {
      return null;
    }
  }

  /// Format duration in Arabic (2 ساعات و 30 دقيقة)
  static String formatDuration(Duration? duration) {
    if (duration == null) return '';

    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    
    if (hours > 0 && minutes > 0) {
      return '$hours ساعات و $minutes دقيقة';
    } else if (hours > 0) {
      return hours == 1 ? 'ساعة واحدة' : '$hours ساعات';
    } else if (minutes > 0) {
      return minutes == 1 ? 'دقيقة واحدة' : '$minutes دقيقة';
    } else {
      return 'أقل من دقيقة';
    }
  }

  /// Check if date is today
  static bool isToday(DateTime? dateTime) {
    if (dateTime == null) return false;
    
    final now = DateTime.now();
    return dateTime.year == now.year &&
           dateTime.month == now.month &&
           dateTime.day == now.day;
  }

  /// Check if date is yesterday
  static bool isYesterday(DateTime? dateTime) {
    if (dateTime == null) return false;
    
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return dateTime.year == yesterday.year &&
           dateTime.month == yesterday.month &&
           dateTime.day == yesterday.day;
  }

  /// Check if date is this week
  static bool isThisWeek(DateTime? dateTime) {
    if (dateTime == null) return false;
    
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday % 7));
    final endOfWeek = startOfWeek.add(const Duration(days: 6));
    
    return dateTime.isAfter(startOfWeek) && dateTime.isBefore(endOfWeek);
  }
}
