<?php

namespace App\Http\Controllers\Employee;

use App\Http\Controllers\Controller;
use App\Models\Notification;
use App\Models\NotificationRecipient;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    /**
     * Show the employee dashboard
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index()
    {
        $employee = Auth::guard('employee')->user();
        
        // Get notifications for this employee
        $recentNotifications = NotificationRecipient::where('recipient_id', $employee->id)
            ->where('recipient_type', 'employee')
            ->with('notification')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();
            
        $totalForEmployee = NotificationRecipient::where('recipient_id', $employee->id)
            ->where('recipient_type', 'employee')
            ->count();
            
        $unreadCount = NotificationRecipient::where('recipient_id', $employee->id)
            ->where('recipient_type', 'employee')
            ->whereNull('read_at')
            ->count();
        
        $stats = [
            'notifications' => [
                'total_for_employee' => $totalForEmployee,
                'unread' => $unreadCount,
            ],
        ];
        
        return view('employee.dashboard', [
            'employee' => $employee,
            'stats' => $stats,
            'recentNotifications' => $recentNotifications,
        ]);
    }
    
    /**
     * Show employee notifications
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function notifications(Request $request)
    {
        $employee = Auth::guard('employee')->user();
        
        $query = NotificationRecipient::where('recipient_id', $employee->id)
            ->where('recipient_type', 'employee')
            ->with('notification');
            
        // Apply filters if provided
        if ($request->has('status')) {
            if ($request->status === 'read') {
                $query->whereNotNull('read_at');
            } elseif ($request->status === 'unread') {
                $query->whereNull('read_at');
            }
        }
        
        // Apply date filters
        if ($request->has('date')) {
            if ($request->date === 'today') {
                $query->whereDate('created_at', now()->toDateString());
            } elseif ($request->date === 'week') {
                $query->where('created_at', '>=', now()->startOfWeek());
            } elseif ($request->date === 'month') {
                $query->where('created_at', '>=', now()->startOfMonth());
            }
        }
        
        $notifications = $query->orderBy('created_at', 'desc')
            ->paginate(10);
        
        // Check if a specific notification was requested
        $highlightedNotificationId = null;
        if ($request->has('notification_id')) {
            $highlightedNotificationId = $request->notification_id;
            
            // Auto-mark as read when viewing a specific notification
            $recipient = NotificationRecipient::where('notification_id', $highlightedNotificationId)
                ->where('recipient_id', $employee->id)
                ->where('recipient_type', 'employee')
                ->whereNull('read_at')
                ->first();
                
            if ($recipient) {
                $recipient->read_at = now();
                $recipient->save();
            }
        }
            
        return view('employee.notifications', [
            'employee' => $employee,
            'notifications' => $notifications,
            'highlightedNotificationId' => $highlightedNotificationId,
        ]);
    }
    
    /**
     * Mark a notification as read
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $notification
     * @return \Illuminate\Http\RedirectResponse
     */
    public function markAsRead(Request $request, $notification)
    {
        $employee = Auth::guard('employee')->user();
        
        $recipient = NotificationRecipient::where('notification_id', $notification)
            ->where('recipient_id', $employee->id)
            ->where('recipient_type', 'employee')
            ->whereNull('read_at')
            ->first();
        
        if ($recipient) {
            $recipient->read_at = now();
            $recipient->save();
            
            return back()->with('success', 'Notification marked as read.');
        }
        
        return back();
    }
    
    /**
     * Get notification details for AJAX request
     *
     * @param  int  $notificationId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getNotification($notificationId)
    {
        $employee = Auth::guard('employee')->user();
        
        // Find notification
        $notification = Notification::find($notificationId);
        
        if (!$notification) {
            return response()->json(['success' => false, 'message' => 'Notification not found']);
        }
        
        // Check if the employee is a recipient
        $isRecipient = NotificationRecipient::where('notification_id', $notificationId)
            ->where('recipient_id', $employee->id)
            ->where('recipient_type', 'employee')
            ->exists();
            
        if (!$isRecipient) {
            return response()->json(['success' => false, 'message' => 'Unauthorized']);
        }
        
        // Mark as read if needed
        $recipient = NotificationRecipient::where('notification_id', $notificationId)
            ->where('recipient_id', $employee->id)
            ->where('recipient_type', 'employee')
            ->whereNull('read_at')
            ->first();
            
        if ($recipient) {
            $recipient->read_at = now();
            $recipient->save();
        }
        
        return response()->json([
            'success' => true,
            'notification' => $notification,
        ]);
    }
    
    /**
     * Mark notification as read via AJAX
     *
     * @param  int  $notificationId
     * @return \Illuminate\Http\JsonResponse
     */
    public function markAsReadAjax($notificationId)
    {
        $employee = Auth::guard('employee')->user();
        
        $recipient = NotificationRecipient::where('notification_id', $notificationId)
            ->where('recipient_id', $employee->id)
            ->where('recipient_type', 'employee')
            ->whereNull('read_at')
            ->first();
            
        if ($recipient) {
            $recipient->read_at = now();
            $recipient->save();
            
            return response()->json(['success' => true]);
        }
        
        return response()->json(['success' => false, 'message' => 'Notification not found or already read']);
    }
}
