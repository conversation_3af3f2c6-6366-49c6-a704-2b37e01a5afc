<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Login - معهد النبطية الفني</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --parchment: #f4ecdc;
            --costa-del-sol: #5d6e35;
            --locust: #abae88;
            --gurkha: #949c74;
            --avocado: #949b6c;
            --coral-reef: #c4c2a4;
            --thistle-green: #d0cdb0;
            --tana: #ded8bf;
            --chino: #ccc4a9;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: #5d6e35;
            background-attachment: fixed;
        }
        
        /* Apply Cairo font to all elements */
        *, ::after, ::before {
            font-family: 'Cairo', sans-serif;
        }
        
        /* Ensure form elements use Cairo font */
        input, select, textarea, button, .btn, .form-control {
            font-family: 'Cairo', sans-serif !important;
        }
        
        /* Set appropriate font weights */
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Cairo', sans-serif;
            font-weight: 700;
        }
        
        /* Adjust for Arabic text direction if needed */
        .rtl-text {
            direction: rtl;
            text-align: center;
        }
        .login-container {
            max-width: 450px;
            margin: 100px auto;
            padding: 35px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
            border-radius: 16px;
            background-color: #fff;
            border: none;
            position: relative;
            overflow: hidden;
        }
        
        .login-container::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(to right, var(--costa-del-sol), var(--locust));
        }
        .login-title {
            text-align: center;
            margin-bottom: 30px;
            color: var(--costa-del-sol);
        }
        .login-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(to right, var(--costa-del-sol), var(--gurkha));
            border: none;
            border-radius: 8px;
            font-weight: 600;
            letter-spacing: 0.5px;
            text-transform: uppercase;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(93, 110, 53, 0.15);
        }
        .login-btn:hover {
            background: linear-gradient(to right, var(--gurkha), var(--costa-del-sol));
            box-shadow: 0 6px 10px rgba(93, 110, 53, 0.25);
            transform: translateY(-1px);
        }
        .app-logo {
            text-align: center;
            margin-bottom: 25px;
            color: var(--gurkha);
        }
        .api-info {
            text-align: center;
            margin-top: 20px;
            font-size: 0.9rem;
            color: var(--gurkha);
        }
        .api-badge {
            display: inline-block;
            padding: 3px 10px;
            background-color: var(--coral-reef);
            border-radius: 20px;
            font-size: 0.8rem;
            color: var(--costa-del-sol);
            margin-top: 10px;
        }
        .form-control:focus {
            border-color: var(--locust);
            box-shadow: 0 0 0 0.25rem rgba(171, 174, 136, 0.25);
        }
        .form-check-input:checked {
            background-color: var(--costa-del-sol);
            border-color: var(--costa-del-sol);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <div class="app-logo">
                <img src="{{ asset('logo.jpeg') }}" alt="معهد النبطية الفني" style="width: 140px; height: 140px; object-fit: cover; margin-bottom: 15px; border-radius: 50%; border: 3px solid var(--locust); box-shadow: 0 4px 10px rgba(93, 110, 53, 0.25);">
                <h3 class="rtl-text" style="color: var(--costa-del-sol); font-weight: 700; margin-bottom: 10px; font-size: 1.5rem;">معهد النبطية الفني</h3>
                <div style="width: 100px; height: 3px; background: linear-gradient(to right, var(--costa-del-sol), var(--locust), var(--costa-del-sol)); margin: 0 auto;"></div>
            </div>
            <h5 class="login-title rtl-text">تسجيل دخول</h5>
            
            @if (session('error'))
                <div class="alert" style="background-color: #f8d7da; border-color: var(--gurkha); color: #721c24;">
                    {{ session('error') }}
                </div>
            @endif
            
            <form method="POST" action="{{ route('login.submit') }}">
                @csrf
                <div class="mb-3">
                    <label for="username" class="form-label" style="color: var(--costa-del-sol);">Username or Email</label>
                    <input type="text" class="form-control" id="username" name="username" required style="border-color: var(--chino);" placeholder="Username or Email address">
                    <small class="text-muted">
                        <!-- <strong>Students:</strong> Use your username (e.g. stu0001)<br>
                        <strong>Employees:</strong> Use your username or email<br>
                        <strong>Admins:</strong> Use your email address -->
                    </small>
                </div>
                <div class="mb-3">
                    <label for="password" class="form-label" style="color: var(--costa-del-sol);">Password</label>
                    <input type="password" class="form-control" id="password" name="password" required style="border-color: var(--chino);">
                </div>
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="remember" name="remember" style="border-color: var(--gurkha);">
                    <label class="form-check-label" for="remember" style="color: var(--gurkha);">Remember Me</label>
                </div>
                <button type="submit" class="btn btn-primary login-btn">تسجيل الدخول</button>
            </form>
            
            <div class="api-info">
                <p style="color: var(--gurkha); margin-bottom: 5px;">API Server Version 1.0.0</p>
                <span class="api-badge">Status: Online</span>
            </div>
        </div>
    </div>
</body>
</html>
