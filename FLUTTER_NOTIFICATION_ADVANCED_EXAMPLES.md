# 🚀 Advanced Flutter Notification Examples

## 📱 **Complete Notification Service Implementation**

### **notification_service.dart:**
```dart
import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:path/path.dart';

class NotificationService {
  final String baseUrl;
  final String token;

  NotificationService({required this.baseUrl, required this.token});

  Map<String, String> get _headers => {
    'Authorization': 'Bearer $token',
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  Map<String, String> get _multipartHeaders => {
    'Authorization': 'Bearer $token',
    'Accept': 'application/json',
  };

  /// Send notification with text only
  Future<NotificationResponse> sendTextNotification({
    required String title,
    required String message,
    required List<int> recipientIds,
    String recipientType = 'students',
    String priority = 'medium',
    String senderType = 'admin',
    int senderId = 1,
    String senderName = 'Flutter App',
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/notifications'),
        headers: _headers,
        body: jsonEncode({
          'title': title,
          'message': message,
          'sender_id': senderId,
          'sender_name': senderName,
          'sender_type': senderType,
          'recipient_type': recipientType,
          'recipient_ids': recipientIds,
          'priority': priority,
        }),
      );

      return NotificationResponse.fromJson(jsonDecode(response.body));
    } catch (e) {
      throw NotificationException('Failed to send notification: $e');
    }
  }

  /// Send notification with file attachments
  Future<NotificationResponse> sendNotificationWithFiles({
    required String title,
    required String message,
    required List<int> recipientIds,
    required List<File> files,
    String recipientType = 'students',
    String priority = 'high',
    String senderType = 'admin',
    int senderId = 1,
    String senderName = 'Flutter App',
  }) async {
    try {
      var request = http.MultipartRequest(
        'POST',
        Uri.parse('$baseUrl/notifications'),
      );

      // Add headers
      request.headers.addAll(_multipartHeaders);

      // Add form fields
      request.fields.addAll({
        'title': title,
        'message': message,
        'sender_id': senderId.toString(),
        'sender_name': senderName,
        'sender_type': senderType,
        'recipient_type': recipientType,
        'priority': priority,
      });

      // Add recipient IDs
      for (int i = 0; i < recipientIds.length; i++) {
        request.fields['recipient_ids[$i]'] = recipientIds[i].toString();
      }

      // Add files
      for (File file in files) {
        if (await file.exists()) {
          request.files.add(await http.MultipartFile.fromPath(
            'attachments[]',
            file.path,
            filename: basename(file.path),
          ));
        }
      }

      var response = await request.send();
      var responseBody = await response.stream.bytesToString();

      if (response.statusCode == 201) {
        return NotificationResponse.fromJson(jsonDecode(responseBody));
      } else {
        throw NotificationException('Upload failed: $responseBody');
      }
    } catch (e) {
      throw NotificationException('Failed to upload files: $e');
    }
  }

  /// Get all notifications with pagination
  Future<NotificationListResponse> getNotifications({
    int page = 1,
    int perPage = 15,
  }) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/notifications?page=$page&per_page=$perPage'),
        headers: _headers,
      );

      if (response.statusCode == 200) {
        return NotificationListResponse.fromJson(jsonDecode(response.body));
      } else {
        throw NotificationException('Failed to fetch notifications');
      }
    } catch (e) {
      throw NotificationException('Network error: $e');
    }
  }

  /// Delete multiple notifications
  Future<bool> bulkDeleteNotifications(List<int> ids) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/notifications/bulk-delete'),
        headers: _headers,
        body: jsonEncode({'notification_ids': ids}),
      );

      return response.statusCode == 200;
    } catch (e) {
      throw NotificationException('Failed to delete notifications: $e');
    }
  }

  /// Download attachment file
  Future<File?> downloadAttachment(String path, String filename) async {
    try {
      final url = '$baseUrl/../storage/$path';
      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final directory = await getApplicationDocumentsDirectory();
        final file = File('${directory.path}/$filename');
        await file.writeAsBytes(response.bodyBytes);
        return file;
      }
      return null;
    } catch (e) {
      throw NotificationException('Failed to download file: $e');
    }
  }
}
```

---

## 📊 **Data Models**

### **notification_models.dart:**
```dart
class NotificationModel {
  final int id;
  final String title;
  final String message;
  final String senderName;
  final String recipientType;
  final List<String> attachmentPaths;
  final List<String> attachmentNames;
  final String priority;
  final String status;
  final DateTime createdAt;

  NotificationModel({
    required this.id,
    required this.title,
    required this.message,
    required this.senderName,
    required this.recipientType,
    required this.attachmentPaths,
    required this.attachmentNames,
    required this.priority,
    required this.status,
    required this.createdAt,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'] ?? 0,
      title: json['title'] ?? '',
      message: json['message'] ?? '',
      senderName: json['sender_name'] ?? '',
      recipientType: json['recipient_type'] ?? '',
      attachmentPaths: json['attachment_path'] != null
          ? List<String>.from(json['attachment_path'])
          : [],
      attachmentNames: json['attachment_name'] != null
          ? List<String>.from(json['attachment_name'])
          : [],
      priority: json['priority'] ?? 'medium',
      status: json['status'] ?? 'sent',
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  bool get hasAttachments => attachmentPaths.isNotEmpty;
  
  Color get priorityColor {
    switch (priority) {
      case 'high': return Colors.red;
      case 'medium': return Colors.orange;
      case 'low': return Colors.green;
      default: return Colors.grey;
    }
  }

  IconData get priorityIcon {
    switch (priority) {
      case 'high': return Icons.priority_high;
      case 'medium': return Icons.remove;
      case 'low': return Icons.keyboard_arrow_down;
      default: return Icons.info;
    }
  }
}

class NotificationResponse {
  final bool success;
  final String message;
  final NotificationModel? data;

  NotificationResponse({
    required this.success,
    required this.message,
    this.data,
  });

  factory NotificationResponse.fromJson(Map<String, dynamic> json) {
    return NotificationResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null ? NotificationModel.fromJson(json['data']) : null,
    );
  }
}

class NotificationListResponse {
  final bool success;
  final String message;
  final List<NotificationModel> data;

  NotificationListResponse({
    required this.success,
    required this.message,
    required this.data,
  });

  factory NotificationListResponse.fromJson(Map<String, dynamic> json) {
    return NotificationListResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null
          ? (json['data'] as List).map((n) => NotificationModel.fromJson(n)).toList()
          : [],
    );
  }
}

class NotificationException implements Exception {
  final String message;
  NotificationException(this.message);
  
  @override
  String toString() => 'NotificationException: $message';
}
```

---

## 🎯 **Advanced UI Components**

### **notification_card.dart:**
```dart
class NotificationCard extends StatelessWidget {
  final NotificationModel notification;
  final VoidCallback? onTap;
  final VoidCallback? onDelete;

  const NotificationCard({
    Key? key,
    required this.notification,
    this.onTap,
    this.onDelete,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    notification.priorityIcon,
                    color: notification.priorityColor,
                    size: 20,
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      notification.title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  if (onDelete != null)
                    IconButton(
                      icon: Icon(Icons.delete, color: Colors.red),
                      onPressed: onDelete,
                    ),
                ],
              ),
              SizedBox(height: 8),
              Text(
                notification.message,
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              if (notification.hasAttachments) ...[
                SizedBox(height: 8),
                Wrap(
                  spacing: 4,
                  children: notification.attachmentNames
                      .map((name) => Chip(
                            label: Text(name, style: TextStyle(fontSize: 12)),
                            backgroundColor: Colors.blue[50],
                            avatar: Icon(Icons.attach_file, size: 16),
                          ))
                      .toList(),
                ),
              ],
              SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'From: ${notification.senderName}',
                    style: TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                  Text(
                    _formatDate(notification.createdAt),
                    style: TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
```

---

## 🔄 **State Management with Provider**

### **notification_provider.dart:**
```dart
import 'package:flutter/foundation.dart';

class NotificationProvider with ChangeNotifier {
  final NotificationService _service;
  
  List<NotificationModel> _notifications = [];
  bool _loading = false;
  String? _error;

  NotificationProvider(this._service);

  List<NotificationModel> get notifications => _notifications;
  bool get loading => _loading;
  String? get error => _error;

  Future<void> loadNotifications() async {
    _loading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await _service.getNotifications();
      _notifications = response.data;
    } catch (e) {
      _error = e.toString();
    } finally {
      _loading = false;
      notifyListeners();
    }
  }

  Future<bool> sendNotification({
    required String title,
    required String message,
    required List<int> recipientIds,
    List<File>? files,
    String priority = 'medium',
  }) async {
    try {
      if (files != null && files.isNotEmpty) {
        await _service.sendNotificationWithFiles(
          title: title,
          message: message,
          recipientIds: recipientIds,
          files: files,
          priority: priority,
        );
      } else {
        await _service.sendTextNotification(
          title: title,
          message: message,
          recipientIds: recipientIds,
          priority: priority,
        );
      }
      
      // Reload notifications
      await loadNotifications();
      return true;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  Future<bool> deleteNotifications(List<int> ids) async {
    try {
      final success = await _service.bulkDeleteNotifications(ids);
      if (success) {
        _notifications.removeWhere((n) => ids.contains(n.id));
        notifyListeners();
      }
      return success;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }
}
```

---

## 🧪 **Usage Examples**

### **main.dart:**
```dart
void main() {
  runApp(
    ChangeNotifierProvider(
      create: (context) => NotificationProvider(
        NotificationService(
          baseUrl: 'http://localhost/appnote-api/public/api',
          token: 'your-token-here',
        ),
      ),
      child: MyApp(),
    ),
  );
}
```

### **notification_screen.dart:**
```dart
class NotificationScreen extends StatefulWidget {
  @override
  _NotificationScreenState createState() => _NotificationScreenState();
}

class _NotificationScreenState extends State<NotificationScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<NotificationProvider>().loadNotifications();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Notifications'),
        actions: [
          IconButton(
            icon: Icon(Icons.add),
            onPressed: () => Navigator.push(
              context,
              MaterialPageRoute(builder: (_) => SendNotificationScreen()),
            ),
          ),
        ],
      ),
      body: Consumer<NotificationProvider>(
        builder: (context, provider, child) {
          if (provider.loading) {
            return Center(child: CircularProgressIndicator());
          }

          if (provider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('Error: ${provider.error}'),
                  ElevatedButton(
                    onPressed: provider.loadNotifications,
                    child: Text('Retry'),
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            itemCount: provider.notifications.length,
            itemBuilder: (context, index) {
              final notification = provider.notifications[index];
              return NotificationCard(
                notification: notification,
                onTap: () => _showNotificationDetails(notification),
                onDelete: () => _deleteNotification(notification.id),
              );
            },
          );
        },
      ),
    );
  }

  void _showNotificationDetails(NotificationModel notification) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(notification.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(notification.message),
            if (notification.hasAttachments) ...[
              SizedBox(height: 16),
              Text('Attachments:', style: TextStyle(fontWeight: FontWeight.bold)),
              ...notification.attachmentNames.map((name) => 
                ListTile(
                  leading: Icon(Icons.attach_file),
                  title: Text(name),
                  onTap: () => _downloadAttachment(notification, name),
                ),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Close'),
          ),
        ],
      ),
    );
  }

  void _deleteNotification(int id) {
    context.read<NotificationProvider>().deleteNotifications([id]);
  }

  void _downloadAttachment(NotificationModel notification, String filename) {
    // Implement download logic
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Downloading $filename...')),
    );
  }
}
```

**Ready for production use!** 🚀
