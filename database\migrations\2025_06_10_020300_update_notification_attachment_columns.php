<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('notifications', function (Blueprint $table) {
            // Change string columns to text to accommodate JSON data for multiple files
            $table->text('attachment_path')->nullable()->change();
            $table->text('attachment_name')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('notifications', function (Blueprint $table) {
            // Change back to string if needed
            $table->string('attachment_path')->nullable()->change();
            $table->string('attachment_name')->nullable()->change();
        });
    }
};
