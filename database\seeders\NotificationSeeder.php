<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Notification;

class NotificationSeeder extends Seeder
{
    public function run()
    {
        // Create sample notifications if none exist
        if (Notification::count() == 0) {
            Notification::create([
                'title' => 'إشعار مهم للموظفين',
                'message' => 'يرجى مراجعة الإدارة لاستلام الوثائق المطلوبة قبل نهاية الأسبوع',
                'target_audience' => 'employees',
                'priority' => 'high',
                'is_read' => false,
                'sender_name' => 'إدارة الموارد البشرية',
            ]);

            Notification::create([
                'title' => 'تحديث النظام',
                'message' => 'سيتم تحديث النظام يوم الجمعة من الساعة 2 إلى 4 مساءً. يرجى حفظ أعمالكم',
                'target_audience' => 'all',
                'priority' => 'medium',
                'is_read' => true,
                'sender_name' => 'إدارة تقنية المعلومات',
            ]);

            Notification::create([
                'title' => 'اجتماع الفريق',
                'message' => 'اجتماع فريق العمل غداً الساعة 10 صباحاً في قاعة الاجتماعات الرئيسية',
                'target_audience' => 'employees',
                'priority' => 'medium',
                'is_read' => false,
                'sender_name' => 'مدير القسم',
            ]);

            Notification::create([
                'title' => 'إجازة مقبولة',
                'message' => 'تم قبول طلب الإجازة المقدم بتاريخ 25/6/2025. ستبدأ الإجازة من تاريخ 1/7/2025',
                'target_audience' => 'employees',
                'priority' => 'low',
                'is_read' => true,
                'sender_name' => 'إدارة الموارد البشرية',
            ]);

            Notification::create([
                'title' => 'تذكير بالمواعيد',
                'message' => 'لا تنس موعد التقييم السنوي يوم الأحد القادم الساعة 9 صباحاً',
                'target_audience' => 'employees',
                'priority' => 'medium',
                'is_read' => false,
                'sender_name' => 'إدارة الموارد البشرية',
            ]);

            Notification::create([
                'title' => 'تهنئة بالعيد',
                'message' => 'تتقدم الإدارة بأحر التهاني بمناسبة العيد السعيد. عيد مبارك وكل عام وأنتم بخير',
                'target_audience' => 'all',
                'priority' => 'low',
                'is_read' => false,
                'sender_name' => 'الإدارة العامة',
            ]);

            Notification::create([
                'title' => 'دورة تدريبية',
                'message' => 'سيتم عقد دورة تدريبية حول استخدام النظام الجديد يوم الثلاثاء القادم',
                'target_audience' => 'employees',
                'priority' => 'medium',
                'is_read' => false,
                'sender_name' => 'قسم التدريب',
            ]);
        }
    }
}
