<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class LogApiRequests
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Process the request
        $response = $next($request);
        
        // Don't log internal or asset requests
        if (!str_starts_with($request->path(), 'api/')) {
            return $response;
        }
        
        // Get data for logging
        $endpoint = $request->path();
        $method = $request->method();
        $statusCode = $response->getStatusCode();
        $userId = Auth::id();
        
        // Store limited request and response data to avoid bloating the DB
        $requestData = null;
        if ($method !== 'GET') {
            // Don't log passwords or sensitive info
            $filteredInput = $request->except(['password', 'token', 'api_token', '_token']);
            if (!empty($filteredInput)) {
                $requestData = json_encode($filteredInput);
            }
        }
        
        // Get limited response data
        $responseData = null;
        $responseContent = $response->getContent();
        if (!empty($responseContent) && strlen($responseContent) < 10000) { // Limit size
            $responseData = $responseContent;
        }
        
        // Log the API request
        DB::table('api_logs')->insert([
            'endpoint' => $endpoint,
            'method' => $method,
            'status_code' => $statusCode,
            'request_data' => $requestData,
            'response_data' => $responseData,
            'user_agent' => $request->userAgent(),
            'ip_address' => $request->ip(),
            'user_id' => $userId,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
        
        return $response;
    }
}
