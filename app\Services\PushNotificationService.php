<?php

namespace App\Services;

use App\Models\PushSubscription;
use App\Models\NotificationRecipient;
use Minishlink\WebPush\WebPush;
use Minishlink\WebPush\Subscription;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;

class PushNotificationService
{
    private $webPush;

    public function __construct()
    {
        $this->webPush = new WebPush([
            'VAPID' => [
                'subject' => config('app.url'),
                'publicKey' => config('webpush.vapid.public_key'),
                'privateKey' => config('webpush.vapid.private_key'),
            ],
        ]);
    }

    /**
     * Subscribe a user to push notifications.
     */
    public function subscribe($userId, $userType, $subscriptionData, $userAgent = null, $ipAddress = null)
    {
        try {
            // Validate subscription data
            if (!isset($subscriptionData['endpoint']) || 
                !isset($subscriptionData['keys']['p256dh']) || 
                !isset($subscriptionData['keys']['auth'])) {
                throw new \Exception('Invalid subscription data');
            }

            // Check if subscription already exists
            $existingSubscription = PushSubscription::where('user_id', $userId)
                ->where('user_type', $userType)
                ->where('endpoint', $subscriptionData['endpoint'])
                ->first();

            if ($existingSubscription) {
                // Update existing subscription
                $existingSubscription->update([
                    'public_key' => $subscriptionData['keys']['p256dh'],
                    'auth_token' => $subscriptionData['keys']['auth'],
                    'is_active' => true,
                    'last_used_at' => now(),
                    'user_agent' => $userAgent,
                    'ip_address' => $ipAddress,
                ]);
                return $existingSubscription;
            }

            // Create new subscription
            $subscription = PushSubscription::create([
                'user_id' => $userId,
                'user_type' => $userType,
                'endpoint' => $subscriptionData['endpoint'],
                'public_key' => $subscriptionData['keys']['p256dh'],
                'auth_token' => $subscriptionData['keys']['auth'],
                'content_encoding' => $subscriptionData['contentEncoding'] ?? 'aes128gcm',
                'user_agent' => $userAgent,
                'ip_address' => $ipAddress,
                'is_active' => true,
                'last_used_at' => now(),
            ]);

            Log::info("Push subscription created for {$userType} {$userId}");
            return $subscription;

        } catch (\Exception $e) {
            Log::error("Failed to create push subscription: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Unsubscribe a user from push notifications.
     */
    public function unsubscribe($userId, $userType, $endpoint = null)
    {
        $query = PushSubscription::where('user_id', $userId)
            ->where('user_type', $userType);

        if ($endpoint) {
            $query->where('endpoint', $endpoint);
        }

        $subscriptions = $query->get();
        
        foreach ($subscriptions as $subscription) {
            $subscription->deactivate();
        }

        Log::info("Push subscriptions deactivated for {$userType} {$userId}");
        return true;
    }

    /**
     * Send push notification to specific users.
     */
    public function sendToUsers(array $userIds, $userType, $title, $body, $data = [], $icon = null, $badge = null)
    {
        $subscriptions = PushSubscription::active()
            ->forUsers($userIds, $userType)
            ->get();

        return $this->sendToSubscriptions($subscriptions, $title, $body, $data, $icon, $badge);
    }

    /**
     * Send push notification to all users of a specific type.
     */
    public function sendToUserType($userType, $title, $body, $data = [], $icon = null, $badge = null)
    {
        $subscriptions = PushSubscription::active()
            ->forUserType($userType)
            ->get();

        return $this->sendToSubscriptions($subscriptions, $title, $body, $data, $icon, $badge);
    }

    /**
     * Send push notification based on notification recipients.
     */
    public function sendToNotificationRecipients($notificationId, $title, $body, $data = [], $icon = null, $badge = null)
    {
        // Get all recipients for this notification
        $recipients = NotificationRecipient::where('notification_id', $notificationId)
            ->get()
            ->groupBy('recipient_type');

        $totalSent = 0;
        $errors = [];

        foreach ($recipients as $recipientType => $recipientGroup) {
            $userIds = $recipientGroup->pluck('recipient_id')->toArray();
            
            $subscriptions = PushSubscription::active()
                ->forUsers($userIds, $recipientType)
                ->get();

            $result = $this->sendToSubscriptions($subscriptions, $title, $body, $data, $icon, $badge);
            $totalSent += $result['sent'];
            $errors = array_merge($errors, $result['errors']);
        }

        return [
            'sent' => $totalSent,
            'errors' => $errors
        ];
    }

    /**
     * Send push notifications to a collection of subscriptions.
     */
    private function sendToSubscriptions($subscriptions, $title, $body, $data = [], $icon = null, $badge = null)
    {
        $sent = 0;
        $errors = [];

        // Prepare notification payload
        $payload = json_encode([
            'title' => $title,
            'body' => $body,
            'icon' => $icon ?: '/icons/icon-192x192.png',
            'badge' => $badge ?: '/icons/icon-72x72.png',
            'data' => array_merge($data, [
                'timestamp' => time(),
                'url' => config('app.url'),
            ]),
            'actions' => [
                [
                    'action' => 'view',
                    'title' => 'عرض',
                    'icon' => '/icons/icon-96x96.png'
                ],
                [
                    'action' => 'close',
                    'title' => 'إغلاق',
                    'icon' => '/icons/icon-72x72.png'
                ]
            ],
            'requireInteraction' => false,
            'silent' => false,
            'vibrate' => [200, 100, 200],
            'tag' => 'appnote-notification',
            'renotify' => true
        ]);

        foreach ($subscriptions as $pushSubscription) {
            try {
                $subscription = Subscription::create($pushSubscription->getSubscriptionData());
                
                $result = $this->webPush->sendOneNotification(
                    $subscription,
                    $payload,
                    ['TTL' => 3600] // 1 hour TTL
                );

                if ($result->isSuccess()) {
                    $pushSubscription->updateLastUsed();
                    $sent++;
                    Log::info("Push notification sent successfully to {$pushSubscription->user_type} {$pushSubscription->user_id}");
                } else {
                    $error = "Failed to send to {$pushSubscription->user_type} {$pushSubscription->user_id}: " . $result->getReason();
                    $errors[] = $error;
                    Log::warning($error);

                    // Deactivate subscription if it's invalid
                    if ($result->isSubscriptionExpired()) {
                        $pushSubscription->deactivate();
                        Log::info("Deactivated expired subscription for {$pushSubscription->user_type} {$pushSubscription->user_id}");
                    }
                }

            } catch (\Exception $e) {
                $error = "Exception sending to {$pushSubscription->user_type} {$pushSubscription->user_id}: " . $e->getMessage();
                $errors[] = $error;
                Log::error($error);
            }
        }

        return [
            'sent' => $sent,
            'errors' => $errors
        ];
    }

    /**
     * Get subscription statistics.
     */
    public function getStats()
    {
        return [
            'total_subscriptions' => PushSubscription::count(),
            'active_subscriptions' => PushSubscription::active()->count(),
            'students' => PushSubscription::active()->forUserType('student')->count(),
            'employees' => PushSubscription::active()->forUserType('employee')->count(),
            'admins' => PushSubscription::active()->forUserType('admin')->count(),
        ];
    }

    /**
     * Clean up expired subscriptions.
     */
    public function cleanupExpiredSubscriptions()
    {
        $expiredCount = 0;
        $subscriptions = PushSubscription::active()->get();

        foreach ($subscriptions as $subscription) {
            if ($subscription->isExpired()) {
                $subscription->deactivate();
                $expiredCount++;
            }
        }

        Log::info("Cleaned up {$expiredCount} expired push subscriptions");
        return $expiredCount;
    }
}
