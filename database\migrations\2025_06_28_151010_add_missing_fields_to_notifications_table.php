<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('notifications', function (Blueprint $table) {
            // Add missing fields for Flutter app compatibility
            $table->string('type')->default('info')->after('message');
            $table->string('target_audience')->default('all')->after('recipient_ids');
            $table->boolean('is_active')->default(true)->after('status');
            $table->text('content')->nullable()->after('message'); // Alternative to message
            $table->json('attachments')->nullable()->after('attachment_name'); // Multiple attachments
            $table->timestamp('scheduled_at')->nullable()->after('is_active');
            $table->timestamp('expires_at')->nullable()->after('scheduled_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('notifications', function (Blueprint $table) {
            $table->dropColumn([
                'type',
                'target_audience',
                'is_active',
                'content',
                'attachments',
                'scheduled_at',
                'expires_at'
            ]);
        });
    }
};
