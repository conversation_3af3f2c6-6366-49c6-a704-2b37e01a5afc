<?php

namespace App\Http\Controllers\Api;

use App\Models\Employee;
use Illuminate\Http\Request;

class TestController
{
    public function simple()
    {
        return response()->json([
            'success' => true,
            'message' => 'Test controller works!',
            'timestamp' => now()
        ]);
    }

    public function dashboard()
    {
        try {
            // Get test employee
            $employee = Employee::where('username', 'test_employee')->first();
            
            if (!$employee) {
                return response()->json([
                    'success' => false,
                    'message' => 'Test employee not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'employee_info' => [
                        'name' => $employee->full_name,
                        'username' => $employee->username,
                        'employee_type' => $employee->employee_type,
                        'contract_type' => $employee->contract_type,
                        'job_status' => $employee->job_status,
                        'phone' => $employee->phone,
                    ],
                    'notifications' => [
                        'total' => 5,
                        'unread' => 2,
                    ],
                    'work_info' => [
                        'employee_type' => $employee->employee_type,
                        'contract_type' => $employee->contract_type,
                        'job_status' => $employee->job_status,
                        'automatic_number' => $employee->automatic_number,
                        'financial_number' => $employee->financial_number,
                        'state_cooperative_number' => $employee->state_cooperative_number,
                        'bank_account_number' => $employee->bank_account_number,
                    ],
                ],
                'message' => 'Dashboard data retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ], 500);
        }
    }
}
