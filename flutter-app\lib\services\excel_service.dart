import 'package:excel/excel.dart';
import 'package:flutter/foundation.dart';
import 'student_service.dart';

class ExcelService {
  // Expected column headers in order
  static const List<String> expectedHeaders = [
    'username',
    'full_name',
    'nationality',
    'phone',
    'specialization',
    'section',
    'class',
    'level',
    'result',
    'password',
    'is_active',
  ];

  // Import students from Excel file
  static Future<Map<String, dynamic>> importStudentsFromExcel(
    Uint8List fileBytes,
    String fileName,
  ) async {
    try {
      if (kDebugMode) {
        print('📊 Starting Excel import: $fileName');
      }

      // Parse Excel file
      final excel = Excel.decodeBytes(fileBytes);

      if (excel.tables.isEmpty) {
        throw Exception('الملف فارغ أو لا يحتوي على جداول');
      }

      // Get the first sheet
      final sheetName = excel.tables.keys.first;
      final sheet = excel.tables[sheetName];

      if (sheet == null || sheet.rows.isEmpty) {
        throw Exception('الجدول فارغ');
      }

      if (kDebugMode) {
        print('📋 Sheet: $sheetName, Rows: ${sheet.rows.length}');
      }

      // Validate headers
      final headerRow = sheet.rows.first;
      final headers = headerRow
          .map((cell) => cell?.value?.toString().trim() ?? '')
          .toList();

      if (kDebugMode) {
        print('📝 Headers found: $headers');
        print('📝 Expected headers: $expectedHeaders');
      }

      // Check if headers match expected format
      final validationResult = _validateHeaders(headers);
      if (!validationResult['isValid']) {
        throw Exception(validationResult['error']);
      }

      // Process data rows
      final List<Map<String, dynamic>> studentsData = [];
      final List<String> errors = [];
      int successCount = 0;
      int errorCount = 0;

      for (int i = 1; i < sheet.rows.length; i++) {
        try {
          final row = sheet.rows[i];
          final studentData = _parseStudentRow(row, i + 1);

          if (studentData != null) {
            studentsData.add(studentData);
            successCount++;
          }
        } catch (e) {
          errorCount++;
          errors.add('الصف ${i + 1}: $e');
          if (kDebugMode) {
            print('❌ Error in row ${i + 1}: $e');
          }
        }
      }

      if (kDebugMode) {
        print('✅ Parsed $successCount students successfully');
        print('❌ $errorCount errors found');
      }

      // Import students to database
      int importedCount = 0;
      final List<String> importErrors = [];

      for (final studentData in studentsData) {
        try {
          final result = await StudentService.createStudent(studentData);
          if (result != null) {
            importedCount++;
          } else {
            importErrors.add(
              'فشل في إضافة الطالب: ${studentData['full_name']}',
            );
          }
        } catch (e) {
          importErrors.add('خطأ في إضافة ${studentData['full_name']}: $e');
        }
      }

      // Clear cache to refresh data
      StudentService.clearCache();

      if (kDebugMode) {
        print(
          '🎉 Import completed: $importedCount/$successCount students imported',
        );
      }

      return {
        'success': true,
        'totalRows': sheet.rows.length - 1, // Exclude header
        'parsedCount': successCount,
        'importedCount': importedCount,
        'parseErrors': errors,
        'importErrors': importErrors,
        'message': 'تم استيراد $importedCount طالب من أصل $successCount',
      };
    } catch (e) {
      if (kDebugMode) {
        print('💥 Excel import failed: $e');
      }

      return {
        'success': false,
        'error': e.toString(),
        'message': 'فشل في استيراد الملف: $e',
      };
    }
  }

  // Validate Excel headers
  static Map<String, dynamic> _validateHeaders(List<String> headers) {
    // Remove empty headers and normalize
    final cleanHeaders = headers
        .where((h) => h.isNotEmpty)
        .map((h) => h.toLowerCase().trim())
        .toList();

    if (cleanHeaders.length < expectedHeaders.length) {
      return {
        'isValid': false,
        'error':
            'عدد الأعمدة غير صحيح. المطلوب: ${expectedHeaders.length}، الموجود: ${cleanHeaders.length}',
      };
    }

    // Check each expected header
    for (int i = 0; i < expectedHeaders.length; i++) {
      if (i >= cleanHeaders.length || cleanHeaders[i] != expectedHeaders[i]) {
        return {
          'isValid': false,
          'error':
              'ترتيب الأعمدة غير صحيح. العمود ${i + 1} يجب أن يكون "${expectedHeaders[i]}" وليس "${i < cleanHeaders.length ? cleanHeaders[i] : 'فارغ'}"',
        };
      }
    }

    return {'isValid': true};
  }

  // Parse a single student row
  static Map<String, dynamic>? _parseStudentRow(
    List<Data?> row,
    int rowNumber,
  ) {
    if (row.isEmpty) return null;

    // Extract values
    final values = row
        .map((cell) => cell?.value?.toString().trim() ?? '')
        .toList();

    // Ensure we have enough columns
    while (values.length < expectedHeaders.length) {
      values.add('');
    }

    final username = values[0];
    final fullName = values[1];
    final nationality = values[2].isEmpty ? 'لبناني' : values[2];
    final phone = values[3];
    final specialization = values[4];
    final section = values[5];
    final studentClass = values[6];
    final level = values[7];
    final result = values[8].isEmpty ? null : values[8];
    final password = values[9].isEmpty ? 'stu123' : values[9];
    final isActiveStr = values[10].toLowerCase();

    // Validate required fields
    if (username.isEmpty) {
      throw Exception('اسم المستخدم مطلوب');
    }
    if (fullName.isEmpty) {
      throw Exception('الاسم الكامل مطلوب');
    }
    if (specialization.isEmpty) {
      throw Exception('التخصص مطلوب');
    }
    if (section.isEmpty) {
      throw Exception('الشعبة مطلوبة');
    }
    if (studentClass.isEmpty) {
      throw Exception('الصف مطلوب');
    }
    if (level.isEmpty) {
      throw Exception('المستوى مطلوب');
    }

    // Parse is_active
    bool isActive = true;
    if (isActiveStr.isNotEmpty) {
      isActive =
          isActiveStr == '1' ||
          isActiveStr == 'true' ||
          isActiveStr == 'نعم' ||
          isActiveStr == 'فعال';
    }

    return {
      'username': username,
      'full_name': fullName,
      'nationality': nationality,
      'phone': phone,
      'specialization': specialization,
      'section': section,
      'class': studentClass,
      'level': level,
      'result': result,
      'password': password,
      'is_active': isActive,
    };
  }

  // Generate sample Excel file for download
  static Uint8List generateSampleExcel() {
    final excel = Excel.createExcel();
    final sheet = excel['Sample'];

    // Add headers
    for (int i = 0; i < expectedHeaders.length; i++) {
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0))
          .value = TextCellValue(
        expectedHeaders[i],
      );
    }

    // Add sample data
    final sampleData = [
      [
        'stu1001',
        'أحمد محمد علي',
        'لبناني',
        '70123456',
        'المعلوماتية الإدارية',
        'A1',
        'TS1 معلوماتية A1',
        'TS1 الامتياز الفني',
        '',
        'stu123',
        '1',
      ],
      [
        'stu1002',
        'فاطمة حسن أحمد',
        'لبنانية',
        '71234567',
        'العناية التمريضية',
        'F1',
        'BT1 عناية F1',
        'BT1 البكالوريا الفنية',
        'نجح',
        'stu123',
        '1',
      ],
      [
        'stu1003',
        'محمد علي حسن',
        'سوري',
        '76345678',
        'الكهرباء',
        'A1',
        'TS2 كهرباء A1',
        'TS2 الامتياز الفني',
        '',
        'stu123',
        '1',
      ],
    ];

    for (int rowIndex = 0; rowIndex < sampleData.length; rowIndex++) {
      final rowData = sampleData[rowIndex];
      for (int colIndex = 0; colIndex < rowData.length; colIndex++) {
        sheet
            .cell(
              CellIndex.indexByColumnRow(
                columnIndex: colIndex,
                rowIndex: rowIndex + 1,
              ),
            )
            .value = TextCellValue(
          rowData[colIndex],
        );
      }
    }

    final excelBytes = excel.encode();
    return Uint8List.fromList(excelBytes!);
  }
}
