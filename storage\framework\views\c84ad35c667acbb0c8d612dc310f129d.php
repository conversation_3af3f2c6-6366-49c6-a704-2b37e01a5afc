<?php $__env->startSection('content'); ?>
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header text-center" style="background: var(--costa-del-sol); color: white;">
                    <h1><i class="fas fa-mobile-alt me-2"></i>PWA Test Dashboard</h1>
                    <p class="mb-0">اختبار تطبيق الويب التقدمي</p>
                </div>
                <div class="card-body p-4">
                    <div id="test-results">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                            <p class="mt-2">جاري فحص PWA...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        runPWATests();
    });

    async function runPWATests() {
        const results = [];
        
        // Test 1: Service Worker Support
        results.push({
            name: 'Service Worker Support',
            nameAr: 'دعم Service Worker',
            status: 'serviceWorker' in navigator ? 'pass' : 'fail',
            message: 'serviceWorker' in navigator ? 'مدعوم' : 'غير مدعوم'
        });

        // Test 2: Manifest
        try {
            const response = await fetch('/manifest.json');
            const manifest = await response.json();
            results.push({
                name: 'Manifest File',
                nameAr: 'ملف Manifest',
                status: manifest.name ? 'pass' : 'fail',
                message: manifest.name ? `تم العثور على: ${manifest.name}` : 'لم يتم العثور على الملف'
            });
        } catch (error) {
            results.push({
                name: 'Manifest File',
                nameAr: 'ملف Manifest',
                status: 'fail',
                message: 'خطأ في تحميل الملف'
            });
        }

        // Test 3: Service Worker Registration
        if ('serviceWorker' in navigator) {
            try {
                const registration = await navigator.serviceWorker.register('/sw.js');
                results.push({
                    name: 'Service Worker Registration',
                    nameAr: 'تسجيل Service Worker',
                    status: registration ? 'pass' : 'fail',
                    message: registration ? 'تم التسجيل بنجاح' : 'فشل في التسجيل'
                });
            } catch (error) {
                results.push({
                    name: 'Service Worker Registration',
                    nameAr: 'تسجيل Service Worker',
                    status: 'fail',
                    message: 'خطأ في التسجيل: ' + error.message
                });
            }
        }

        // Test 4: HTTPS
        results.push({
            name: 'HTTPS',
            nameAr: 'بروتوكول HTTPS',
            status: location.protocol === 'https:' || location.hostname === 'localhost' ? 'pass' : 'warning',
            message: location.protocol === 'https:' ? 'آمن' : location.hostname === 'localhost' ? 'محلي (مقبول للاختبار)' : 'مطلوب HTTPS للإنتاج'
        });

        // Test 5: Cache API
        results.push({
            name: 'Cache API',
            nameAr: 'واجهة التخزين المؤقت',
            status: 'caches' in window ? 'pass' : 'fail',
            message: 'caches' in window ? 'مدعوم' : 'غير مدعوم'
        });

        // Test 6: Fetch API
        results.push({
            name: 'Fetch API',
            nameAr: 'واجهة Fetch',
            status: 'fetch' in window ? 'pass' : 'fail',
            message: 'fetch' in window ? 'مدعوم' : 'غير مدعوم'
        });

        // Test 7: Install Prompt
        let installPromptSupported = false;
        window.addEventListener('beforeinstallprompt', (e) => {
            installPromptSupported = true;
        });
        
        setTimeout(() => {
            results.push({
                name: 'Install Prompt',
                nameAr: 'مطالبة التثبيت',
                status: installPromptSupported ? 'pass' : 'warning',
                message: installPromptSupported ? 'متوفر' : 'قد يكون التطبيق مثبت بالفعل'
            });
            
            displayResults(results);
        }, 1000);
    }

    function displayResults(results) {
        const container = document.getElementById('test-results');
        let html = '';
        
        const passCount = results.filter(r => r.status === 'pass').length;
        const totalCount = results.length;
        
        html += `
            <div class="alert alert-info text-center mb-4">
                <h4><i class="fas fa-chart-pie me-2"></i>نتائج الاختبار</h4>
                <p class="mb-0">نجح ${passCount} من ${totalCount} اختبارات</p>
                <div class="progress mt-2">
                    <div class="progress-bar" style="width: ${(passCount/totalCount)*100}%"></div>
                </div>
            </div>
        `;
        
        results.forEach(result => {
            const iconClass = result.status === 'pass' ? 'fa-check-circle text-success' : 
                            result.status === 'warning' ? 'fa-exclamation-triangle text-warning' : 
                            'fa-times-circle text-danger';
            
            html += `
                <div class="d-flex align-items-center justify-content-between p-3 border-bottom">
                    <div>
                        <h6 class="mb-1">${result.nameAr}</h6>
                        <small class="text-muted">${result.name}</small>
                    </div>
                    <div class="text-end">
                        <i class="fas ${iconClass} fa-lg me-2"></i>
                        <span class="small">${result.message}</span>
                    </div>
                </div>
            `;
        });
        
        html += `
            <div class="text-center mt-4">
                <button class="btn btn-primary me-2" onclick="location.reload()">
                    <i class="fas fa-redo me-1"></i>إعادة الاختبار
                </button>
                <button class="btn btn-success" onclick="window.location.href='<?php echo e(route('dashboard')); ?>'">
                    <i class="fas fa-home me-1"></i>العودة للوحة التحكم
                </button>
            </div>
        `;
        
        container.innerHTML = html;
    }
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\appnote-api\resources\views/pwa-test.blade.php ENDPATH**/ ?>