-- إعداد قاعدة البيانات للتطبيق - محدث
-- Database Setup for AppNote Application - Updated

-- ===================================
-- 1. إنشاء أدمن تجريبي
-- Create Demo Admin
-- ===================================

-- حذف الأدمن إذا كان موجوداً
DELETE FROM users WHERE username = 'admin';

-- إنشاء أدمن جديد
-- Password: password (hashed with bcrypt)
INSERT INTO users (username, password, name, email, created_at, updated_at) 
VALUES (
  'admin',
  '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
  'مدير النظام',
  '<EMAIL>',
  NOW(),
  NOW()
);

-- ===================================
-- 2. إنشاء طلاب تجريبيين
-- Create Demo Students
-- ===================================

-- حذف الطلاب التجريبيين إذا كانوا موجودين
DELETE FROM students WHERE username IN ('student001', 'student002', 'student003', 'student004', 'student005', 'student006', 'student007', 'student008');

-- إنشاء طلاب تجريبيين حسب بنية الجدول الحقيقية
INSERT INTO students (
  username, 
  full_name, 
  nationality, 
  phone, 
  specialization, 
  section, 
  class, 
  level, 
  result,
  password,
  is_active,
  created_at, 
  updated_at
) VALUES
-- طالب 1
(
  'student001',
  'أحمد محمد علي',
  'لبناني',
  '+961-70-123456',
  'العلوم العامة',
  'أ',
  'الصف العاشر',
  'ممتاز',
  'نجح',
  'stu123',
  1,
  NOW(),
  NOW()
),
-- طالب 2
(
  'student002',
  'فاطمة حسن أحمد',
  'لبنانية',
  '+961-71-234567',
  'الأدب والإنسانيات',
  'ب',
  'الصف التاسع',
  'جيد جداً',
  'نجح',
  'stu123',
  1,
  NOW(),
  NOW()
),
-- طالب 3
(
  'student003',
  'محمد أحمد خليل',
  'لبناني',
  '+961-76-345678',
  'العلوم العامة',
  'أ',
  'الصف الحادي عشر',
  'ممتاز',
  'نجح',
  'stu123',
  1,
  NOW(),
  NOW()
),
-- طالب 4
(
  'student004',
  'زينب علي حسن',
  'لبنانية',
  '+961-78-456789',
  'الرياضيات والفيزياء',
  'ب',
  'الصف العاشر',
  'جيد جداً',
  'نجح',
  'stu123',
  1,
  NOW(),
  NOW()
),
-- طالب 5
(
  'student005',
  'علي حسين محمد',
  'لبناني',
  '+961-79-567890',
  'العلوم العامة',
  'أ',
  'الصف الثاني عشر',
  'جيد',
  'نجح',
  'stu123',
  1,
  NOW(),
  NOW()
),
-- طالب 6
(
  'student006',
  'مريم خالد أحمد',
  'لبنانية',
  '+961-70-678901',
  'الأدب والإنسانيات',
  'أ',
  'الصف التاسع',
  'ممتاز',
  'نجح',
  'stu123',
  1,
  NOW(),
  NOW()
),
-- طالب 7
(
  'student007',
  'حسام الدين محمد',
  'لبناني',
  '+961-71-789012',
  'العلوم العامة',
  'أ',
  'الصف العاشر',
  'جيد جداً',
  'نجح',
  'stu123',
  1,
  NOW(),
  NOW()
),
-- طالب 8
(
  'student008',
  'نور الهدى علي',
  'لبنانية',
  '+961-76-890123',
  'الرياضيات والفيزياء',
  'ب',
  'الصف الحادي عشر',
  'ممتاز',
  'نجح',
  'stu123',
  1,
  NOW(),
  NOW()
);

-- ===================================
-- 3. التحقق من البيانات
-- Verify Data
-- ===================================

-- عرض الأدمن المُنشأ
SELECT 'Admin Created:' as info, username, name, email FROM users WHERE username = 'admin';

-- عرض الطلاب المُنشأين
SELECT 'Students Created:' as info, COUNT(*) as total_students FROM students;

-- عرض تفاصيل الطلاب
SELECT 
  username,
  full_name,
  nationality,
  phone,
  class,
  specialization,
  section,
  level,
  result,
  is_active
FROM students 
ORDER BY username;

-- ===================================
-- 4. معلومات تسجيل الدخول
-- Login Information
-- ===================================

/*
معلومات تسجيل الدخول:

🔐 الأدمن:
- اسم المستخدم: admin
- كلمة المرور: password

👥 الطلاب:
- student001 إلى student008
- كلمة المرور: stu123

📊 إحصائيات:
- 1 أدمن
- 8 طلاب
- جميع الطلاب نشطين (is_active = 1)
- تخصصات متنوعة: العلوم العامة، الأدب والإنسانيات، الرياضيات والفيزياء
- صفوف متنوعة: التاسع، العاشر، الحادي عشر، الثاني عشر
- شعب: أ، ب
- مستويات: ممتاز، جيد جداً، جيد
- النتائج: جميعهم نجحوا

📱 بنية الجدول:
- username: اسم المستخدم (فريد)
- full_name: الاسم الكامل
- nationality: الجنسية
- phone: رقم الهاتف
- specialization: التخصص
- section: الشعبة
- class: الصف
- level: المستوى
- result: النتيجة
- password: كلمة المرور (افتراضية: stu123)
- is_active: نشط (1) أم لا (0)
*/
