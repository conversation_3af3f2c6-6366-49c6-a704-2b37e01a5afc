@extends('layouts.app')

@section('content')
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card">
                <div class="card-header text-center" style="background: var(--costa-del-sol); color: white;">
                    <h1><i class="fas fa-bell me-2"></i>Push Notification Test</h1>
                    <p class="mb-0">اختبار الإشعارات الفورية</p>
                </div>
                <div class="card-body p-4">
                    
                    <!-- Notification Permission Status -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-info">
                                <div class="card-header bg-info text-white">
                                    <h5><i class="fas fa-info-circle me-2"></i>حالة الأذونات</h5>
                                </div>
                                <div class="card-body">
                                    <p><strong>Notification Permission:</strong> <span id="notificationPermission" class="badge bg-secondary">جاري التحقق...</span></p>
                                    <p><strong>Service Worker:</strong> <span id="serviceWorkerStatus" class="badge bg-secondary">جاري التحقق...</span></p>
                                    <p><strong>Push Manager:</strong> <span id="pushManagerStatus" class="badge bg-secondary">جاري التحقق...</span></p>
                                    <p><strong>Subscription Status:</strong> <span id="subscriptionStatus" class="badge bg-secondary">جاري التحقق...</span></p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h5><i class="fas fa-cogs me-2"></i>إجراءات سريعة</h5>
                                </div>
                                <div class="card-body">
                                    <button class="btn btn-primary btn-sm mb-2 w-100" onclick="requestNotificationPermission()">
                                        <i class="fas fa-bell me-1"></i>طلب إذن الإشعارات
                                    </button>
                                    <button class="btn btn-success btn-sm mb-2 w-100" onclick="subscribeToPush()">
                                        <i class="fas fa-plus me-1"></i>الاشتراك في الإشعارات
                                    </button>
                                    <button class="btn btn-warning btn-sm mb-2 w-100" onclick="unsubscribeFromPush()">
                                        <i class="fas fa-minus me-1"></i>إلغاء الاشتراك
                                    </button>
                                    <button class="btn btn-info btn-sm w-100" onclick="refreshStatus()">
                                        <i class="fas fa-sync me-1"></i>تحديث الحالة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Test Notifications -->
                    <div class="card border-warning mb-4">
                        <div class="card-header bg-warning text-dark">
                            <h5><i class="fas fa-test-tube me-2"></i>اختبار الإشعارات</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="testTitle" class="form-label">عنوان الإشعار</label>
                                        <input type="text" class="form-control" id="testTitle" value="اختبار الإشعارات">
                                    </div>
                                    <div class="mb-3">
                                        <label for="testMessage" class="form-label">نص الإشعار</label>
                                        <textarea class="form-control" id="testMessage" rows="3">هذا إشعار تجريبي للتأكد من عمل النظام بشكل صحيح</textarea>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">نوع الاختبار</label>
                                        <div class="d-grid gap-2">
                                            <button class="btn btn-outline-primary" onclick="testLocalNotification()">
                                                <i class="fas fa-desktop me-1"></i>إشعار محلي
                                            </button>
                                            <button class="btn btn-outline-success" onclick="testServerNotification()">
                                                <i class="fas fa-server me-1"></i>إشعار من الخادم
                                            </button>
                                            <button class="btn btn-outline-info" onclick="testBrowserNotification()">
                                                <i class="fas fa-browser me-1"></i>إشعار المتصفح
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Push Statistics -->
                    <div class="card border-primary">
                        <div class="card-header bg-primary text-white">
                            <h5><i class="fas fa-chart-bar me-2"></i>إحصائيات الإشعارات</h5>
                        </div>
                        <div class="card-body">
                            <div id="pushStats">
                                <div class="text-center">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">جاري التحميل...</span>
                                    </div>
                                    <p class="mt-2">جاري تحميل الإحصائيات...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Log Output -->
                    <div class="card border-dark mt-4">
                        <div class="card-header bg-dark text-white">
                            <h5><i class="fas fa-terminal me-2"></i>سجل الأحداث</h5>
                            <button class="btn btn-sm btn-outline-light float-end" onclick="clearLog()">مسح السجل</button>
                        </div>
                        <div class="card-body">
                            <div id="logOutput" style="height: 200px; overflow-y: auto; background: #f8f9fa; padding: 10px; font-family: monospace; font-size: 12px;">
                                <div class="text-muted">سجل الأحداث سيظهر هنا...</div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
        refreshStatus();
        loadPushStats();
        log('تم تحميل صفحة اختبار الإشعارات');
    });

    // Logging function
    function log(message) {
        const logOutput = document.getElementById('logOutput');
        const timestamp = new Date().toLocaleTimeString('ar-SA');
        const logEntry = document.createElement('div');
        logEntry.innerHTML = `<span class="text-muted">[${timestamp}]</span> ${message}`;
        logOutput.appendChild(logEntry);
        logOutput.scrollTop = logOutput.scrollHeight;
    }

    function clearLog() {
        document.getElementById('logOutput').innerHTML = '<div class="text-muted">سجل الأحداث سيظهر هنا...</div>';
    }

    // Status checking functions
    async function refreshStatus() {
        log('🔄 تحديث حالة الإشعارات...');
        
        // Check notification permission
        const notificationPermission = Notification.permission;
        updateStatus('notificationPermission', notificationPermission, getPermissionBadgeClass(notificationPermission));
        
        // Check service worker
        const swSupported = 'serviceWorker' in navigator;
        updateStatus('serviceWorkerStatus', swSupported ? 'مدعوم' : 'غير مدعوم', swSupported ? 'bg-success' : 'bg-danger');
        
        // Check push manager
        const pushSupported = 'PushManager' in window;
        updateStatus('pushManagerStatus', pushSupported ? 'مدعوم' : 'غير مدعوم', pushSupported ? 'bg-success' : 'bg-danger');
        
        // Check subscription status
        if (swSupported && pushSupported) {
            try {
                const registration = await navigator.serviceWorker.ready;
                const subscription = await registration.pushManager.getSubscription();
                updateStatus('subscriptionStatus', subscription ? 'مشترك' : 'غير مشترك', subscription ? 'bg-success' : 'bg-warning');
                
                if (subscription) {
                    log('✅ المستخدم مشترك في الإشعارات الفورية');
                } else {
                    log('⚠️ المستخدم غير مشترك في الإشعارات الفورية');
                }
            } catch (error) {
                updateStatus('subscriptionStatus', 'خطأ', 'bg-danger');
                log('❌ خطأ في فحص حالة الاشتراك: ' + error.message);
            }
        } else {
            updateStatus('subscriptionStatus', 'غير متاح', 'bg-secondary');
        }
    }

    function updateStatus(elementId, text, badgeClass) {
        const element = document.getElementById(elementId);
        element.textContent = text;
        element.className = `badge ${badgeClass}`;
    }

    function getPermissionBadgeClass(permission) {
        switch (permission) {
            case 'granted': return 'bg-success';
            case 'denied': return 'bg-danger';
            case 'default': return 'bg-warning';
            default: return 'bg-secondary';
        }
    }

    // Permission and subscription functions
    async function requestNotificationPermission() {
        log('📋 طلب إذن الإشعارات...');
        
        try {
            const permission = await Notification.requestPermission();
            log(`📋 نتيجة طلب الإذن: ${permission}`);
            refreshStatus();
        } catch (error) {
            log('❌ خطأ في طلب الإذن: ' + error.message);
        }
    }

    async function subscribeToPush() {
        log('📱 محاولة الاشتراك في الإشعارات الفورية...');
        
        if (Notification.permission !== 'granted') {
            log('⚠️ يجب منح إذن الإشعارات أولاً');
            await requestNotificationPermission();
            return;
        }

        try {
            // Call the global function from the main layout
            if (typeof subscribeToPushNotifications === 'function') {
                await subscribeToPushNotifications();
                log('✅ تم الاشتراك في الإشعارات الفورية بنجاح');
            } else {
                log('❌ وظيفة الاشتراك غير متوفرة');
            }
            refreshStatus();
        } catch (error) {
            log('❌ خطأ في الاشتراك: ' + error.message);
        }
    }

    async function unsubscribeFromPush() {
        log('📱 محاولة إلغاء الاشتراك من الإشعارات الفورية...');
        
        try {
            const registration = await navigator.serviceWorker.ready;
            const subscription = await registration.pushManager.getSubscription();
            
            if (subscription) {
                await subscription.unsubscribe();
                log('✅ تم إلغاء الاشتراك من الإشعارات الفورية');
            } else {
                log('⚠️ لا يوجد اشتراك لإلغائه');
            }
            
            refreshStatus();
        } catch (error) {
            log('❌ خطأ في إلغاء الاشتراك: ' + error.message);
        }
    }

    // Test notification functions
    function testBrowserNotification() {
        log('🔔 اختبار إشعار المتصفح...');
        
        if (Notification.permission !== 'granted') {
            log('❌ إذن الإشعارات غير ممنوح');
            return;
        }

        const title = document.getElementById('testTitle').value;
        const message = document.getElementById('testMessage').value;

        const notification = new Notification(title, {
            body: message,
            icon: '/icons/icon-192x192.png',
            badge: '/icons/icon-72x72.png',
            tag: 'test-notification'
        });

        notification.onclick = function() {
            log('👆 تم النقر على الإشعار');
            notification.close();
        };

        log('✅ تم إرسال إشعار المتصفح');
    }

    function testLocalNotification() {
        log('🔔 اختبار الإشعار المحلي...');
        
        // Call the global test function from the main layout
        if (typeof testPushNotification === 'function') {
            testPushNotification();
            log('✅ تم طلب إرسال إشعار محلي');
        } else {
            log('❌ وظيفة الاختبار غير متوفرة');
        }
    }

    async function testServerNotification() {
        log('🔔 اختبار إشعار من الخادم...');
        
        const title = document.getElementById('testTitle').value;
        const message = document.getElementById('testMessage').value;
        
        try {
            const userType = getCurrentUserType();
            const userId = getCurrentUserId();
            
            if (!userType || !userId) {
                log('❌ يجب تسجيل الدخول أولاً');
                return;
            }

            const response = await fetch('/api/push/test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    user_type: userType,
                    user_id: userId,
                    title: title,
                    body: message
                })
            });

            const data = await response.json();
            
            if (data.success) {
                log('✅ تم إرسال الإشعار من الخادم بنجاح');
                log(`📊 تم الإرسال إلى: ${data.sent} مستخدم`);
                if (data.errors && data.errors.length > 0) {
                    log(`⚠️ أخطاء: ${data.errors.length}`);
                }
            } else {
                log('❌ فشل في إرسال الإشعار من الخادم: ' + data.message);
            }
        } catch (error) {
            log('❌ خطأ في إرسال الإشعار من الخادم: ' + error.message);
        }
    }

    // Load push statistics
    async function loadPushStats() {
        try {
            const response = await fetch('/api/push/stats');
            const data = await response.json();
            
            if (data.success) {
                displayPushStats(data.stats);
            } else {
                document.getElementById('pushStats').innerHTML = '<div class="alert alert-warning">فشل في تحميل الإحصائيات</div>';
            }
        } catch (error) {
            document.getElementById('pushStats').innerHTML = '<div class="alert alert-danger">خطأ في تحميل الإحصائيات</div>';
            log('❌ خطأ في تحميل الإحصائيات: ' + error.message);
        }
    }

    function displayPushStats(stats) {
        const html = `
            <div class="row text-center">
                <div class="col-md-3">
                    <div class="card border-primary">
                        <div class="card-body">
                            <h3 class="text-primary">${stats.total_subscriptions}</h3>
                            <p class="mb-0">إجمالي الاشتراكات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-success">
                        <div class="card-body">
                            <h3 class="text-success">${stats.active_subscriptions}</h3>
                            <p class="mb-0">الاشتراكات النشطة</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card border-info">
                        <div class="card-body">
                            <h3 class="text-info">${stats.students}</h3>
                            <p class="mb-0">الطلاب</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card border-warning">
                        <div class="card-body">
                            <h3 class="text-warning">${stats.employees}</h3>
                            <p class="mb-0">الموظفون</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card border-secondary">
                        <div class="card-body">
                            <h3 class="text-secondary">${stats.admins}</h3>
                            <p class="mb-0">المدراء</p>
                        </div>
                    </div>
                </div>
            </div>
        `;
        document.getElementById('pushStats').innerHTML = html;
    }

    // Helper functions (these should match the ones in the main layout)
    function getCurrentUserType() {
        const path = window.location.pathname;
        if (path.includes('/student/')) return 'student';
        if (path.includes('/employee/')) return 'employee';
        if (path.includes('/admin/') || path.includes('/dashboard')) return 'admin';
        return 'admin'; // Default for testing
    }

    function getCurrentUserId() {
        @if(Auth::guard('student')->check())
            return {{ Auth::guard('student')->id() }};
        @elseif(Auth::guard('employee')->check())
            return {{ Auth::guard('employee')->id() }};
        @elseif(Auth::check())
            return {{ Auth::id() }};
        @else
            return 1; // Default for testing
        @endif
    }
</script>
@endsection
