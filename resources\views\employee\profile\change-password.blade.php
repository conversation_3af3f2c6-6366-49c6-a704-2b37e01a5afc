@extends('layouts.app')

@section('styles')
<style>
    .profile-card {
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        overflow: hidden;
    }
    
    .profile-header {
        padding: 1.5rem;
        background-color: #f8f9fa;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .profile-body {
        padding: 1.5rem;
    }
    
    .profile-title {
        color: var(--costa-del-sol);
        font-weight: 600;
        margin-bottom: 0;
    }
    
    .primary-btn {
        background-color: var(--costa-del-sol);
        color: white;
        border-radius: 20px;
        padding: 0.5rem 1.5rem;
        border: none;
        transition: background-color 0.2s ease;
    }
    
    .primary-btn:hover {
        background-color: var(--locust);
        color: white;
    }
    
    .secondary-btn {
        background-color: white;
        color: var(--costa-del-sol);
        border-radius: 20px;
        border: 1px solid var(--costa-del-sol);
        padding: 0.5rem 1.5rem;
        text-decoration: none;
        transition: background-color 0.2s ease, color 0.2s ease;
    }
    
    .secondary-btn:hover {
        background-color: var(--costa-del-sol);
        color: white;
    }

    /* Responsive Design */
    @media (max-width: 992px) {
        .container-fluid.main-content {
            padding-left: 1rem !important;
            padding-right: 1rem !important;
        }

        .col-md-9 {
            max-width: 100%;
            flex: 0 0 100%;
        }
    }

    @media (max-width: 768px) {
        .container-fluid.main-content {
            padding-left: 0.75rem !important;
            padding-right: 0.75rem !important;
        }

        /* Header responsive */
        .d-flex.justify-content-between.align-items-center {
            flex-direction: column;
            align-items: flex-start !important;
            gap: 1rem;
        }

        .d-flex.justify-content-between.align-items-center > div:last-child {
            align-self: flex-end;
        }

        /* Make secondary button smaller on mobile */
        .secondary-btn {
            padding: 0.4rem 1rem !important;
            font-size: 0.8rem !important;
            border-radius: 15px !important;
        }

        .secondary-btn i {
            font-size: 0.75rem;
        }

        .profile-card {
            border-radius: 12px;
        }

        .profile-header {
            padding: 1rem;
        }

        .profile-body {
            padding: 1rem;
        }

        .profile-title {
            font-size: 1rem;
        }

        .form-control {
            padding: 0.4rem 0.7rem;
            font-size: 0.85rem;
        }

        .form-label {
            font-size: 0.8rem;
            margin-bottom: 0.25rem;
        }

        .primary-btn {
            padding: 0.6rem 1.25rem;
            font-size: 0.9rem;
        }
    }

    @media (max-width: 576px) {
        .container-fluid.main-content {
            padding-left: 0.5rem !important;
            padding-right: 0.5rem !important;
        }

        /* Even smaller button for very small screens */
        .secondary-btn {
            padding: 0.3rem 0.8rem !important;
            font-size: 0.75rem !important;
            border-radius: 12px !important;
        }

        .secondary-btn i {
            font-size: 0.7rem;
        }

        h2 {
            font-size: 1.4rem !important;
        }

        .profile-header {
            padding: 0.75rem;
        }

        .profile-body {
            padding: 0.75rem;
        }

        .profile-title {
            font-size: 0.9rem;
        }

        .form-control {
            padding: 0.35rem 0.6rem;
            font-size: 0.8rem;
        }

        .form-label {
            font-size: 0.75rem;
        }

        .primary-btn {
            padding: 0.5rem 1rem;
            font-size: 0.85rem;
        }

        .alert {
            padding: 0.5rem;
            font-size: 0.8rem;
        }
    }
    
    .form-control:focus {
        border-color: var(--costa-del-sol);
        box-shadow: 0 0 0 0.25rem rgba(125, 145, 120, 0.25);
    }
    
    .form-label {
        color: var(--mid-gray);
        font-weight: 500;
    }
</style>
@endsection

@section('content')
<div class="container-fluid main-content px-4">
    <div class="row">
        <div class="col-md-9">
            <div class="d-flex justify-content-between align-items-start align-items-md-center mb-4 mt-2 flex-column flex-md-row gap-3 gap-md-0">
                <div class="flex-grow-1">
                    <h2 class="mb-1 h3 h2-md" style="color: var(--costa-del-sol); font-weight: 700;">
                        <i class="fas fa-key me-2"></i> Change Password
                    </h2>
                    <p class="mb-0 text-muted small">
                        Update your account password for better security
                    </p>
                </div>
                <div class="align-self-end align-self-md-auto">
                    <a href="{{ route('employee.profile.edit') }}" class="secondary-btn">
                        <i class="fas fa-arrow-left me-1"></i>
                        <span class="d-none d-sm-inline">Back to </span>Profile
                    </a>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-8">
                    <!-- Change Password Form -->
                    <div class="card profile-card mb-4">
                        <div class="profile-header">
                            <h5 class="profile-title"><i class="fas fa-lock me-2" style="color: var(--locust);"></i>Password Security</h5>
                        </div>
                        <div class="profile-body">
                            @if(session('success'))
                                <div class="alert alert-success alert-dismissible fade show" role="alert">
                                    {{ session('success') }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            @endif
                            
                            @if($errors->any())
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    <ul class="mb-0">
                                        @foreach($errors->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            @endif
                            
                            <form action="{{ route('employee.profile.update-password') }}" method="POST">
                                @csrf
                                @method('PUT')
                                
                                <div class="mb-3">
                                    <label for="current_password" class="form-label">Current Password</label>
                                    <input type="password" class="form-control @error('current_password') is-invalid @enderror" 
                                           id="current_password" name="current_password">
                                    @error('current_password')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="mb-3">
                                    <label for="password" class="form-label">New Password</label>
                                    <input type="password" class="form-control @error('password') is-invalid @enderror"
                                           id="password" name="password">
                                    @error('password')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="text-muted">Password must be at least 6 characters long</small>
                                </div>
                                
                                <div class="mb-4">
                                    <label for="password_confirmation" class="form-label">Confirm New Password</label>
                                    <input type="password" class="form-control"
                                           id="password_confirmation" name="password_confirmation">
                                </div>
                                
                                <div class="d-grid">
                                    <button type="submit" class="primary-btn">
                                        <i class="fas fa-save me-1"></i> Update Password
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <div class="card profile-card">
                        <div class="profile-header">
                            <h5 class="profile-title"><i class="fas fa-shield-alt me-2" style="color: var(--locust);"></i>Password Security Tips</h5>
                        </div>
                        <div class="profile-body">
                            <ul class="mb-0">
                                <li class="mb-2">Use a minimum of 8 characters</li>
                                <li class="mb-2">Include at least one uppercase letter</li>
                                <li class="mb-2">Include at least one number</li>
                                <li class="mb-2">Include at least one special character</li>
                                <li class="mb-2">Avoid using personal information</li>
                                <li>Don't reuse passwords across different sites</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
