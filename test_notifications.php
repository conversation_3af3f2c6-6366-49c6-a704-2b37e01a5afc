<?php

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/appnote-api/public/api/employee/notifications');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Accept: application/json']);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

echo "HTTP Code: $httpCode\n";
if ($response) {
    $data = json_decode($response, true);
    if ($data && isset($data['data']) && is_array($data['data']) && count($data['data']) > 0) {
        echo "First notification:\n";
        print_r($data['data'][0]);
    } else {
        echo "Response: $response\n";
    }
} else {
    echo "No response\n";
}

curl_close($ch);
