<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Notification;

echo "=== Testing Notification Attachments ===\n\n";

// Get all notifications with attachments
$notifications = Notification::whereNotNull('attachment_path')->get();

echo "Found " . $notifications->count() . " notifications with attachments:\n\n";

foreach ($notifications as $notification) {
    echo "Notification ID: {$notification->id}\n";
    echo "Title: {$notification->title}\n";
    
    // Test attachment_path
    echo "Attachment Path (raw): " . var_export($notification->getAttributes()['attachment_path'], true) . "\n";
    echo "Attachment Path (cast): " . var_export($notification->attachment_path, true) . "\n";
    
    // Test attachment_name
    echo "Attachment Name (raw): " . var_export($notification->getAttributes()['attachment_name'], true) . "\n";
    echo "Attachment Name (cast): " . var_export($notification->attachment_name, true) . "\n";
    
    // Test count
    $paths = $notification->attachment_path ?? [];
    echo "Attachment Count: " . count($paths) . "\n";
    
    echo "---\n\n";
}

// Test creating a new notification with attachments
echo "Creating test notification with attachments...\n";

$testNotification = new Notification();
$testNotification->title = "Test Attachment Notification";
$testNotification->message = "This is a test notification with attachments";
$testNotification->sender_type = "admin";
$testNotification->sender_id = 1;
$testNotification->sender_name = "Test Admin";
$testNotification->recipient_type = "all";
$testNotification->recipient_ids = [1, 2, 3];
$testNotification->attachment_path = ["notifications/test1.pdf", "notifications/test2.docx"];
$testNotification->attachment_name = ["Test Document 1.pdf", "Test Document 2.docx"];
$testNotification->priority = "medium";
$testNotification->status = "sent";
$testNotification->save();

echo "Test notification created with ID: {$testNotification->id}\n";

// Retrieve and test the created notification
$retrievedNotification = Notification::find($testNotification->id);
echo "Retrieved notification:\n";
echo "Attachment Path: " . var_export($retrievedNotification->attachment_path, true) . "\n";
echo "Attachment Name: " . var_export($retrievedNotification->attachment_name, true) . "\n";
echo "Attachment Count: " . count($retrievedNotification->attachment_path ?? []) . "\n";

echo "\n✅ Test completed successfully!\n";
