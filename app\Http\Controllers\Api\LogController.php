<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;

class LogController extends Controller
{
    /**
     * Log files directory
     */
    protected $logPath;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->logPath = storage_path('logs');
    }

    /**
     * Get all log files.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        try {
            $logFiles = collect(File::files($this->logPath))->map(function ($file) {
                return [
                    'name' => $file->getFilename(),
                    'size' => $this->formatFileSize($file->getSize()),
                    'size_bytes' => $file->getSize(),
                    'modified' => date('Y-m-d H:i:s', $file->getMTime()),
                    'timestamp' => $file->getMTime(),
                ];
            })->sortByDesc('timestamp')->values();

            return response()->json([
                'success' => true,
                'data' => $logFiles,
                'count' => $logFiles->count()
            ]);
        } catch (\Exception $e) {
            Log::error('❌ Error retrieving log files: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving log files: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show the specified log file.
     *
     * @param string $filename
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(string $filename)
    {
        try {
            $filePath = $this->logPath . '/' . $filename;
            
            if (!File::exists($filePath)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Log file not found'
                ], 404);
            }

            $content = File::get($filePath);
            $formattedContent = $this->parseLogContent($content);

            return response()->json([
                'success' => true,
                'data' => [
                    'filename' => $filename,
                    'content' => $formattedContent,
                    'modified' => date('Y-m-d H:i:s', File::lastModified($filePath)),
                    'size' => $this->formatFileSize(File::size($filePath)),
                    'size_bytes' => File::size($filePath),
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('❌ Error retrieving log file: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving log file: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete the specified log file.
     *
     * @param string $filename
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(string $filename)
    {
        try {
            $filePath = $this->logPath . '/' . $filename;
            
            if (!File::exists($filePath)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Log file not found'
                ], 404);
            }

            File::delete($filePath);
            
            return response()->json([
                'success' => true,
                'message' => 'Log file deleted successfully',
                'data' => [
                    'filename' => $filename
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('❌ Error deleting log file: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error deleting log file: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Format file size to readable format
     */
    protected function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * Parse log content to format for display
     */
    protected function parseLogContent(string $content): array
    {
        $pattern = '/\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\]/';
        $logEntries = preg_split($pattern, $content, -1, PREG_SPLIT_DELIM_CAPTURE | PREG_SPLIT_NO_EMPTY);
        
        $formattedEntries = [];
        $datePattern = '/\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\]/';
        
        foreach ($logEntries as $index => $entry) {
            if (preg_match($datePattern, $entry, $matches)) {
                $date = $matches[1];
                $nextIndex = $index + 1;
                
                if (isset($logEntries[$nextIndex])) {
                    $level = '';
                    if (strpos($logEntries[$nextIndex], '.ERROR') !== false) {
                        $level = 'error';
                    } elseif (strpos($logEntries[$nextIndex], '.WARNING') !== false) {
                        $level = 'warning';
                    } elseif (strpos($logEntries[$nextIndex], '.INFO') !== false) {
                        $level = 'info';
                    } elseif (strpos($logEntries[$nextIndex], '.DEBUG') !== false) {
                        $level = 'debug';
                    }
                    
                    $formattedEntries[] = [
                        'date' => $date,
                        'content' => $logEntries[$nextIndex],
                        'level' => $level
                    ];
                }
            }
        }
        
        return array_reverse($formattedEntries);
    }
}
