# خطوط التطبيق - App Fonts

## خط Cairo المطلوب / Required Cairo Font

يحتاج التطبيق إلى خط Cairo للحصول على أفضل عرض للنصوص العربية والإنجليزية.

### تحميل الخط / Download Font:

1. **من Google Fonts:**
   - اذهب إلى: https://fonts.google.com/specimen/Cairo
   - اضغط على "Download family"
   - استخرج الملفات

2. **الملفات المطلوبة / Required Files:**
   - `Cairo-Regular.ttf` (وزن 400)
   - `Cairo-Medium.ttf` (وزن 500)
   - `Cairo-SemiBold.ttf` (وزن 600)
   - `Cairo-Bold.ttf` (وزن 700)

3. **ضع الملفات هنا / Place Files Here:**
   ```
   assets/fonts/
   ├── Cairo-Regular.ttf
   ├── Cairo-Medium.ttf
   ├── Cairo-SemiBold.ttf
   └── Cairo-Bold.ttf
   ```

### مميزات خط Cairo / Cairo Font Features:

- ✅ دعم ممتاز للعربية والإنجليزية
- ✅ تصميم عصري وواضح
- ✅ متعدد الأوزان
- ✅ مناسب للشاشات الرقمية
- ✅ مفتوح المصدر

### الحالة الحالية / Current Status:

❌ **الخطوط غير موجودة حالياً**
- التطبيق يستخدم الخط الافتراضي للنظام
- بعد إضافة ملفات Cairo، سيتم تطبيق الخط تلقائياً

✅ **بعد إضافة الخطوط:**
- سيظهر النص العربي بشكل أجمل
- تحسن في قراءة النصوص
- تناسق أفضل مع تصميم التطبيق

### ملاحظة مهمة / Important Note:

إذا لم تتمكن من الحصول على ملفات الخط، سيعمل التطبيق بشكل طبيعي باستخدام الخط الافتراضي للنظام.
