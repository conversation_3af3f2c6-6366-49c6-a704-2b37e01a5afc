<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Notification;
use App\Models\NotificationRecipient;
use App\Models\Student;

echo "=== Creating Test Notification with Attachment ===\n\n";

// Create a test notification with attachments
$notification = new Notification();
$notification->title = "Test Notification with Attachment";
$notification->message = "This is a test notification that includes file attachments. Please download and review the attached documents.";
$notification->sender_type = "admin";
$notification->sender_id = 1;
$notification->sender_name = "Test Admin";
$notification->recipient_type = "students";
$notification->recipient_ids = [1, 2, 3]; // First 3 students
$notification->priority = "high";
$notification->status = "sent";

// Add test attachments (these will be arrays thanks to our cast)
$notification->attachment_path = [
    "notifications/test_document.pdf",
    "notifications/sample_file.docx"
];
$notification->attachment_name = [
    "Test Document.pdf",
    "Sample File.docx"
];

$notification->save();

echo "✅ Test notification created with ID: {$notification->id}\n";
echo "Title: {$notification->title}\n";
echo "Attachment Paths: " . json_encode($notification->attachment_path) . "\n";
echo "Attachment Names: " . json_encode($notification->attachment_name) . "\n";

// Create notification recipients for the first 3 students
$students = Student::take(3)->get();
foreach ($students as $student) {
    $recipient = new NotificationRecipient();
    $recipient->notification_id = $notification->id;
    $recipient->recipient_id = $student->id;
    $recipient->recipient_type = 'student';
    $recipient->recipient_name = $student->full_name;
    $recipient->recipient_class = $student->class ?? 'N/A';
    $recipient->read_at = null;
    $recipient->status = 'sent';
    $recipient->sent_at = now();
    $recipient->save();
    
    echo "✅ Recipient added: {$student->full_name} (ID: {$student->id})\n";
}

echo "\n=== Testing Attachment Display ===\n";

// Test how the notification looks when retrieved
$testNotification = Notification::find($notification->id);
echo "Retrieved notification:\n";
echo "ID: {$testNotification->id}\n";
echo "Title: {$testNotification->title}\n";
echo "Attachment Path (type): " . gettype($testNotification->attachment_path) . "\n";
echo "Attachment Path (value): " . var_export($testNotification->attachment_path, true) . "\n";
echo "Attachment Name (type): " . gettype($testNotification->attachment_name) . "\n";
echo "Attachment Name (value): " . var_export($testNotification->attachment_name, true) . "\n";

// Test the condition used in views
$attachmentPaths = $testNotification->attachment_path ?? [];
if (!is_array($attachmentPaths)) {
    $attachmentPaths = [$attachmentPaths];
}
$hasAttachment = !empty($attachmentPaths) && !empty(array_filter($attachmentPaths));

echo "Has Attachment (condition result): " . ($hasAttachment ? "YES" : "NO") . "\n";
echo "Attachment Count: " . count($attachmentPaths) . "\n";

if ($hasAttachment) {
    echo "\nAttachment Details:\n";
    foreach ($attachmentPaths as $index => $path) {
        $name = $testNotification->attachment_name[$index] ?? "Unknown";
        echo "  {$index}: {$path} -> {$name}\n";
    }
}

echo "\n✅ Test completed! Check the notification in the web interface.\n";
echo "Notification ID to look for: {$notification->id}\n";
