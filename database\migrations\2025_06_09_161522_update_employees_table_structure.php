<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop the existing employees table and recreate it with the new structure
        Schema::dropIfExists('employees');
        
        Schema::create('employees', function (Blueprint $table) {
            $table->id();
           
            $table->string('full_name');
            $table->string('contract_type');
            $table->string('employee_type');
            $table->string('phone')->nullable();
            $table->string('job_status');
            $table->string('automatic_number')->nullable();
            $table->string('financial_number')->nullable();
            $table->string('state_cooperative_number')->nullable();
            $table->string('bank_account_number')->nullable();
            $table->string('username')->unique();
            $table->string('password');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This will drop the new table structure
        Schema::dropIfExists('employees');
        
        // If you need to recreate the original table structure, you would do that here
        // For now, we'll just drop it since we're replacing it completely
    }
};
