<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\StudentController;
use App\Http\Controllers\EmployeeController;
use App\Http\Controllers\NotificationController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Test route
Route::get('/test-simple', function() {
    return response()->json([
        'success' => true,
        'message' => 'Web test successful',
        'timestamp' => now()
    ]);
});

// PWA Test route
Route::get('/pwa-test', function() {
    return view('pwa-test');
});

// Push Notification Test route
Route::get('/push-test', function() {
    return view('push-test');
});

// Test employee notifications
Route::get('/test-employee-notifications', function () {
    $employees = \App\Models\Employee::select('id', 'username', 'full_name')->get();

    $result = [];
    foreach ($employees as $employee) {
        $notifications = \App\Models\NotificationRecipient::where('recipient_id', $employee->id)
            ->where('recipient_type', 'employee')
            ->with('notification')
            ->count();

        $unread = \App\Models\NotificationRecipient::where('recipient_id', $employee->id)
            ->where('recipient_type', 'employee')
            ->whereNull('read_at')
            ->count();

        $result[] = [
            'employee' => $employee->full_name,
            'username' => $employee->username,
            'id' => $employee->id,
            'total_notifications' => $notifications,
            'unread_notifications' => $unread,
        ];
    }

    return response()->json([
        'success' => true,
        'employees' => $result,
        'total_employees' => count($employees)
    ]);
});

// Test employee login and dashboard
Route::get('/test-employee-login/{employeeId}', function ($employeeId) {
    $employee = \App\Models\Employee::find($employeeId);

    if (!$employee) {
        return response()->json(['error' => 'Employee not found']);
    }

    // Login the employee
    Auth::guard('employee')->login($employee);

    // Get notifications for this employee
    $notifications = \App\Models\NotificationRecipient::where('recipient_id', $employee->id)
        ->where('recipient_type', 'employee')
        ->with('notification')
        ->orderBy('created_at', 'desc')
        ->limit(5)
        ->get();

    $unread = \App\Models\NotificationRecipient::where('recipient_id', $employee->id)
        ->where('recipient_type', 'employee')
        ->whereNull('read_at')
        ->count();

    return response()->json([
        'success' => true,
        'logged_in_employee' => [
            'id' => $employee->id,
            'name' => $employee->full_name,
            'username' => $employee->username,
        ],
        'auth_check' => Auth::guard('employee')->check(),
        'auth_user' => Auth::guard('employee')->user() ? Auth::guard('employee')->user()->full_name : null,
        'notifications_count' => $notifications->count(),
        'unread_count' => $unread,
        'recent_notifications' => $notifications->map(function($recipient) {
            return [
                'title' => $recipient->notification->title,
                'read_at' => $recipient->read_at,
                'created_at' => $recipient->created_at,
            ];
        }),
        'dashboard_url' => route('employee.dashboard'),
    ]);
});

// Create test notifications for all employees
Route::get('/create-test-notifications', function () {
    $employees = \App\Models\Employee::all();
    $created = 0;

    foreach ($employees as $employee) {
        // Create a test notification
        $notification = \App\Models\Notification::create([
            'title' => "Test notification for {$employee->full_name}",
            'message' => "This is a test notification for employee {$employee->full_name}",
            'sender_type' => 'admin',
            'sender_id' => 1,
            'sender_name' => 'System Admin',
            'target_audience' => 'employees',
            'priority' => 'Medium',
            'status' => 'sent',
            'is_active' => true,
        ]);

        // Create recipient record
        \App\Models\NotificationRecipient::create([
            'notification_id' => $notification->id,
            'recipient_id' => $employee->id,
            'recipient_type' => 'employee',
            'read_at' => null, // Unread
        ]);

        $created++;
    }

    return response()->json([
        'success' => true,
        'message' => "Created $created test notifications",
        'employees_count' => $employees->count(),
    ]);
});

// Test notification sending to students only
Route::get('/test-student-notification', function () {
    try {
        // Create a test notification for students only
        $notification = \App\Models\Notification::create([
            'title' => 'Test Student Notification',
            'message' => 'This notification should only go to students',
            'sender_type' => 'admin',
            'sender_id' => 1,
            'sender_name' => 'Test Admin',
            'target_audience' => 'students',
            'priority' => 'Medium',
            'status' => 'sent',
            'is_active' => true,
            'recipient_type' => 'all_students',
        ]);

        // Get all students
        $students = \App\Models\Student::where('is_active', 1)->get();

        $recipientCount = 0;
        foreach ($students as $student) {
            \App\Models\NotificationRecipient::create([
                'notification_id' => $notification->id,
                'recipient_id' => $student->id,
                'recipient_type' => 'student',
                'recipient_name' => $student->full_name,
                'recipient_class' => $student->class,
                'read_at' => null,
                'status' => 'sent',
                'sent_at' => now(),
            ]);
            $recipientCount++;
        }

        return response()->json([
            'success' => true,
            'message' => "Test notification sent to $recipientCount students only",
            'notification_id' => $notification->id,
            'recipients' => $students->map(function($student) {
                return [
                    'id' => $student->id,
                    'name' => $student->full_name,
                    'class' => $student->class,
                    'type' => 'student'
                ];
            })
        ]);

    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage()
        ], 500);
    }
});

// Check notification recipients
Route::get('/check-notification-recipients/{notificationId?}', function ($notificationId = null) {
    try {
        if ($notificationId) {
            // Check specific notification
            $notification = \App\Models\Notification::find($notificationId);
            if (!$notification) {
                return response()->json(['error' => 'Notification not found']);
            }

            $recipients = \App\Models\NotificationRecipient::where('notification_id', $notificationId)
                ->get()
                ->groupBy('recipient_type');

            $result = [
                'notification' => [
                    'id' => $notification->id,
                    'title' => $notification->title,
                    'recipient_type' => $notification->recipient_type,
                    'target_audience' => $notification->target_audience,
                ],
                'recipients_by_type' => []
            ];

            foreach ($recipients as $type => $typeRecipients) {
                $result['recipients_by_type'][$type] = [
                    'count' => $typeRecipients->count(),
                    'recipients' => $typeRecipients->map(function($recipient) {
                        return [
                            'id' => $recipient->recipient_id,
                            'name' => $recipient->recipient_name,
                            'class' => $recipient->recipient_class,
                            'type' => $recipient->recipient_type,
                        ];
                    })
                ];
            }

            return response()->json($result);
        } else {
            // Check recent notifications
            $recentNotifications = \App\Models\Notification::orderBy('created_at', 'desc')
                ->limit(5)
                ->get();

            $result = [];
            foreach ($recentNotifications as $notification) {
                $recipients = \App\Models\NotificationRecipient::where('notification_id', $notification->id)
                    ->get()
                    ->groupBy('recipient_type');

                $recipientSummary = [];
                foreach ($recipients as $type => $typeRecipients) {
                    $recipientSummary[$type] = $typeRecipients->count();
                }

                $result[] = [
                    'id' => $notification->id,
                    'title' => $notification->title,
                    'recipient_type' => $notification->recipient_type,
                    'target_audience' => $notification->target_audience,
                    'recipients_summary' => $recipientSummary,
                    'created_at' => $notification->created_at,
                ];
            }

            return response()->json([
                'success' => true,
                'recent_notifications' => $result
            ]);
        }
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage()
        ], 500);
    }
});

// Employee dashboard route (working)
Route::get('/api/employee/dashboard', function() {
    try {
        // Get employee ID from request header or use first employee for testing
        $employeeId = request()->header('X-Employee-ID');
        $employeeUsername = request()->header('X-Employee-Username');

        $employee = null;

        if ($employeeId) {
            $employee = \App\Models\Employee::find($employeeId);
        } elseif ($employeeUsername) {
            $employee = \App\Models\Employee::where('username', $employeeUsername)->first();
        } else {
            // Fallback to first employee for testing
            $employee = \App\Models\Employee::first();
        }

        if (!$employee) {
            return response()->json([
                'success' => false,
                'message' => 'لا يوجد موظف بهذه البيانات'
            ], 404);
        }

        // Get real notifications count for this specific employee
        $totalNotifications = \App\Models\NotificationRecipient::where('recipient_id', $employee->id)
            ->where('recipient_type', 'employee')
            ->count();

        $unreadNotifications = \App\Models\NotificationRecipient::where('recipient_id', $employee->id)
            ->where('recipient_type', 'employee')
            ->whereNull('read_at')
            ->count();

        return response()->json([
            'success' => true,
            'data' => [
                'employee_info' => [
                    'name' => $employee->full_name,
                    'username' => $employee->username,
                    'employee_type' => $employee->employee_type,
                    'contract_type' => $employee->contract_type,
                    'job_status' => $employee->job_status,
                    'phone' => $employee->phone,
                ],
                'notifications' => [
                    'total' => $totalNotifications,
                    'unread' => $unreadNotifications,
                ],
                'work_info' => [
                    'employee_type' => $employee->employee_type,
                    'contract_type' => $employee->contract_type,
                    'job_status' => $employee->job_status,
                    'automatic_number' => $employee->automatic_number,
                    'financial_number' => $employee->financial_number,
                    'state_cooperative_number' => $employee->state_cooperative_number,
                    'bank_account_number' => $employee->bank_account_number,
                ],
            ],
            'message' => 'تم تحميل بيانات لوحة التحكم بنجاح'
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'خطأ: ' . $e->getMessage()
        ], 500);
    }
});

// Employee notifications route
Route::get('/api/employee/notifications', function() {
    try {
        $page = request()->get('page', 1);
        $perPage = request()->get('per_page', 20);
        $status = request()->get('status');

        // Get employee info from headers
        $employeeId = request()->header('X-Employee-ID');
        $employeeUsername = request()->header('X-Employee-Username');

        if (config('app.debug')) {
            error_log("Employee notifications request - ID: $employeeId, Username: $employeeUsername");
        }

        // Find the employee
        $employee = null;
        if ($employeeId) {
            $employee = \App\Models\Employee::find($employeeId);
        } elseif ($employeeUsername) {
            $employee = \App\Models\Employee::where('username', $employeeUsername)->first();
        } else {
            // Fallback to first employee for testing
            $employee = \App\Models\Employee::first();
        }

        if (!$employee) {
            return response()->json([
                'success' => false,
                'message' => 'لا يوجد موظف بهذه البيانات'
            ], 404);
        }

        // Get notifications for this specific employee using NotificationRecipient table
        $query = \App\Models\NotificationRecipient::where('recipient_id', $employee->id)
            ->where('recipient_type', 'employee')
            ->with('notification')
            ->orderBy('created_at', 'desc');

        if ($status) {
            if ($status === 'unread') {
                $query->whereNull('read_at');
            } elseif ($status === 'read') {
                $query->whereNotNull('read_at');
            }
        }

        $recipients = $query->paginate($perPage, ['*'], 'page', $page);

        $data = $recipients->items();
        $formattedData = [];

        foreach ($data as $recipient) {
            $notification = $recipient->notification;
            if ($notification) {
                $formattedData[] = [
                    'id' => $notification->id,
                    'title' => $notification->title,
                    'message' => $notification->message,
                    'content' => $notification->content ?? $notification->message,
                    'type' => $notification->type ?? 'info',
                    'priority' => $notification->priority ?? 'medium',
                    'target_audience' => $notification->target_audience ?? 'employees',
                    'is_read' => !is_null($recipient->read_at),
                    'is_active' => $notification->is_active ?? true,
                    'status' => is_null($recipient->read_at) ? 'unread' : 'read',
                    'created_at' => $notification->created_at->toISOString(),
                    'updated_at' => $notification->updated_at->toISOString(),
                    'sender_name' => $notification->sender_name ?? 'النظام',
                    'attachment_path' => $notification->attachment_path,
                    'attachment_name' => $notification->attachment_name,
                    'scheduled_at' => $notification->scheduled_at,
                    'expires_at' => $notification->expires_at,
            ];
            }
        }

        return response()->json([
            'success' => true,
            'data' => $formattedData,
            'pagination' => [
                'current_page' => $recipients->currentPage(),
                'total_pages' => $recipients->lastPage(),
                'total_items' => $recipients->total(),
                'per_page' => $recipients->perPage(),
            ],
            'message' => 'تم تحميل الإشعارات بنجاح'
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'خطأ في تحميل الإشعارات: ' . $e->getMessage()
        ], 500);
    }
});

// Mark notification as read
Route::post('/api/employee/notifications/{id}/mark-read', function($id) {
    try {
        $notification = \App\Models\Notification::find($id);

        if (!$notification) {
            return response()->json([
                'success' => false,
                'message' => 'الإشعار غير موجود'
            ], 404);
        }

        $notification->update(['status' => 'read']);

        return response()->json([
            'success' => true,
            'message' => 'تم تحديد الإشعار كمقروء'
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'خطأ: ' . $e->getMessage()
        ], 500);
    }
});

// Mark notification as unread
Route::post('/api/employee/notifications/{id}/mark-unread', function($id) {
    try {
        $notification = \App\Models\Notification::find($id);

        if (!$notification) {
            return response()->json([
                'success' => false,
                'message' => 'الإشعار غير موجود'
            ], 404);
        }

        $notification->update(['status' => 'unread']);

        return response()->json([
            'success' => true,
            'message' => 'تم تحديد الإشعار كغير مقروء'
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'خطأ: ' . $e->getMessage()
        ], 500);
    }
});

// Redirect root to login page
Route::get('/', [AuthController::class, 'showLoginForm'])->name('login'); // Adding named route for login page
Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login.form');

// Authentication routes
Route::post('/login', [AuthController::class, 'login'])->name('login.submit');
Route::post('/logout', [AuthController::class, 'logout'])->name('logout');
Route::get('/logout', [AuthController::class, 'logout'])->name('logout.get');
// Admin dashboard route
Route::get('/dashboard', [AuthController::class, 'dashboard'])->name('dashboard')->middleware('auth:web');

// Student routes
Route::middleware(['auth:student'])->prefix('student')->name('student.')->group(function () {
    Route::get('/dashboard', [\App\Http\Controllers\Student\DashboardController::class, 'index'])->name('dashboard');
    Route::get('/notifications', [\App\Http\Controllers\Student\DashboardController::class, 'notifications'])->name('notifications');
    Route::post('/notifications/{notification}/mark-read', [\App\Http\Controllers\Student\DashboardController::class, 'markAsRead'])->name('notifications.mark-read');
    
    // AJAX routes for notifications
    Route::get('/notification/{notificationId}', [\App\Http\Controllers\Student\DashboardController::class, 'getNotification']);
    Route::get('/notification-attachments/{notificationId}', [\App\Http\Controllers\Student\DashboardController::class, 'getAttachments']);
    Route::post('/notifications/mark-read/{notificationId}', [\App\Http\Controllers\Student\DashboardController::class, 'markAsReadAjax']);
    
    // Profile routes
    Route::get('/profile', [\App\Http\Controllers\Student\ProfileController::class, 'edit'])->name('profile.edit');
    Route::put('/profile', [\App\Http\Controllers\Student\ProfileController::class, 'update'])->name('profile.update');
    Route::get('/profile/change-password', [\App\Http\Controllers\Student\ProfileController::class, 'showChangePasswordForm'])->name('profile.change-password');
    Route::put('/profile/change-password', [\App\Http\Controllers\Student\ProfileController::class, 'changePassword'])->name('profile.update-password');
});

// Employee routes
Route::middleware(['auth:employee'])->prefix('employee')->name('employee.')->group(function () {
    Route::get('/dashboard', [\App\Http\Controllers\Employee\DashboardController::class, 'index'])->name('dashboard');
    Route::get('/notifications', [\App\Http\Controllers\Employee\DashboardController::class, 'notifications'])->name('notifications');
    Route::post('/notifications/{notification}/mark-read', [\App\Http\Controllers\Employee\DashboardController::class, 'markAsRead'])->name('notifications.mark-read');
    
    // AJAX routes for notifications
    Route::get('/notification/{notificationId}', [\App\Http\Controllers\Employee\DashboardController::class, 'getNotification']);
    Route::post('/notifications/mark-read/{notificationId}', [\App\Http\Controllers\Employee\DashboardController::class, 'markAsReadAjax']);
    
    // Profile routes
    Route::get('/profile', [\App\Http\Controllers\Employee\ProfileController::class, 'edit'])->name('profile.edit');
    Route::put('/profile', [\App\Http\Controllers\Employee\ProfileController::class, 'update'])->name('profile.update');
    Route::get('/profile/change-password', [\App\Http\Controllers\Employee\ProfileController::class, 'showChangePasswordForm'])->name('profile.change-password');
    Route::put('/profile/change-password', [\App\Http\Controllers\Employee\ProfileController::class, 'changePassword'])->name('profile.update-password');
});

// Student and Employee management routes - all protected by auth middleware
Route::middleware(['auth:web,student'])->group(function () {
    // Student CRUD routes
    Route::get('/students', [StudentController::class, 'index'])->name('students.index');
    Route::post('/students', [StudentController::class, 'store'])->name('students.store');
    Route::put('/students/{student}', [StudentController::class, 'update'])->name('students.update');
    Route::delete('/students/{student}', [StudentController::class, 'destroy'])->name('students.destroy');
    
    // Excel import/export routes
    Route::post('/students/import', [StudentController::class, 'import'])->name('students.import');
    Route::get('/students/import-progress/{importId}', [StudentController::class, 'importProgress'])->name('students.import.progress');
    Route::get('/students/export/template', [StudentController::class, 'exportTemplate'])->name('students.export.template');
    
    // Bulk actions route
    Route::post('/students/bulk', [StudentController::class, 'bulkAction'])->name('students.bulk');
    
    // Employee management routes
    Route::resource('employees', EmployeeController::class);
    Route::post('/employees/bulk', [EmployeeController::class, 'bulkAction'])->name('employees.bulk');
    Route::get('/employees/refresh-cache', [EmployeeController::class, 'refreshDropdownCache'])->name('employees.refresh-cache');
    
    // Employee import/export routes
    Route::post('/employees/import', [EmployeeController::class, 'import'])->name('employees.import');
    Route::get('/employees/import-progress/{importId}', [EmployeeController::class, 'importProgress'])->name('employees.import.progress');
    Route::get('/employees/export/template', [EmployeeController::class, 'exportTemplate'])->name('employees.export.template');
    
    // Notification system routes
    Route::get('/notifications', [NotificationController::class, 'index'])->name('notifications.index');
    Route::get('/notifications/create', [NotificationController::class, 'create'])->name('notifications.create');
    Route::post('/notifications', [NotificationController::class, 'store'])->name('notifications.store');
    Route::get('/notifications/{notification}', [NotificationController::class, 'show'])->name('notifications.show');
    
    // Bulk notification actions
    Route::delete('/notifications/bulk-delete', [NotificationController::class, 'bulkDelete'])->name('notifications.bulkDelete');
    Route::patch('/notifications/bulk-update-status', [NotificationController::class, 'bulkUpdateStatus'])->name('notifications.bulkUpdateStatus');
    
    // System Logs routes
    Route::get('/logs', [\App\Http\Controllers\LogController::class, 'index'])->name('logs.index');
    Route::get('/logs/{filename}', [\App\Http\Controllers\LogController::class, 'show'])->name('logs.show');
    Route::delete('/logs/{filename}', [\App\Http\Controllers\LogController::class, 'destroy'])->name('logs.destroy');
    
    // Database Backups routes
    Route::get('/backups', [\App\Http\Controllers\BackupController::class, 'index'])->name('backups.index');
    Route::post('/backups/create', [\App\Http\Controllers\BackupController::class, 'create'])->name('backups.create');
    Route::get('/backups/download/{filename}', [\App\Http\Controllers\BackupController::class, 'download'])->name('backups.download');
    Route::delete('/backups/{filename}', [\App\Http\Controllers\BackupController::class, 'destroy'])->name('backups.destroy');
});









