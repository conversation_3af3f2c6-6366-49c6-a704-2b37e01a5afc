import 'package:flutter/material.dart';

class ApiConstants {
  // Laravel API URL for Laragon setup
  static const String baseUrl = 'http://localhost/appnote-api/public/api';

  // Alternative URLs for different setups:
  // static const String baseUrl = 'http://127.0.0.1:8000/api'; // For php artisan serve
  // static const String baseUrl = 'http://appnote-api.test/api'; // For Laragon with virtual host

  // Authentication endpoints (matching Laravel routes)
  static const String adminLogin = '$baseUrl/auth/admin/login';
  static const String studentLogin = '$baseUrl/auth/student/login';
  static const String employeeLogin = '$baseUrl/auth/employee/login';
  static const String logout = '$baseUrl/auth/logout';

  // Health check endpoints
  static const String healthCheck = '$baseUrl/health';
  static const String testDb = '$baseUrl/test-db';
  static const String docs = '$baseUrl/docs';

  // Other endpoints (will be added as needed)
  static const String notifications = '$baseUrl/notifications';
  static const String students = '$baseUrl/students';
  static const String employees = '$baseUrl/employees';
  static const String logs = '$baseUrl/logs';
  static const String backups = '$baseUrl/backups';
}

class StorageKeys {
  static const String userToken = 'user_token';
  static const String userType = 'user_type';
  static const String userData = 'user_data';
}

enum UserType { admin, student, employee }

// App Color Palette - Enhanced with Logo Colors
class AppColors {
  // Original palette colors
  static const Color parchment = Color(0xFFF4ECDC);
  static const Color costaDelSol = Color(0xFF5D6E35);
  static const Color locust = Color(0xFFABAE88);
  static const Color gurkha = Color(0xFF949C74);
  static const Color avocado = Color(0xFF949B6C);
  static const Color coralReef = Color(0xFFC4C2A4);
  static const Color gurkha2 = Color(0xFF9C9C74);
  static const Color thistleGreen = Color(0xFFD0CDB0);
  static const Color tana = Color(0xFFDED8BF);
  static const Color chino = Color(0xFFCCC4A9);

  // Additional colors extracted from logo
  static const Color logoGreen = Color(0xFF4A5D2A); // Dark green from logo
  static const Color logoLightGreen = Color(
    0xFF6B7F3F,
  ); // Medium green from logo
  static const Color logoBeige = Color(
    0xFFF2E8D5,
  ); // Light beige from logo background
  static const Color logoOlive = Color(
    0xFF7A8B5C,
  ); // Olive green from logo leaves
  static const Color logoSage = Color(0xFF9CAA7E); // Sage green from logo
  static const Color logoBrown = Color(
    0xFF8B7355,
  ); // Brown undertones from logo
  static const Color logoGold = Color(0xFFD4C4A8); // Golden beige from logo
  static const Color logoForest = Color(0xFF3E4F26); // Deep forest green
  static const Color logoMint = Color(0xFFB8C5A0); // Mint green from highlights
  static const Color logoTaupe = Color(0xFFB5A48B); // Taupe from logo shadows

  // Role-specific colors (updated with logo colors)
  static const Color adminPrimary = logoGreen; // Deep green for admin
  static const Color studentPrimary = logoSage; // Sage green for students
  static const Color employeePrimary = logoOlive; // Olive green for employees

  // UI colors (enhanced with logo colors) - Made lighter
  static const Color background = Color(
    0xFFF8F4E8,
  ); // Lighter background (was logoBeige)
  static const Color surface = Color(
    0xFFF6F0E4,
  ); // Lighter surface (was logoGold)
  static const Color cardBackground = Color(
    0xFFF2F0E8,
  ); // Lighter card background
  static const Color textPrimary = logoForest; // Deep forest for primary text
  static const Color textSecondary = logoOlive; // Olive for secondary text
  static const Color accent = logoMint; // Mint for accents
  static const Color border = logoTaupe; // Taupe for borders

  // Enhanced status colors (inspired by logo)
  static const Color success = logoSage; // Sage green for success
  static const Color warning = logoBrown; // Brown for warnings
  static const Color error = Color(
    0xFF8B4A42,
  ); // Muted red that complements logo
  static const Color info = logoLightGreen; // Light green for info

  // Gradient colors for beautiful transitions
  static const Color gradientStart = logoBeige;
  static const Color gradientMiddle = logoGold;
  static const Color gradientEnd = logoMint;

  // Additional semantic colors
  static const Color highlight = logoGold;
  static const Color shadow = logoTaupe;
  static const Color disabled = Color(0xFFD0C7B8);
  static const Color divider = logoTaupe;
}
