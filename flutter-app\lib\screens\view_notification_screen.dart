import 'package:flutter/material.dart';
import '../models/notification.dart';
import '../services/attachment_service.dart';
import 'edit_notification_screen.dart';

class ViewNotificationScreen extends StatelessWidget {
  final AppNotification notification;

  const ViewNotificationScreen({super.key, required this.notification});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF4ECDC),
      appBar: AppBar(
        title: const Text(
          'عرض الإشعار',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: const Color(0xFF5D6E35),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Card
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title
                  Row(
                    children: [
                      Icon(
                        _getTypeIcon(notification.type),
                        color: _getTypeColor(notification.type),
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          notification.title,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF2C3E50),
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Status and Priority
                  Row(
                    children: [
                      _buildStatusChip(
                        notification.isActive ? 'نشط' : 'غير نشط',
                        notification.isActive ? Colors.green : Colors.grey,
                      ),
                      const SizedBox(width: 8),
                      _buildStatusChip(
                        _getPriorityText(notification.priority),
                        _getPriorityColor(notification.priority),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20),

            // Content Card
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'محتوى الإشعار',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF5D6E35),
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    notification.content.isNotEmpty
                        ? notification.content
                        : 'لا يوجد محتوى للإشعار',
                    style: TextStyle(
                      fontSize: 14,
                      height: 1.6,
                      color: notification.content.isNotEmpty
                          ? const Color(0xFF2C3E50)
                          : Colors.grey.shade600,
                      fontStyle: notification.content.isNotEmpty
                          ? FontStyle.normal
                          : FontStyle.italic,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20),

            // Details Card
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'تفاصيل الإشعار',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF5D6E35),
                    ),
                  ),
                  const SizedBox(height: 16),

                  _buildDetailRow('رقم الإشعار', '#${notification.id}'),
                  _buildDetailRow('النوع', _getTypeText(notification.type)),
                  _buildDetailRow(
                    'الأولوية',
                    _getPriorityText(notification.priority),
                  ),
                  _buildDetailRow(
                    'الجمهور المستهدف',
                    notification.targetAudience,
                  ),
                  _buildDetailRow(
                    'تاريخ الإنشاء',
                    notification.createdAt != null
                        ? _formatDate(notification.createdAt!)
                        : 'غير محدد',
                  ),
                  _buildDetailRow(
                    'آخر تحديث',
                    notification.updatedAt != null
                        ? _formatDate(notification.updatedAt!)
                        : 'غير محدد',
                  ),

                  if (notification.scheduledAt != null)
                    _buildDetailRow(
                      'موعد الإرسال',
                      _formatDate(notification.scheduledAt!),
                    ),

                  if (notification.expiresAt != null)
                    _buildDetailRow(
                      'تاريخ الانتهاء',
                      _formatDate(notification.expiresAt!),
                    ),
                ],
              ),
            ),

            const SizedBox(height: 20),

            // Attachments Card (if any)
            if (notification.hasAttachment)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Text(
                          'المرفقات',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF5D6E35),
                          ),
                        ),
                        const Spacer(),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.blue.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            notification.attachmentCount == 1
                                ? 'ملف واحد'
                                : '${notification.attachmentCount} ملفات',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.blue.shade700,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    // Display all attachments
                    ...notification.allAttachments.asMap().entries.map((entry) {
                      final index = entry.key;
                      final attachment = entry.value;
                      final fileName = _cleanFileName(
                        attachment['name'] ?? 'ملف مرفق',
                      );
                      final fileSize = attachment['size'] ?? 0;

                      return GestureDetector(
                        onTap: () async {
                          await AttachmentService.openAttachment(context, attachment);
                        },
                        child: Container(
                          margin: EdgeInsets.only(
                            bottom: index < notification.allAttachments.length - 1
                                ? 8
                                : 0,
                          ),
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.green.shade300),
                            borderRadius: BorderRadius.circular(8),
                            color: Colors.green.shade50,
                          ),
                        child: Row(
                          children: [
                            Icon(
                              _getFileIcon(fileName),
                              color: Colors.green.shade700,
                              size: 24,
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    fileName,
                                    style: const TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  const SizedBox(height: 4),
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.info_outline,
                                        size: 14,
                                        color: Colors.grey.shade600,
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        fileSize > 0
                                            ? 'حجم الملف: ${_formatFileSize(fileSize)}'
                                            : 'حجم غير محدد',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.grey.shade600,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(6),
                                border: Border.all(color: Colors.grey.shade300),
                              ),
                              child: Icon(
                                Icons.download,
                                size: 16,
                                color: Colors.grey.shade700,
                              ),
                            ),
                          ],
                        ),
                        ),
                      );
                    }),
                  ],
                ),
              ),

            const SizedBox(height: 30),

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.arrow_back),
                    label: const Text('العودة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey.shade600,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () async {
                      final scaffoldMessenger = ScaffoldMessenger.of(context);
                      final result = await Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => EditNotificationScreen(
                            notification: notification,
                          ),
                        ),
                      );

                      // If notification was updated successfully, show success message
                      if (result == true) {
                        scaffoldMessenger.showSnackBar(
                          const SnackBar(
                            content: Text('تم تحديث الإشعار بنجاح'),
                            backgroundColor: Colors.green,
                          ),
                        );
                      }
                    },
                    icon: const Icon(Icons.edit),
                    label: const Text('تعديل'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF5D6E35),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade700,
              ),
            ),
          ),
          const Text(': ', style: TextStyle(fontWeight: FontWeight.bold)),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 14, color: Color(0xFF2C3E50)),
            ),
          ),
        ],
      ),
    );
  }

  IconData _getTypeIcon(String type) {
    switch (type) {
      case 'info':
        return Icons.info;
      case 'warning':
        return Icons.warning;
      case 'success':
        return Icons.check_circle;
      case 'error':
        return Icons.error;
      default:
        return Icons.notifications;
    }
  }

  Color _getTypeColor(String type) {
    switch (type) {
      case 'info':
        return Colors.blue;
      case 'warning':
        return Colors.orange;
      case 'success':
        return Colors.green;
      case 'error':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getTypeText(String type) {
    switch (type) {
      case 'info':
        return 'معلومات';
      case 'warning':
        return 'تحذير';
      case 'success':
        return 'نجاح';
      case 'error':
        return 'خطأ';
      default:
        return 'عام';
    }
  }

  String _getPriorityText(String priority) {
    switch (priority) {
      case 'low':
        return 'منخفضة';
      case 'medium':
        return 'متوسطة';
      case 'high':
        return 'عالية';
      default:
        return 'غير محدد';
    }
  }

  Color _getPriorityColor(String priority) {
    switch (priority) {
      case 'low':
        return Colors.green;
      case 'medium':
        return Colors.orange;
      case 'high':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} - ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  String _cleanFileName(String fileName) {
    // Remove Unicode escape sequences and clean the filename
    String cleaned = fileName.replaceAll(RegExp(r'\\u[0-9a-fA-F]{4}'), '');

    // Remove array brackets and quotes if present
    cleaned = cleaned.replaceAll(RegExp(r'[\[\]"]'), '');

    // If the cleaned name is empty or too short, provide a default
    if (cleaned.trim().isEmpty || cleaned.length < 3) {
      return 'ملف مرفق';
    }

    // Extract just the filename without path
    if (cleaned.contains('/')) {
      cleaned = cleaned.split('/').last;
    }
    if (cleaned.contains('\\')) {
      cleaned = cleaned.split('\\').last;
    }

    // Remove any remaining special characters that might cause issues
    cleaned = cleaned.replaceAll(RegExp(r'[^\w\s\-_\.]'), '');

    final result = cleaned.trim();

    return result.isNotEmpty ? result : 'ملف مرفق';
  }

  IconData _getFileIcon(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    switch (extension) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart;
      case 'ppt':
      case 'pptx':
        return Icons.slideshow;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return Icons.image;
      case 'txt':
        return Icons.text_snippet;
      case 'zip':
      case 'rar':
        return Icons.archive;
      default:
        return Icons.insert_drive_file;
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }
}
