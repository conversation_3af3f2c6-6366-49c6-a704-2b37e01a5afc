<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Symfony\Component\Process\Process;
use Symfony\Component\Process\Exception\ProcessFailedException;

class BackupController extends Controller
{
    /**
     * Backup directory path
     */
    protected $backupPath;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->backupPath = storage_path('app/backups');
        
        // Create backups directory if it doesn't exist
        if (!File::exists($this->backupPath)) {
            File::makeDirectory($this->backupPath, 0755, true);
        }
    }

    /**
     * Get all backup files.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        try {
            $backupFiles = collect(File::files($this->backupPath))->filter(function ($file) {
                return in_array($file->getExtension(), ['sql', 'gz', 'zip']);
            })->map(function ($file) {
                return [
                    'name' => $file->getFilename(),
                    'size' => $this->formatFileSize($file->getSize()),
                    'size_bytes' => $file->getSize(),
                    'modified' => date('Y-m-d H:i:s', $file->getMTime()),
                    'timestamp' => $file->getMTime(),
                ];
            })->sortByDesc('timestamp')->values();

            return response()->json([
                'success' => true,
                'data' => $backupFiles,
                'count' => $backupFiles->count()
            ]);
        } catch (\Exception $e) {
            Log::error('❌ Error retrieving backup files: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving backup files: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a new database backup.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function create()
    {
        try {
            // Get database config
            $host = config('database.connections.mysql.host');
            $port = config('database.connections.mysql.port');
            $database = config('database.connections.mysql.database');
            $username = config('database.connections.mysql.username');
            $password = config('database.connections.mysql.password');
            
            // Set filename with timestamp
            $filename = "backup-" . Carbon::now()->format('Y-m-d_H-i-s') . ".sql";
            $filepath = $this->backupPath . '/' . $filename;
            
            // Build mysqldump command
            $command = "mysqldump -h {$host} -P {$port} -u {$username}";
            
            if ($password) {
                $command .= " -p'{$password}'";
            }
            
            $command .= " {$database} > {$filepath}";
            
            // Execute backup command
            $process = Process::fromShellCommandline($command);
            $process->setTimeout(300); // 5 minutes
            $process->run();
            
            // Check if process was successful
            if (!$process->isSuccessful()) {
                throw new ProcessFailedException($process);
            }
            
            Log::info('✅ Database backup created successfully: ' . $filename);
            
            return response()->json([
                'success' => true,
                'message' => 'Database backup created successfully',
                'data' => [
                    'filename' => $filename,
                    'size' => $this->formatFileSize(File::size($filepath)),
                    'size_bytes' => File::size($filepath),
                    'created_at' => date('Y-m-d H:i:s', File::lastModified($filepath)),
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('❌ Error creating database backup: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error creating database backup: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Download a database backup file.
     *
     * @param string $filename
     * @return \Illuminate\Http\JsonResponse|\Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function download(string $filename)
    {
        try {
            $filePath = $this->backupPath . '/' . $filename;
            
            if (!File::exists($filePath)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Backup file not found'
                ], 404);
            }
            
            return response()->download($filePath);
        } catch (\Exception $e) {
            Log::error('❌ Error downloading backup file: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error downloading backup file: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete the specified backup file.
     *
     * @param string $filename
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(string $filename)
    {
        try {
            $filePath = $this->backupPath . '/' . $filename;
            
            if (!File::exists($filePath)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Backup file not found'
                ], 404);
            }

            File::delete($filePath);
            
            return response()->json([
                'success' => true,
                'message' => 'Backup file deleted successfully',
                'data' => [
                    'filename' => $filename
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('❌ Error deleting backup file: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error deleting backup file: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Format file size to readable format
     */
    protected function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
