# 🚨 دليل استكشاف أخطاء API وحلها

## ❌ **خطأ: json_decode(): Argument #1 ($json) must be of type string, array given**

### 🔍 **سبب المشكلة:**
يحدث هذا الخطأ عندما يتم إرسال البيانات كـ **array** أو **object** بدلاً من **JSON string**.

### 🔧 **الحلول:**

#### 1. **في Flutter/Dart:**
```dart
import 'dart:convert';
import 'package:http/http.dart' as http;

// ❌ خطأ - إرسال Map مباشرة
Map<String, dynamic> wrongData = {
  'full_name': 'محمد أحمد',
  'phone': '70123456',
  'contract_type': 'دوام كامل'
};

// ✅ صحيح - تحويل إلى JSON string
Map<String, dynamic> data = {
  'full_name': 'محمد أحمد',
  'phone': '70123456',
  'contract_type': 'دوام كامل'
};

String jsonString = jsonEncode(data);

final response = await http.post(
  Uri.parse('$baseUrl/api/employees'),
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer $token',
  },
  body: jsonString,  // إرسال JSON string وليس Map
);
```

#### 2. **في PowerShell:**
```powershell
# ❌ خطأ - إرسال Hashtable مباشرة
$wrongData = @{
    full_name = "محمد أحمد"
    phone = "70123456"
}

# ✅ صحيح - تحويل إلى JSON
$data = @{
    full_name = "محمد أحمد"
    phone = "70123456"
    contract_type = "دوام كامل"
    employee_type = "مدرس"
    job_status = "نشط"
}

$jsonData = $data | ConvertTo-Json -Depth 3

$headers = @{
    "Authorization" = "Bearer $token"
    "Content-Type" = "application/json"
}

Invoke-WebRequest -Uri "$baseUrl/api/employees" -Method POST -Headers $headers -Body $jsonData
```

#### 3. **في JavaScript/Ajax:**
```javascript
// ❌ خطأ - إرسال Object مباشرة
const wrongData = {
  full_name: 'محمد أحمد',
  phone: '70123456'
};

// ✅ صحيح - تحويل إلى JSON string
const data = {
  full_name: 'محمد أحمد',
  phone: '70123456',
  contract_type: 'دوام كامل'
};

fetch('/api/employees', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify(data)  // تحويل إلى JSON string
});
```

#### 4. **في cURL:**
```bash
# ✅ صحيح - إرسال JSON string
curl -X POST "http://localhost/appnote-api/public/api/employees" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "full_name": "محمد أحمد",
    "phone": "70123456",
    "contract_type": "دوام كامل",
    "employee_type": "مدرس",
    "job_status": "نشط"
  }'
```

---

## ❌ **أخطاء أخرى شائعة وحلولها**

### 🔐 **خطأ 401 Unauthorized**

#### السبب:
- Token غير صحيح أو منتهي الصلاحية
- عدم إرسال Authorization header

#### الحل:
```dart
// تأكد من إرسال Token صحيح
headers: {
  'Authorization': 'Bearer $token',  // تأكد من المسافة بعد Bearer
  'Content-Type': 'application/json'
}

// تحقق من صحة Token
if (token == null || token.isEmpty) {
  // إعادة تسجيل الدخول
  await login();
}
```

### 📝 **خطأ 422 Validation Error**

#### السبب:
- حقول مطلوبة مفقودة
- نوع البيانات غير صحيح
- قيم غير مسموحة

#### الحل:
```dart
// تأكد من إرسال جميع الحقول المطلوبة
Map<String, dynamic> employeeData = {
  'full_name': 'محمد أحمد',        // مطلوب
  'contract_type': 'دوام كامل',    // مطلوب
  'employee_type': 'مدرس',        // مطلوب
  'job_status': 'نشط',           // مطلوب
  'phone': '70123456',           // اختياري
  'username': 'emp001',          // اختياري
};

// تحقق من القيم المسموحة
List<String> allowedContractTypes = ['دوام كامل', 'دوام جزئي', 'مؤقت'];
List<String> allowedJobStatuses = ['نشط', 'غير نشط', 'إجازة'];
```

### 🔧 **خطأ 500 Internal Server Error**

#### السبب:
- خطأ في الخادم
- بيانات غير صحيحة
- مشكلة في قاعدة البيانات

#### الحل:
```dart
// إضافة معالجة الأخطاء
try {
  final response = await http.post(
    Uri.parse('$baseUrl/api/employees'),
    headers: headers,
    body: jsonData,
  );
  
  if (response.statusCode == 500) {
    print('Server Error: ${response.body}');
    // إظهار رسالة خطأ للمستخدم
    showErrorDialog('حدث خطأ في الخادم، يرجى المحاولة لاحقاً');
  }
} catch (e) {
  print('Network Error: $e');
  showErrorDialog('تحقق من اتصال الإنترنت');
}
```

### 🌐 **خطأ Network/Connection**

#### السبب:
- عدم وجود اتصال بالإنترنت
- URL غير صحيح
- الخادم غير متاح

#### الحل:
```dart
// تحقق من الاتصال
import 'package:connectivity_plus/connectivity_plus.dart';

Future<bool> checkConnection() async {
  var connectivityResult = await Connectivity().checkConnectivity();
  return connectivityResult != ConnectivityResult.none;
}

// استخدام timeout
final response = await http.post(
  Uri.parse('$baseUrl/api/employees'),
  headers: headers,
  body: jsonData,
).timeout(Duration(seconds: 30));
```

---

## 🧪 **اختبار سريع للتأكد من الحلول**

### 1. **اختبار بسيط للموظفين:**
```powershell
# اختبار إنشاء موظف
$testData = @{
    full_name = "اختبار موظف"
    contract_type = "دوام كامل"
    employee_type = "مدرس"
    job_status = "نشط"
} | ConvertTo-Json

$headers = @{
    "Authorization" = "Bearer YOUR_TOKEN"
    "Content-Type" = "application/json"
}

$response = Invoke-WebRequest -Uri "http://localhost/appnote-api/public/api/employees" -Method POST -Headers $headers -Body $testData

Write-Host "Status: $($response.StatusCode)"
Write-Host "Response: $($response.Content)"
```

### 2. **اختبار بسيط للإشعارات:**
```powershell
# اختبار إنشاء إشعار
$notificationData = @{
    title = "اختبار إشعار"
    content = "هذا إشعار تجريبي"
    type = "info"
    priority = "medium"
    target_audience = "all"
} | ConvertTo-Json

$response = Invoke-WebRequest -Uri "http://localhost/appnote-api/public/api/notifications" -Method POST -Headers $headers -Body $notificationData

Write-Host "Status: $($response.StatusCode)"
Write-Host "Response: $($response.Content)"
```

---

## 📋 **قائمة تحقق سريعة**

قبل إرسال أي طلب API، تأكد من:

- ✅ **Content-Type**: `application/json`
- ✅ **Authorization**: `Bearer TOKEN` (مع المسافة)
- ✅ **Body**: JSON string وليس object
- ✅ **Required Fields**: جميع الحقول المطلوبة موجودة
- ✅ **Data Types**: أنواع البيانات صحيحة (boolean, integer, string)
- ✅ **Allowed Values**: القيم من القوائم المسموحة
- ✅ **URL**: الرابط صحيح ومتاح

---

## 🔧 **أدوات مفيدة للاختبار**

1. **Postman** - لاختبار APIs
2. **Insomnia** - بديل لـ Postman
3. **Browser DevTools** - لمراقبة Network requests
4. **Laravel Telescope** - لمراقبة requests في Laravel
5. **Laravel Log Viewer** - لمراجعة logs الخادم

---

## 📞 **الحصول على المساعدة**

إذا استمرت المشاكل:

1. **تحقق من Laravel logs**: `storage/logs/laravel.log`
2. **فعّل debug mode**: `.env` → `APP_DEBUG=true`
3. **استخدم `dd()` في Controller** لفحص البيانات المستلمة
4. **تحقق من Database logs** للتأكد من وصول البيانات

هذا الدليل يجب أن يحل معظم المشاكل الشائعة في API! 🚀
