<?php

namespace App\Http\Controllers;

use App\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class EmployeeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Set proper charset header for Arabic text
        header('Content-Type: text/html; charset=UTF-8');

        // Ensure database connection uses UTF-8
        \DB::statement("SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci");

        // Increase page size and select all necessary columns including the four additional fields
        $query = Employee::select('id', 'username', 'full_name', 'phone', 'contract_type', 'employee_type', 'job_status', 'automatic_number', 'financial_number', 'state_cooperative_number', 'bank_account_number');
        
        // Add search functionality
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('full_name', 'LIKE', "%{$search}%")
                  ->orWhere('username', 'LIKE', "%{$search}%")
                  ->orWhere('phone', 'LIKE', "%{$search}%")
                  ->orWhere('employee_type', 'LIKE', "%{$search}%")
                  ->orWhere('contract_type', 'LIKE', "%{$search}%")
                  ->orWhere('job_status', 'LIKE', "%{$search}%");
            });
        }

        // Add filter by contract type
        if ($request->has('contract_type') && !empty($request->contract_type)) {
            $query->where('contract_type', $request->contract_type);
        }

        // Add filter by employee type
        if ($request->has('employee_type') && !empty($request->employee_type)) {
            $query->where('employee_type', $request->employee_type);
        }
        
        // Execute the paginated query
        $employees = $query->latest()->paginate(25);
        
        // Get unique values for dropdowns using optimized queries
        // Use caching to avoid repeated queries
        $contractTypes = cache()->remember('contract_types', 60*60, function() {
            return Employee::select('contract_type')
                ->distinct()
                ->whereNotNull('contract_type')
                ->where('contract_type', '!=', '')
                ->where('contract_type', 'NOT LIKE', '%?%') // Exclude corrupted data
                ->orderBy('contract_type')
                ->pluck('contract_type')
                ->toArray();
        });

        $employeeTypes = cache()->remember('employee_types', 60*60, function() {
            return Employee::select('employee_type')
                ->distinct()
                ->whereNotNull('employee_type')
                ->where('employee_type', '!=', '')
                ->where('employee_type', 'NOT LIKE', '%?%') // Exclude corrupted data
                ->orderBy('employee_type')
                ->pluck('employee_type')
                ->toArray();
        });

        $jobStatuses = cache()->remember('job_statuses', 60*60, function() {
            return Employee::select('job_status')
                ->distinct()
                ->whereNotNull('job_status')
                ->where('job_status', '!=', '')
                ->where('job_status', 'NOT LIKE', '%?%') // Exclude corrupted data
                ->orderBy('job_status')
                ->pluck('job_status')
                ->toArray();
        });
        
        // Set default values if no data exists yet
        if (empty($contractTypes)) {
            $contractTypes = ['Full-time', 'Part-time', 'Contract'];
        }
        
        if (empty($employeeTypes)) {
            $employeeTypes = ['Administrative', 'Academic', 'Technical', 'Support', 'Consultant'];
        }
        
        if (empty($jobStatuses)) {
            $jobStatuses = ['Active', 'On Leave', 'Terminated'];
        }
        
        return view('employees.index', compact('employees', 'contractTypes', 'employeeTypes', 'jobStatuses'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Not needed as we're using modal forms
        return redirect()->route('employees.index');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'full_name' => 'required|string|max:255',
            'contract_type' => 'required|string|max:255',
            'employee_type' => 'required|string|max:255',
            'phone' => 'nullable|string|max:255',
            'job_status' => 'required|string|max:255',
            'automatic_number' => 'nullable|string|max:255',
            'financial_number' => 'nullable|string|max:255',
            'state_cooperative_number' => 'nullable|string|max:255',
            'bank_account_number' => 'nullable|string|max:255',
        ]);
        
        // Get all input data but ensure employee_id is not included
        $employeeData = $request->all();
        if (isset($employeeData['employee_id'])) {
            unset($employeeData['employee_id']);
        }
        
        // Employee model will automatically generate username and password
        $employee = Employee::create($employeeData);
        
        return redirect()->route('employees.index')
            ->with('success', 'Employee created successfully. Username: ' . $employee->username);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        // Not needed as we're using modal forms
        return redirect()->route('employees.index');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        // Not needed as we're using modal forms
        return redirect()->route('employees.index');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $employee = Employee::findOrFail($id);
        
        $request->validate([
            'full_name' => 'required|string|max:255',
            'contract_type' => 'required|string|max:255',
            'employee_type' => 'required|string|max:255',
            'phone' => 'nullable|string|max:255',
            'job_status' => 'required|string|max:255',
            'automatic_number' => 'nullable|string|max:255',
            'financial_number' => 'nullable|string|max:255',
            'state_cooperative_number' => 'nullable|string|max:255',
            'bank_account_number' => 'nullable|string|max:255',
        ]);
        
        // Get all input data but ensure employee_id is not included
        $employeeData = $request->all();
        
        // Remove employee_id if it exists
        if (isset($employeeData['employee_id'])) {
            unset($employeeData['employee_id']);
        }
        
        // Handle password - only update if provided
        if (empty($employeeData['password'])) {
            // Remove password field if it's empty to avoid updating with empty value
            unset($employeeData['password']);
        } else {
            // Hash the new password if provided
            $employeeData['password'] = Hash::make($employeeData['password']);
        }
        
        $employee->update($employeeData);
        
        return redirect()->route('employees.index')
            ->with('success', 'Employee updated successfully');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $employee = Employee::findOrFail($id);
        $employee->delete();
        
        return redirect()->route('employees.index')
            ->with('success', 'Employee deleted successfully');
    }
    
    /**
     * Handle bulk actions on employees.
     */
    public function bulkAction(Request $request)
    {
        $action = $request->input('action');
        $ids = $request->input('ids', '');
        
        // Convert comma-separated string to array if needed
        $idArray = is_array($ids) ? $ids : explode(',', $ids);
        
        // Validate action and IDs
        if (empty($idArray) && $action !== 'delete-all') {
            return redirect()->route('employees.index')->with('error', 'No employees selected for bulk action');
        }
        
        try {
            $count = 0;
            $message = '';
            
            switch ($action) {
                case 'activate':
                    // Process in chunks to avoid timeout
                    DB::beginTransaction();
                    foreach(array_chunk($idArray, 100) as $chunk) {
                        Employee::whereIn('id', $chunk)->update(['job_status' => 'Active']);
                    }
                    DB::commit();
                    $count = count($idArray);
                    $message = "{$count} employees activated successfully.";
                    break;
                    
                case 'deactivate':
                    // Process in chunks to avoid timeout
                    DB::beginTransaction();
                    foreach(array_chunk($idArray, 100) as $chunk) {
                        Employee::whereIn('id', $chunk)->update(['job_status' => 'Terminated']);
                    }
                    DB::commit();
                    $count = count($idArray);
                    $message = "{$count} employees deactivated successfully.";
                    break;
                    
                case 'delete':
                    // Process in chunks to avoid timeout
                    DB::beginTransaction();
                    $count = 0;
                    foreach(array_chunk($idArray, 100) as $chunk) {
                        $count += Employee::whereIn('id', $chunk)->count();
                        Employee::whereIn('id', $chunk)->delete();
                    }
                    DB::commit();
                    $message = "{$count} employees deleted successfully.";
                    break;
                    
                case 'delete-all':
                    set_time_limit(300); // Extend time limit for this operation
                    $count = Employee::count();
                    // Use raw database query for better performance on large datasets
                    DB::statement('SET FOREIGN_KEY_CHECKS=0;');
                    Employee::truncate();
                    DB::statement('SET FOREIGN_KEY_CHECKS=1;');
                    $message = "All {$count} employees deleted successfully.";
                    break;
                    
                default:
                    return redirect()->route('employees.index')->with('error', 'Invalid bulk action');
            }
            
            return redirect()->route('employees.index')->with('success', $message);
            
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Bulk action error: ' . $e->getMessage());
            return redirect()->route('employees.index')
                ->with('error', 'Error performing bulk action: ' . $e->getMessage());
        }
    }
    
    /**
     * Import employees from Excel file
     */
    public function import(Request $request)
    {
        // Increase execution time limit for large imports
        ini_set('max_execution_time', 600); // 10 minutes

        $request->validate([
            'excel_file' => 'required|file|mimes:xlsx,xls,csv,txt|max:20480',
            'has_header' => 'nullable',
        ]);

        \Log::info('📊 Employee import started', [
            'user_id' => auth()->id(),
            'file_name' => $request->file('excel_file')->getClientOriginalName(),
            'has_header' => $request->has('has_header'),
        ]);

        try {
            $file = $request->file('excel_file');
            $extension = strtolower($file->getClientOriginalExtension());

            // Store the file temporarily
            $filePath = $file->storeAs('temp_imports', 'emp_import_' . time() . '.' . $extension);
            $fullPath = storage_path('app/' . $filePath);

            \Log::info('📁 File stored', [
                'path' => $fullPath,
                'extension' => $extension,
                'size' => filesize($fullPath) . ' bytes'
            ]);

            // Read file data
            $rows = [];

            if ($extension === 'csv') {
                // Handle CSV files
                if (($handle = fopen($fullPath, "r")) !== FALSE) {
                    while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
                        $rows[] = $data;
                    }
                    fclose($handle);
                }
            } else {
                // Handle Excel files
                if (!class_exists('ZipArchive')) {
                    throw new \Exception('ZipArchive extension is required for Excel files. Please convert your file to CSV format and try again.');
                }

                try {
                    $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReaderForFile($fullPath);
                    $reader->setReadDataOnly(true);

                    // Set encoding options for better Arabic text support
                    if (method_exists($reader, 'setInputEncoding')) {
                        $reader->setInputEncoding('UTF-8');
                    }

                    $spreadsheet = $reader->load($fullPath);
                    $worksheet = $spreadsheet->getActiveSheet();
                    $rows = $worksheet->toArray();
                } catch (\Exception $e) {
                    \Log::error('❌ Failed to read Excel file', [
                        'error' => $e->getMessage(),
                        'file' => $fullPath,
                    ]);
                    throw new \Exception('Failed to read Excel file: ' . $e->getMessage() . '. Please try converting to CSV format.');
                }
            }

            // Skip header row if indicated
            if ($request->has('has_header')) {
                array_shift($rows);
            }

            list($successCount, $errorCount, $skippedEmptyRows) = $this->processEmployeeRows($rows);

            // Clean up the temp file
            if (file_exists($fullPath)) {
                unlink($fullPath);
            }

            // Prepare success message
            $message = "$successCount employees imported successfully.";
            if ($skippedEmptyRows > 0) {
                $message .= " $skippedEmptyRows empty rows were skipped.";
            }

            if ($errorCount > 0) {
                return redirect()->route('employees.index')
                    ->with('success', $message)
                    ->with('error', "$errorCount employees could not be imported due to errors (missing required fields or duplicate usernames).");
            }

            return redirect()->route('employees.index')
                ->with('success', $message);

        } catch (\Exception $e) {
            \Log::error('❌ Employee import error: ' . $e->getMessage());
            return redirect()->route('employees.index')
                ->with('error', 'Error importing file: ' . $e->getMessage());
        }
    }

    /**
     * Process employee rows from imported file
     */
    private function processEmployeeRows($rows)
    {
        $successCount = 0;
        $errorCount = 0;
        $skippedEmptyRows = 0;

        foreach ($rows as $index => $row) {
            // Skip completely empty rows
            if (empty($row) || (count($row) == 1 && empty(trim($row[0] ?? '')))) {
                $skippedEmptyRows++;
                continue;
            }

            // Skip rows where all cells are empty
            $hasData = false;
            foreach ($row as $cell) {
                if (!empty(trim($cell ?? ''))) {
                    $hasData = true;
                    break;
                }
            }
            if (!$hasData) {
                $skippedEmptyRows++;
                continue;
            }

            // Check if data is semicolon-separated in first column
            if (count($row) == 1 && strpos($row[0], ';') !== false) {
                $row = explode(';', $row[0]);
                \Log::info('🔧 Fixed semicolon-separated data for employee', [
                    'original_count' => 1,
                    'new_count' => count($row),
                    'first_field' => $row[0] ?? 'N/A'
                ]);
            }

            try {
                \Log::info('👤 Processing employee row', [
                    'row_index' => $index,
                    'data_count' => count($row),
                    'first_field' => $row[0] ?? 'N/A'
                ]);

                // Expected columns: full_name, contract_type, employee_type, phone, job_status, automatic_number, financial_number, state_cooperative_number, bank_account_number, username, password
                // Ensure proper UTF-8 encoding for Arabic text
                $fullName = trim($this->ensureUtf8($row[0] ?? ''));
                $contractType = trim($this->ensureUtf8($row[1] ?? ''));

                // Check if the data seems to be in wrong order (phone before employee_type)
                // If row[2] looks like a phone number (digits) and row[3] looks like employee type (text), swap them
                $possiblePhone = trim($row[2] ?? '');
                $possibleEmployeeType = trim($this->ensureUtf8($row[3] ?? ''));

                if (preg_match('/^\d+$/', $possiblePhone) && !preg_match('/^\d+$/', $possibleEmployeeType)) {
                    // Data seems to be: full_name, contract_type, phone, employee_type, job_status
                    $phone = $possiblePhone;
                    $employeeType = $possibleEmployeeType;
                    $jobStatus = trim($this->ensureUtf8($row[4] ?? 'نشط'));
                } else {
                    // Data is in expected order: full_name, contract_type, employee_type, phone, job_status
                    $employeeType = $this->ensureUtf8($possiblePhone);
                    $phone = $possibleEmployeeType;
                    $jobStatus = trim($this->ensureUtf8($row[4] ?? 'نشط'));
                }
                $automaticNumber = trim($row[5] ?? '');
                $financialNumber = trim($row[6] ?? '');
                $stateCooperativeNumber = trim($row[7] ?? '');
                $bankAccountNumber = trim($row[8] ?? '');
                $username = trim($row[9] ?? '');
                $password = trim($row[10] ?? 'emp123');

                // Validate required fields
                if (empty($fullName)) {
                    \Log::warning('⚠️ Skipping row - missing full_name', ['row_index' => $index]);
                    $errorCount++;
                    continue;
                }

                if (empty($contractType)) {
                    \Log::warning('⚠️ Skipping row - missing contract_type', ['row_index' => $index]);
                    $errorCount++;
                    continue;
                }

                if (empty($employeeType)) {
                    \Log::warning('⚠️ Skipping row - missing employee_type', ['row_index' => $index]);
                    $errorCount++;
                    continue;
                }

                // Generate username if not provided
                if (empty($username)) {
                    $username = 'emp' . str_pad($index + 1, 4, '0', STR_PAD_LEFT);
                }

                // Check if username already exists
                if (Employee::where('username', $username)->exists()) {
                    \Log::warning('⚠️ Username already exists', ['username' => $username]);
                    $errorCount++;
                    continue;
                }

                $employee = new Employee();
                $employee->full_name = $fullName;
                $employee->contract_type = $contractType;
                $employee->employee_type = $employeeType;
                $employee->phone = $phone;
                $employee->job_status = $jobStatus;
                $employee->automatic_number = $automaticNumber;
                $employee->financial_number = $financialNumber;
                $employee->state_cooperative_number = $stateCooperativeNumber;
                $employee->bank_account_number = $bankAccountNumber;
                $employee->username = $username;
                $employee->password = Hash::make($password);
                $employee->save();

                \Log::info('✅ Employee saved successfully', [
                    'id' => $employee->id,
                    'username' => $employee->username,
                    'full_name' => $employee->full_name
                ]);

                $successCount++;

            } catch (\Exception $e) {
                \Log::error('❌ Error processing employee row', [
                    'row_index' => $index,
                    'error' => $e->getMessage(),
                    'data' => $row
                ]);
                $errorCount++;
            }
        }

        // Log summary
        \Log::info('📊 Import Summary', [
            'successful' => $successCount,
            'errors' => $errorCount,
            'skipped_empty_rows' => $skippedEmptyRows,
            'total_processed' => $successCount + $errorCount,
            'total_rows_in_file' => count($rows)
        ]);

        return [$successCount, $errorCount, $skippedEmptyRows];
    }
    
    
    
    /**
     * Download an Excel template for employee imports
     * 
     * @return \Illuminate\Http\Response
     */
    public function exportTemplate()
    {
        // Get the unique values for the dynamic dropdown fields
        $contractTypes = Employee::select('contract_type')
            ->distinct()
            ->whereNotNull('contract_type')
            ->where('contract_type', '!=', '')
            ->where('contract_type', 'NOT LIKE', '%?%') // Exclude corrupted data
            ->orderBy('contract_type')
            ->pluck('contract_type')
            ->toArray();

        $employeeTypes = Employee::select('employee_type')
            ->distinct()
            ->whereNotNull('employee_type')
            ->where('employee_type', '!=', '')
            ->where('employee_type', 'NOT LIKE', '%?%') // Exclude corrupted data
            ->orderBy('employee_type')
            ->pluck('employee_type')
            ->toArray();

        $jobStatuses = Employee::select('job_status')
            ->distinct()
            ->whereNotNull('job_status')
            ->where('job_status', '!=', '')
            ->where('job_status', 'NOT LIKE', '%?%') // Exclude corrupted data
            ->orderBy('job_status')
            ->pluck('job_status')
            ->toArray();
        
        // Set default values if no data exists yet
        if (empty($contractTypes)) {
            $contractTypes = ['Full-time', 'Part-time', 'Contract'];
        }
        
        if (empty($employeeTypes)) {
            $employeeTypes = ['Administrative', 'Academic', 'Technical', 'Support', 'Consultant'];
        }
        
        if (empty($jobStatuses)) {
            $jobStatuses = ['Active', 'On Leave', 'Terminated'];
        }
        
        // Define column headers (removed employee_id)
        $headers = [
            'full_name',
            'contract_type',
            'employee_type',
            'phone',
            'job_status',
            'automatic_number',
            'financial_number',
            'state_cooperative_number',
            'bank_account_number',
            'username',
            'password'
        ];
        
        // Create content with headers and a sample row
        $content = implode(',', $headers) . "\n";
        
        // Add a sample row with examples, including actual values for dropdowns
        $sampleData = [
            'John Doe', // full_name
            $contractTypes[0], // contract_type (first value from the actual options)
            $employeeTypes[0], // employee_type (first value from the actual options)
            '************', // phone
            $jobStatuses[0], // job_status (first value from the actual options)
            'A12345', // automatic_number
            'F67890', // financial_number
            'SC12345', // state_cooperative_number
            '**********', // bank_account_number
            'johndoe', // username
            'leave_blank_for_default' // password
        ];
        
        $content .= implode(',', $sampleData);
        
        // Add dropdown options as comments in additional rows
        $content .= "\n\n# Available Contract Types: " . implode(', ', $contractTypes);
        $content .= "\n# Available Employee Types: " . implode(', ', $employeeTypes);
        $content .= "\n# Available Job Statuses: " . implode(', ', $jobStatuses);
        
        return response($content)
            ->header('Content-Type', 'text/csv')
            ->header('Content-Disposition', 'attachment; filename="employee_import_template.csv"');
    }

    /**
     * Refresh the employee dropdown caches
     */
    public function refreshDropdownCache()
    {
        // Clear existing caches
        cache()->forget('contract_types');
        cache()->forget('employee_types');
        cache()->forget('job_statuses');
        
        // Force refresh cache with current database values
        $contractTypes = Employee::select('contract_type')
            ->distinct()
            ->whereNotNull('contract_type')
            ->where('contract_type', '!=', '')
            ->where('contract_type', 'NOT LIKE', '%?%') // Exclude corrupted data
            ->orderBy('contract_type')
            ->pluck('contract_type')
            ->toArray();

        $employeeTypes = Employee::select('employee_type')
            ->distinct()
            ->whereNotNull('employee_type')
            ->where('employee_type', '!=', '')
            ->where('employee_type', 'NOT LIKE', '%?%') // Exclude corrupted data
            ->orderBy('employee_type')
            ->pluck('employee_type')
            ->toArray();

        $jobStatuses = Employee::select('job_status')
            ->distinct()
            ->whereNotNull('job_status')
            ->where('job_status', '!=', '')
            ->where('job_status', 'NOT LIKE', '%?%') // Exclude corrupted data
            ->orderBy('job_status')
            ->pluck('job_status')
            ->toArray();
        
        // Debug dump the actual values from database
        $debugInfo = [
            'Employee Types from DB' => $employeeTypes,
            'Count' => count($employeeTypes)
        ];
        
        // For debugging only - add all employee types to default list if empty
        if (empty($employeeTypes)) {
            $employeeTypes = ['موظف', 'غير موظف']; // Arabic defaults
        }
        
        // Store in cache for 1 hour
        cache()->put('contract_types', $contractTypes, 60*60);
        cache()->put('employee_types', $employeeTypes, 60*60);
        cache()->put('job_statuses', $jobStatuses, 60*60);
        
        return redirect()->route('employees.index')
            ->with('debug', $debugInfo)
            ->with('success', 'Employee dropdown data refreshed. Please check the debug information.');
    }

    /**
     * Ensure proper UTF-8 encoding for Arabic text
     */
    private function ensureUtf8($text)
    {
        if (empty($text)) {
            return $text;
        }

        // If the text is already UTF-8, return as is
        if (mb_check_encoding($text, 'UTF-8')) {
            return $text;
        }

        // Try to convert from common encodings to UTF-8
        $encodings = ['Windows-1256', 'ISO-8859-6', 'CP1256', 'UTF-8'];

        foreach ($encodings as $encoding) {
            $converted = @mb_convert_encoding($text, 'UTF-8', $encoding);
            if ($converted !== false && mb_check_encoding($converted, 'UTF-8')) {
                return $converted;
            }
        }

        // If all else fails, return the original text
        return $text;
    }
}
