import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/auth_service.dart';
import '../services/student_service.dart';
import '../utils/constants.dart';
import 'login_selection_screen.dart';
import 'student_notifications_screen.dart';
import 'student_profile_screen.dart';

class StudentDashboardScreen extends StatefulWidget {
  const StudentDashboardScreen({super.key});

  @override
  State<StudentDashboardScreen> createState() => _StudentDashboardScreenState();
}

class _StudentDashboardScreenState extends State<StudentDashboardScreen> {
  bool _isLoading = false;
  Map<String, dynamic>? _dashboardData;

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    setState(() => _isLoading = true);

    try {
      // Load real dashboard data from API
      final dashboardData = await StudentService.getDashboardData();

      setState(() {
        _dashboardData = dashboardData;
      });
    } catch (e) {
      print('Error loading dashboard data: $e');

      // Set default data in case of error
      setState(() {
        _dashboardData = {
          'notifications': {
            'total': 0,
            'unread': 0,
          },
        };
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل البيانات: $e'),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'إعادة المحاولة',
              textColor: Colors.white,
              onPressed: _loadDashboardData,
            ),
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isTablet = screenWidth > 600;
    final isLandscape = screenWidth > screenHeight;
    
    // Responsive sizing
    final horizontalPadding = isTablet ? screenWidth * 0.05 : 16.0;
    final cardPadding = isTablet ? 20.0 : 16.0;
    final spacing = isTablet ? 20.0 : 16.0;
    final fontSize = isTablet ? 16.0 : 14.0;
    final titleFontSize = isTablet ? 20.0 : 18.0;

    return Consumer<AuthService>(
      builder: (context, authService, child) {
        final currentUser = authService.currentUser;
        
        return Scaffold(
          backgroundColor: const Color(0xFFF5F5DC),
          appBar: AppBar(
            title: Text(
              'لوحة تحكم الطالب',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: isTablet ? 22 : 18,
              ),
            ),
            backgroundColor: AppColors.studentPrimary,
            foregroundColor: Colors.white,
            elevation: 0,
            actions: [
              IconButton(
                onPressed: _loadDashboardData,
                icon: const Icon(Icons.refresh),
                tooltip: 'تحديث',
              ),
              PopupMenuButton<String>(
                onSelected: (value) => _handleMenuAction(context, value, authService),
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'profile',
                    child: Row(
                      children: [
                        Icon(Icons.person, color: Colors.blue),
                        SizedBox(width: 8),
                        Text('الملف الشخصي'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'settings',
                    child: Row(
                      children: [
                        Icon(Icons.settings, color: Colors.grey),
                        SizedBox(width: 8),
                        Text('الإعدادات'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'logout',
                    child: Row(
                      children: [
                        Icon(Icons.logout, color: Colors.red),
                        SizedBox(width: 8),
                        Text('تسجيل الخروج'),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          body: _isLoading
              ? const Center(
                  child: CircularProgressIndicator(
                    color: AppColors.studentPrimary,
                  ),
                )
              : RefreshIndicator(
                  onRefresh: _loadDashboardData,
                  color: AppColors.studentPrimary,
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    padding: EdgeInsets.symmetric(
                      horizontal: horizontalPadding,
                      vertical: spacing,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Welcome Section
                        _buildWelcomeSection(currentUser, cardPadding, fontSize, titleFontSize),
                        SizedBox(height: spacing),
                        
                        // Statistics Cards
                        _buildStatisticsSection(cardPadding, spacing, fontSize, titleFontSize, isLandscape),
                        SizedBox(height: spacing),
                        
                        // Quick Actions
                        _buildQuickActionsSection(cardPadding, spacing, fontSize, titleFontSize, isLandscape),
                        SizedBox(height: spacing),
                        
                        // Recent Notifications
                        _buildRecentNotificationsSection(cardPadding, spacing, fontSize, titleFontSize),
                      ],
                    ),
                  ),
                ),
        );
      },
    );
  }

  Widget _buildWelcomeSection(dynamic currentUser, double cardPadding, double fontSize, double titleFontSize) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(cardPadding),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.studentPrimary,
            AppColors.studentPrimary.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.2),
            spreadRadius: 2,
            blurRadius: 10,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.school,
                color: Colors.white,
                size: titleFontSize * 1.5,
              ),
              SizedBox(width: cardPadding * 0.5),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'مرحباً بك',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: fontSize,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      currentUser?.name ?? 'الطالب',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: titleFontSize,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: cardPadding * 0.5),
          Text(
            'نتمنى لك يوماً دراسياً مثمراً',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.9),
              fontSize: fontSize * 0.9,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatisticsSection(double cardPadding, double spacing, double fontSize, double titleFontSize, bool isLandscape) {
    if (_dashboardData == null) return const SizedBox.shrink();

    final stats = [
      {
        'title': 'الإشعارات',
        'value': '${_dashboardData!['notifications']['total']}',
        'subtitle': '${_dashboardData!['notifications']['unread']} غير مقروءة',
        'icon': Icons.notifications,
        'color': Colors.orange,
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الإحصائيات',
          style: TextStyle(
            fontSize: titleFontSize,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: spacing * 0.8),
        // Single notification card - centered
        Center(
          child: SizedBox(
            width: isLandscape ? 300 : double.infinity,
            child: _buildStatCard(
              title: stats[0]['title'] as String,
              value: stats[0]['value'] as String,
              subtitle: stats[0]['subtitle'] as String,
              icon: stats[0]['icon'] as IconData,
              color: stats[0]['color'] as Color,
              cardPadding: cardPadding,
              fontSize: fontSize,
              titleFontSize: titleFontSize,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required String subtitle,
    required IconData icon,
    required Color color,
    required double cardPadding,
    required double fontSize,
    required double titleFontSize,
  }) {
    return Container(
      padding: EdgeInsets.all(cardPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            color: color,
            size: titleFontSize * 1.8,
          ),
          SizedBox(height: cardPadding * 0.3),
          Text(
            value,
            style: TextStyle(
              fontSize: titleFontSize * 1.2,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          SizedBox(height: cardPadding * 0.2),
          Text(
            title,
            style: TextStyle(
              fontSize: fontSize,
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: cardPadding * 0.1),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: fontSize * 0.8,
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionsSection(double cardPadding, double spacing, double fontSize, double titleFontSize, bool isLandscape) {
    final actions = [
      {
        'title': 'الإشعارات',
        'subtitle': 'عرض جميع الإشعارات',
        'icon': Icons.notifications,
        'color': Colors.orange,
        'onTap': () => _navigateToNotifications(),
      },
      {
        'title': 'الملف الشخصي',
        'subtitle': 'عرض وتعديل البيانات',
        'icon': Icons.person,
        'color': Colors.blue,
        'onTap': () => _navigateToProfile(),
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الإجراءات السريعة',
          style: TextStyle(
            fontSize: titleFontSize,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: spacing * 0.8),
        // Two action cards in a row
        Row(
          children: actions.map((action) => Expanded(
            child: Container(
              margin: EdgeInsets.only(
                right: actions.indexOf(action) == 0 ? spacing * 0.4 : 0,
                left: actions.indexOf(action) == 1 ? spacing * 0.4 : 0,
              ),
              child: _buildActionCard(
                title: action['title'] as String,
                subtitle: action['subtitle'] as String,
                icon: action['icon'] as IconData,
                color: action['color'] as Color,
                onTap: action['onTap'] as VoidCallback,
                cardPadding: cardPadding,
                fontSize: fontSize,
                titleFontSize: titleFontSize,
              ),
            ),
          )).toList(),
        ),
      ],
    );
  }

  Widget _buildActionCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
    required double cardPadding,
    required double fontSize,
    required double titleFontSize,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: EdgeInsets.all(cardPadding),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              spreadRadius: 1,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: EdgeInsets.all(cardPadding * 0.6),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                icon,
                color: color,
                size: titleFontSize * 1.5,
              ),
            ),
            SizedBox(height: cardPadding * 0.5),
            Text(
              title,
              style: TextStyle(
                fontSize: fontSize,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: cardPadding * 0.2),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: fontSize * 0.8,
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentNotificationsSection(double cardPadding, double spacing, double fontSize, double titleFontSize) {
    // Mock recent notifications
    final recentNotifications = [
      {
        'title': 'إشعار جديد من الإدارة',
        'message': 'يرجى مراجعة الجدول الزمني المحدث للفصل الدراسي',
        'time': 'منذ ساعتين',
        'isRead': false,
      },
      {
        'title': 'تذكير بموعد الامتحان',
        'message': 'امتحان مادة الرياضيات يوم الأحد القادم',
        'time': 'منذ 4 ساعات',
        'isRead': true,
      },
      {
        'title': 'نتائج الواجب',
        'message': 'تم رفع نتائج واجب مادة العلوم',
        'time': 'أمس',
        'isRead': true,
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'الإشعارات الحديثة',
              style: TextStyle(
                fontSize: titleFontSize,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            TextButton(
              onPressed: _navigateToNotifications,
              child: Text(
                'عرض الكل',
                style: TextStyle(
                  fontSize: fontSize,
                  color: AppColors.studentPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: spacing * 0.8),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                spreadRadius: 1,
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: recentNotifications.length,
            separatorBuilder: (context, index) => Divider(
              height: 1,
              color: Colors.grey.shade200,
            ),
            itemBuilder: (context, index) {
              final notification = recentNotifications[index];
              return _buildNotificationItem(
                title: notification['title'] as String,
                message: notification['message'] as String,
                time: notification['time'] as String,
                isRead: notification['isRead'] as bool,
                cardPadding: cardPadding,
                fontSize: fontSize,
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildNotificationItem({
    required String title,
    required String message,
    required String time,
    required bool isRead,
    required double cardPadding,
    required double fontSize,
  }) {
    return InkWell(
      onTap: _navigateToNotifications,
      child: Container(
        padding: EdgeInsets.all(cardPadding),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: 8,
              height: 8,
              margin: EdgeInsets.only(top: fontSize * 0.5),
              decoration: BoxDecoration(
                color: isRead ? Colors.grey.shade300 : AppColors.studentPrimary,
                shape: BoxShape.circle,
              ),
            ),
            SizedBox(width: cardPadding * 0.8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: fontSize,
                      fontWeight: isRead ? FontWeight.w500 : FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: cardPadding * 0.2),
                  Text(
                    message,
                    style: TextStyle(
                      fontSize: fontSize * 0.9,
                      color: AppColors.textSecondary,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: cardPadding * 0.2),
                  Text(
                    time,
                    style: TextStyle(
                      fontSize: fontSize * 0.8,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.chevron_right,
              color: AppColors.textSecondary,
              size: fontSize * 1.2,
            ),
          ],
        ),
      ),
    );
  }

  void _handleMenuAction(BuildContext context, String action, AuthService authService) {
    switch (action) {
      case 'profile':
        _navigateToProfile();
        break;
      case 'settings':
        _showComingSoon('الإعدادات');
        break;
      case 'logout':
        _showLogoutDialog(context, authService);
        break;
    }
  }

  void _navigateToNotifications() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const StudentNotificationsScreen(),
      ),
    );
  }

  void _navigateToProfile() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const StudentProfileScreen(),
      ),
    );
  }

  void _showComingSoon(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('ميزة $feature ستكون متاحة قريباً'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _showLogoutDialog(BuildContext context, AuthService authService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من رغبتك في تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await authService.logout();
              if (context.mounted) {
                Navigator.pushAndRemoveUntil(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const LoginSelectionScreen(),
                  ),
                  (route) => false,
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );
  }
}
