@extends('layouts.app')

@section('content')
<div class="container main-content">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 style="color: var(--costa-del-sol);">Student Management</h2>
        <div>
            <button type="button" class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#addStudentModal">
                <i class="fas fa-plus"></i> Add Student
            </button>
            <button type="button" class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#importExcelModal">
                <i class="fas fa-file-excel"></i> Import from Excel
            </button>
            <div class="dropdown d-inline-block bulk-actions" style="display: none;">
                <button class="btn btn-secondary dropdown-toggle" type="button" id="bulkActionsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    Bulk Actions
                </button>
                <ul class="dropdown-menu" aria-labelledby="bulkActionsDropdown">
                    <li><a class="dropdown-item bulk-action" href="#" data-action="activate">Activate Selected</a></li>
                    <li><a class="dropdown-item bulk-action" href="#" data-action="deactivate">Deactivate Selected</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item bulk-action text-danger" href="#" data-action="delete">Delete Selected</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item bulk-action text-danger fw-bold" href="#" data-action="delete-all">Delete All Students</a></li>
                </ul>
            </div>
        </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success">
            {{ session('success') }}
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger">
            {{ session('error') }}
        </div>
    @endif

    <!-- Students Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <span>Students List</span>
            <form class="d-flex" action="{{ route('students.index') }}" method="GET">
                <input class="form-control me-2" type="search" name="search" placeholder="Search by name or class" value="{{ request('search') }}">
                <button class="btn btn-outline-success" type="submit">Search</button>
            </form>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped student-table">
                    <thead>
                        <tr>
                            <th>
                                <div class="form-check">
                                    <input class="form-check-input select-all" type="checkbox" id="selectAll">
                                </div>
                            </th>
                            <th>ID</th>
                            <th>Username</th>
                            <th>Full Name</th>
                            <th>Phone</th>
                            <th>Class</th>
                            <th>Level</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($students as $student)
                            <tr>
                                <td>
                                    <div class="form-check">
                                        <input class="form-check-input student-checkbox" type="checkbox" value="{{ $student->id }}" id="student-{{ $student->id }}">
                                    </div>
                                </td>
                                <td>{{ $student->id }}</td>
                                <td>{{ $student->username }}</td>
                                <td>{{ $student->full_name }}</td>
                                <td>{{ $student->phone }}</td>
                                <td>{{ $student->class }}</td>
                                <td>{{ $student->level }}</td>
                                <td>
                                    <span class="badge bg-{{ $student->is_active ? 'success' : 'danger' }}">
                                        {{ $student->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-primary edit-student" 
                                        data-id="{{ $student->id }}"
                                        data-username="{{ $student->username }}"
                                        data-name="{{ $student->full_name }}"
                                        data-phone="{{ $student->phone }}"
                                        data-specialization="{{ $student->specialization }}"
                                        data-section="{{ $student->section }}"
                                        data-class="{{ $student->class }}"
                                        data-level="{{ $student->level }}"
                                        data-result="{{ $student->result }}"
                                        data-nationality="{{ $student->nationality }}"
                                        data-active="{{ $student->is_active }}"
                                        data-bs-toggle="modal" 
                                        data-bs-target="#editStudentModal">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-danger" 
                                        onclick="if(confirm('Are you sure you want to delete this student?')) { document.getElementById('delete-student-{{ $student->id }}').submit(); }">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <form id="delete-student-{{ $student->id }}" action="{{ route('students.destroy', $student->id) }}" method="POST" style="display: none;">
                                        @csrf
                                        @method('DELETE')
                                    </form>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="9" class="text-center">No students found</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
            <div class="d-flex justify-content-center mt-4">
                {{ $students->links('pagination::bootstrap-5') }}
            </div>
        </div>
    </div>
</div>

<!-- Add Student Modal -->
<div class="modal fade" id="addStudentModal" tabindex="-1" aria-labelledby="addStudentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header" style="background-color: var(--thistle-green);">
                <h5 class="modal-title" id="addStudentModalLabel" style="color: var(--costa-del-sol);">Add New Student</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ route('students.store') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" name="username" required placeholder="e.g. stu0001">
                        <small class="text-muted">Format: 'stu' followed by 4 digits</small>
                    </div>
                    <div class="mb-3">
                        <label for="full_name" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="full_name" name="full_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="nationality" class="form-label">Nationality</label>
                        <input type="text" class="form-control" id="nationality" name="nationality">
                    </div>
                    <div class="mb-3">
                        <label for="phone" class="form-label">Phone</label>
                        <input type="text" class="form-control" id="phone" name="phone">
                    </div>
                    <div class="mb-3">
                        <label for="specialization" class="form-label">Specialization</label>
                        <input type="text" class="form-control" id="specialization" name="specialization">
                    </div>
                    <div class="mb-3">
                        <label for="section" class="form-label">Section</label>
                        <input type="text" class="form-control" id="section" name="section">
                    </div>
                    <div class="mb-3">
                        <label for="class" class="form-label">Class</label>
                        <input type="text" class="form-control" id="class" name="class">
                    </div>
                    <div class="mb-3">
                        <label for="level" class="form-label">Level</label>
                        <input type="text" class="form-control" id="level" name="level">
                    </div>
                    <div class="mb-3">
                        <label for="result" class="form-label">Result</label>
                        <input type="text" class="form-control" id="result" name="result">
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                        <label class="form-check-label" for="is_active">Active</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Student</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Student Modal -->
<div class="modal fade" id="editStudentModal" tabindex="-1" aria-labelledby="editStudentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header" style="background-color: var(--thistle-green);">
                <h5 class="modal-title" id="editStudentModalLabel" style="color: var(--costa-del-sol);">Edit Student</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editStudentForm" action="" method="POST">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_username" class="form-label">Username</label>
                        <input type="text" class="form-control bg-light" id="edit_username" name="username" readonly disabled>
                        <small class="text-muted">Username cannot be changed</small>
                    </div>
                    <div class="mb-3">
                        <label for="edit_full_name" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="edit_full_name" name="full_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_nationality" class="form-label">Nationality</label>
                        <select class="form-select" id="edit_nationality" name="nationality">
                            <option value="">Select Nationality</option>
                            @foreach($nationalities as $nationality)
                                <option value="{{ $nationality }}">{{ $nationality }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_phone" class="form-label">Phone</label>
                        <input type="text" class="form-control" id="edit_phone" name="phone">
                    </div>
                    <div class="mb-3">
                        <label for="edit_specialization" class="form-label">Specialization</label>
                        <select class="form-select" id="edit_specialization" name="specialization">
                            <option value="">Select Specialization</option>
                            @foreach($specializations as $specialization)
                                <option value="{{ $specialization }}">{{ $specialization }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_section" class="form-label">Section</label>
                        <select class="form-select" id="edit_section" name="section">
                            <option value="">Select Section</option>
                            @foreach($sections as $section)
                                <option value="{{ $section }}">{{ $section }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_class" class="form-label">Class</label>
                        <select class="form-select" id="edit_class" name="class">
                            <option value="">Select Class</option>
                            @foreach($classes as $class)
                                <option value="{{ $class }}">{{ $class }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_level" class="form-label">Level</label>
                        <select class="form-select" id="edit_level" name="level">
                            <option value="">Select Level</option>
                            @foreach($levels as $level)
                                <option value="{{ $level }}">{{ $level }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_result" class="form-label">Result</label>
                        <select class="form-select" id="edit_result" name="result">
                            <option value="">Select Result</option>
                            @foreach($results as $result)
                                <option value="{{ $result }}">{{ $result }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_password" class="form-label">New Password (leave blank to keep current)</label>
                        <input type="password" class="form-control" id="edit_password" name="password">
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active">
                        <label class="form-check-label" for="edit_is_active">Active</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Student</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Import Excel Modal -->
<div class="modal fade" id="importExcelModal" tabindex="-1" aria-labelledby="importExcelModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header" style="background-color: var(--thistle-green);">
                <h5 class="modal-title" id="importExcelModalLabel" style="color: var(--costa-del-sol);">Import Students from Excel</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ route('students.import') }}" method="POST" enctype="multipart/form-data">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="excel_file" class="form-label">Excel File</label>
                        <input type="file" class="form-control" id="excel_file" name="excel_file" accept=".xlsx,.xls,.csv" required>
                    </div>
                    <div class="mb-3">
                        <div class="alert alert-info">
                            <h6>Excel File Format:</h6>
                            <p class="mb-1">Your Excel file should contain the following columns:</p>
                            <ul class="mb-0">
                                <li>username</li>
                                <li>full_name</li>
                                <li>nationality (optional)</li>
                                <li>phone (optional)</li>
                                <li>specialization (optional)</li>
                                <li>section (optional)</li>
                                <li>class (optional)</li>
                                <li>level (optional)</li>
                                <li>result (optional)</li>
                                <li>password</li>
                                <li>is_active (1 for active, 0 for inactive)</li>
                            </ul>
                        </div>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="has_header" name="has_header" checked>
                        <label class="form-check-label" for="has_header">File has header row</label>
                    </div>
                </div>
                <div class="progress mb-3" style="display: none;" id="import-progress-container">
                    <div id="import-progress-bar" class="progress-bar bg-success progress-bar-striped progress-bar-animated" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%">0%</div>
                </div>
                <div id="import-status" class="alert alert-info mb-3" style="display: none;"></div>
                <div class="modal-footer">
                    <a href="{{ route('students.export.template') }}" class="btn btn-outline-secondary">Download Template</a>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="import-submit-btn">Import</button>
                </div>
            </form>
        </div>
    </div>
</div>

@endsection

@section('scripts')
<script>
    // Excel Import with Progress Bar
    document.addEventListener('DOMContentLoaded', function() {
        const importForm = document.querySelector('#importExcelModal form');
        const progressContainer = document.getElementById('import-progress-container');
        const progressBar = document.getElementById('import-progress-bar');
        const importStatus = document.getElementById('import-status');
        const importSubmitBtn = document.getElementById('import-submit-btn');
        
        if (importForm) {
            importForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                // Show progress bar and status
                progressContainer.style.display = 'flex';
                importStatus.style.display = 'block';
                importStatus.textContent = 'Preparing to import...';
                importSubmitBtn.disabled = true;
                
                // Create FormData object
                const formData = new FormData(importForm);
                
                // Create unique import ID for this session
                const importId = 'import_' + Date.now();
                formData.append('import_id', importId);
                
                // Send AJAX request
                const xhr = new XMLHttpRequest();
                xhr.open('POST', importForm.action, true);
                xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
                
                xhr.upload.onprogress = function(e) {
                    if (e.lengthComputable) {
                        // This only tracks the file upload progress, not the processing
                        const uploadPercent = Math.round((e.loaded / e.total) * 20); // File upload is 20% of total progress
                        updateProgress(uploadPercent, 'Uploading file: ' + uploadPercent + '%');
                    }
                };
                
                xhr.onload = function() {
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            
                            if (response.success) {
                                // Start polling for progress updates
                                updateProgress(20, 'File uploaded. Starting import process...');
                                pollImportProgress(importId);
                            } else {
                                handleImportError(response.message || 'Error during import');
                            }
                        } catch (e) {
                            handleImportError('Invalid server response');
                        }
                    } else {
                        handleImportError('Server error: ' + xhr.status);
                    }
                };
                
                xhr.onerror = function() {
                    handleImportError('Network error during upload');
                };
                
                xhr.send(formData);
            });
        }
        
        function pollImportProgress(importId) {
            const progressCheckUrl = '/students/import-progress/' + importId;
            const checkInterval = setInterval(function() {
                fetch(progressCheckUrl)
                    .then(response => response.json())
                    .then(data => {
                        if (data.error) {
                            clearInterval(checkInterval);
                            handleImportError(data.error);
                            return;
                        }
                        
                        // Calculate progress: 20% for upload + 80% for processing
                        const totalProgress = 20 + Math.round(data.progress * 0.8);
                        updateProgress(totalProgress, data.message);
                        
                        if (data.completed) {
                            clearInterval(checkInterval);
                            updateProgress(100, 'Import completed successfully!');
                            
                            // Redirect after a short delay to show the 100% completion
                            setTimeout(function() {
                                window.location.href = '/students?import_success=true';
                            }, 1500);
                        }
                    })
                    .catch(error => {
                        clearInterval(checkInterval);
                        handleImportError('Error checking import progress');
                    });
            }, 1000); // Check every second
        }
        
        function updateProgress(percent, message) {
            progressBar.style.width = percent + '%';
            progressBar.setAttribute('aria-valuenow', percent);
            progressBar.textContent = percent + '%';
            importStatus.textContent = message;
        }
        
        function handleImportError(message) {
            importStatus.textContent = 'Error: ' + message;
            importStatus.className = 'alert alert-danger mb-3';
            importSubmitBtn.disabled = false;
            progressBar.className = 'progress-bar bg-danger';
        }
    });
</script>
<style>
    /* Custom checkbox styling using the app's color palette */
    .form-check-input:checked {
        background-color: var(--costa-del-sol) !important;
        border-color: var(--costa-del-sol) !important;
    }
    
    .form-check-input:focus {
        border-color: var(--locust);
        box-shadow: 0 0 0 0.25rem rgba(93, 110, 53, 0.25);
    }
    
    .form-check-input {
        cursor: pointer;
        border: 2px solid var(--gurkha) !important;
        background-color: var(--parchment) !important;
        width: 20px;
        height: 20px;
    }
    
    /* Ensure the checkmark is visible */
    .form-check-input:checked::after {
        content: '✓';
        position: absolute;
        color: white;
        font-size: 14px;
        top: -2px;
        left: 3px;
    }
    
    /* Style the checkbox column */
    .student-table th:first-child,
    .student-table td:first-child {
        /* background-color: var(--thistle-green); */
        width: 50px;
        text-align: center;
        vertical-align: middle;
    }
    
    /* Add some spacing and border to the table */
    .student-table {
        border: 1px solid var(--chino);
    }
    
    .student-table th {
        background-color: var(--coral-reef);
    }
    
    /* Custom pagination styling */
    .pagination {
        --bs-pagination-color: var(--costa-del-sol);
        --bs-pagination-bg: var(--parchment);
        --bs-pagination-border-color: var(--chino);
        --bs-pagination-hover-color: white;
        --bs-pagination-hover-bg: var(--gurkha);
        --bs-pagination-hover-border-color: var(--gurkha);
        --bs-pagination-focus-color: var(--costa-del-sol);
        --bs-pagination-focus-bg: var(--thistle-green);
        --bs-pagination-focus-box-shadow: 0 0 0 0.25rem rgba(93, 110, 53, 0.25);
        --bs-pagination-active-color: white;
        --bs-pagination-active-bg: var(--costa-del-sol);
        --bs-pagination-active-border-color: var(--costa-del-sol);
    }
    
    .page-item.active .page-link {
        background-color: var(--costa-del-sol) !important;
        border-color: var(--costa-del-sol) !important;
        color: white !important;
    }
    
    .page-link {
        color: var(--costa-del-sol) !important;
    }
    
    .page-link:hover {
        background-color: var(--gurkha) !important;
        border-color: var(--gurkha) !important;
        color: white !important;
    }
    
    /* Style for bulk actions dropdown */
    .dropdown-item:active {
        background-color: var(--costa-del-sol);
    }
    
    .bulk-actions .btn-secondary {
        background-color: var(--gurkha);
        border-color: var(--gurkha);
    }
    
    .bulk-actions .btn-secondary:hover {
        background-color: var(--costa-del-sol);
        border-color: var(--costa-del-sol);
    }
</style>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Set up edit student modal
        const editButtons = document.querySelectorAll('.edit-student');
        editButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.dataset.id;
                const username = this.dataset.username;
                const name = this.dataset.name;
                const phone = this.dataset.phone;
                const specialization = this.dataset.specialization;
                const section = this.dataset.section;
                const className = this.dataset.class;
                const level = this.dataset.level;
                const result = this.dataset.result;
                const nationality = this.dataset.nationality;
                const isActive = this.dataset.active === '1';
                
                document.getElementById('edit_username').value = username;
                document.getElementById('edit_full_name').value = name;
                document.getElementById('edit_nationality').value = nationality;
                document.getElementById('edit_phone').value = phone;
                document.getElementById('edit_specialization').value = specialization;
                document.getElementById('edit_section').value = section;
                document.getElementById('edit_class').value = className;
                document.getElementById('edit_level').value = level;
                document.getElementById('edit_result').value = result;
                document.getElementById('edit_password').value = '';
                document.getElementById('edit_is_active').checked = isActive;
                
                document.getElementById('editStudentForm').action = `/students/${id}`;
            });
        });
        
        // Bulk selection functionality
        const selectAllCheckbox = document.getElementById('selectAll');
        const studentCheckboxes = document.querySelectorAll('.student-checkbox');
        const bulkActionsDiv = document.querySelector('.bulk-actions');
        
        // Function to update bulk actions visibility
        function updateBulkActionsVisibility() {
            const checkedBoxes = document.querySelectorAll('.student-checkbox:checked');
            if (checkedBoxes.length > 0) {
                bulkActionsDiv.style.display = 'inline-block';
            } else {
                bulkActionsDiv.style.display = 'none';
            }
        }
        
        // Select all checkbox
        selectAllCheckbox.addEventListener('change', function() {
            studentCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActionsVisibility();
        });
        
        // Individual checkboxes
        studentCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                // Update select all checkbox
                const allChecked = document.querySelectorAll('.student-checkbox:checked').length === studentCheckboxes.length;
                selectAllCheckbox.checked = allChecked;
                
                // Show/hide bulk actions
                updateBulkActionsVisibility();
            });
        });
        
        // Bulk actions
        const bulkActions = document.querySelectorAll('.bulk-action');
        bulkActions.forEach(action => {
            action.addEventListener('click', function(e) {
                e.preventDefault();
                
                const actionType = this.dataset.action;
                const selectedIds = [];
                
                // For delete-all action, we don't need selected checkboxes
                if (actionType !== 'delete-all') {
                    document.querySelectorAll('.student-checkbox:checked').forEach(checkbox => {
                        selectedIds.push(checkbox.value);
                    });
                    
                    if (selectedIds.length === 0) {
                        alert('Please select at least one student');
                        return;
                    }
                }
                
                let confirmMessage = '';
                
                switch(actionType) {
                    case 'activate':
                        confirmMessage = 'Are you sure you want to activate the selected students?';
                        break;
                    case 'deactivate':
                        confirmMessage = 'Are you sure you want to deactivate the selected students?';
                        break;
                    case 'delete':
                        confirmMessage = 'Are you sure you want to delete the selected students? This action cannot be undone.';
                        break;
                    case 'delete-all':
                        confirmMessage = 'WARNING! You are about to delete ALL students from the database. This action CANNOT be undone and will remove ALL student records. Are you absolutely sure you want to proceed?';
                        break;
                }
                
                if (confirm(confirmMessage)) {
                    // Create a form to submit the bulk action
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = '{{ route("students.bulk") }}';
                    
                    // Add CSRF token
                    const csrfToken = document.createElement('input');
                    csrfToken.type = 'hidden';
                    csrfToken.name = '_token';
                    csrfToken.value = '{{ csrf_token() }}';
                    form.appendChild(csrfToken);
                    
                    // Add action type
                    const actionInput = document.createElement('input');
                    actionInput.type = 'hidden';
                    actionInput.name = 'action';
                    actionInput.value = actionType;
                    form.appendChild(actionInput);
                    
                    // Add selected IDs
                    selectedIds.forEach(id => {
                        const idInput = document.createElement('input');
                        idInput.type = 'hidden';
                        idInput.name = 'ids[]';
                        idInput.value = id;
                        form.appendChild(idInput);
                    });
                    
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });
</script>
@endsection
