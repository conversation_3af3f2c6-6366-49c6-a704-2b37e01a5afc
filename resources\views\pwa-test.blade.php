@extends('layouts.app')

@section('content')
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header text-center" style="background: var(--costa-del-sol); color: white;">
                    <h1><i class="fas fa-mobile-alt me-2"></i>PWA Test Dashboard</h1>
                    <p class="mb-0">اختبار تطبيق الويب التقدمي</p>
                </div>
                <div class="card-body p-4">
                    <div id="test-results">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                            <p class="mt-2">جاري فحص PWA...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        runPWATests();
    });

    async function runPWATests() {
        const results = [];
        
        // Test 1: Service Worker Support
        results.push({
            name: 'Service Worker Support',
            nameAr: 'دعم Service Worker',
            status: 'serviceWorker' in navigator ? 'pass' : 'fail',
            message: 'serviceWorker' in navigator ? 'مدعوم' : 'غير مدعوم'
        });

        // Test 2: Manifest
        try {
            const response = await fetch('/manifest.json');
            const manifest = await response.json();
            results.push({
                name: 'Manifest File',
                nameAr: 'ملف Manifest',
                status: manifest.name ? 'pass' : 'fail',
                message: manifest.name ? `تم العثور على: ${manifest.name}` : 'لم يتم العثور على الملف'
            });
        } catch (error) {
            results.push({
                name: 'Manifest File',
                nameAr: 'ملف Manifest',
                status: 'fail',
                message: 'خطأ في تحميل الملف'
            });
        }

        // Test 3: Service Worker Registration
        if ('serviceWorker' in navigator) {
            try {
                // First check if already registered
                let registration = await navigator.serviceWorker.getRegistration();

                if (!registration) {
                    // Try to register if not found
                    registration = await navigator.serviceWorker.register('/sw.js');
                }

                // Check registration status
                let status = 'fail';
                let message = 'غير مسجل';

                if (registration) {
                    if (registration.active) {
                        status = 'pass';
                        message = 'مسجل ونشط';
                    } else if (registration.installing) {
                        status = 'warning';
                        message = 'قيد التثبيت';
                    } else if (registration.waiting) {
                        status = 'warning';
                        message = 'في الانتظار';
                    } else {
                        status = 'warning';
                        message = 'مسجل لكن غير نشط';
                    }
                }

                results.push({
                    name: 'Service Worker Registration',
                    nameAr: 'تسجيل Service Worker',
                    status: status,
                    message: message
                });
            } catch (error) {
                results.push({
                    name: 'Service Worker Registration',
                    nameAr: 'تسجيل Service Worker',
                    status: 'fail',
                    message: 'خطأ في التسجيل: ' + error.message
                });
            }
        } else {
            results.push({
                name: 'Service Worker Registration',
                nameAr: 'تسجيل Service Worker',
                status: 'fail',
                message: 'غير مدعوم'
            });
        }

        // Test 4: HTTPS
        results.push({
            name: 'HTTPS',
            nameAr: 'بروتوكول HTTPS',
            status: location.protocol === 'https:' || location.hostname === 'localhost' ? 'pass' : 'warning',
            message: location.protocol === 'https:' ? 'آمن' : location.hostname === 'localhost' ? 'محلي (مقبول للاختبار)' : 'مطلوب HTTPS للإنتاج'
        });

        // Test 5: Cache API
        if ('caches' in window) {
            try {
                // Test if we can actually use the cache
                const testCacheName = 'pwa-test-cache';
                const cache = await caches.open(testCacheName);
                await caches.delete(testCacheName); // Clean up

                results.push({
                    name: 'Cache API',
                    nameAr: 'واجهة التخزين المؤقت',
                    status: 'pass',
                    message: 'مدعوم ويعمل'
                });
            } catch (error) {
                results.push({
                    name: 'Cache API',
                    nameAr: 'واجهة التخزين المؤقت',
                    status: 'fail',
                    message: 'مدعوم لكن لا يعمل: ' + error.message
                });
            }
        } else {
            results.push({
                name: 'Cache API',
                nameAr: 'واجهة التخزين المؤقت',
                status: 'fail',
                message: 'غير مدعوم'
            });
        }

        // Test 6: Service Worker Cache Check
        if ('serviceWorker' in navigator && 'caches' in window) {
            try {
                const cacheNames = await caches.keys();
                const appCaches = cacheNames.filter(name => name.includes('appnote'));

                if (appCaches.length > 0) {
                    const cache = await caches.open(appCaches[0]);
                    const cachedRequests = await cache.keys();

                    results.push({
                        name: 'Service Worker Cache',
                        nameAr: 'تخزين Service Worker',
                        status: cachedRequests.length > 0 ? 'pass' : 'warning',
                        message: cachedRequests.length > 0 ? `${cachedRequests.length} ملف محفوظ` : 'لا توجد ملفات محفوظة'
                    });
                } else {
                    results.push({
                        name: 'Service Worker Cache',
                        nameAr: 'تخزين Service Worker',
                        status: 'warning',
                        message: 'لم يتم إنشاء التخزين المؤقت بعد'
                    });
                }
            } catch (error) {
                results.push({
                    name: 'Service Worker Cache',
                    nameAr: 'تخزين Service Worker',
                    status: 'fail',
                    message: 'خطأ في فحص التخزين المؤقت'
                });
            }
        } else {
            results.push({
                name: 'Service Worker Cache',
                nameAr: 'تخزين Service Worker',
                status: 'fail',
                message: 'غير متاح'
            });
        }

        // Test 7: Fetch API
        results.push({
            name: 'Fetch API',
            nameAr: 'واجهة Fetch',
            status: 'fetch' in window ? 'pass' : 'fail',
            message: 'fetch' in window ? 'مدعوم' : 'غير مدعوم'
        });

        // Test 7: Install Prompt
        let installPromptSupported = false;
        window.addEventListener('beforeinstallprompt', (e) => {
            installPromptSupported = true;
        });
        
        setTimeout(() => {
            results.push({
                name: 'Install Prompt',
                nameAr: 'مطالبة التثبيت',
                status: installPromptSupported ? 'pass' : 'warning',
                message: installPromptSupported ? 'متوفر' : 'قد يكون التطبيق مثبت بالفعل'
            });
            
            displayResults(results);
        }, 1000);
    }

    function displayResults(results) {
        const container = document.getElementById('test-results');
        let html = '';
        
        const passCount = results.filter(r => r.status === 'pass').length;
        const totalCount = results.length;
        
        html += `
            <div class="alert alert-info text-center mb-4">
                <h4><i class="fas fa-chart-pie me-2"></i>نتائج الاختبار</h4>
                <p class="mb-0">نجح ${passCount} من ${totalCount} اختبارات</p>
                <div class="progress mt-2">
                    <div class="progress-bar" style="width: ${(passCount/totalCount)*100}%"></div>
                </div>
            </div>
        `;
        
        results.forEach(result => {
            const iconClass = result.status === 'pass' ? 'fa-check-circle text-success' : 
                            result.status === 'warning' ? 'fa-exclamation-triangle text-warning' : 
                            'fa-times-circle text-danger';
            
            html += `
                <div class="d-flex align-items-center justify-content-between p-3 border-bottom">
                    <div>
                        <h6 class="mb-1">${result.nameAr}</h6>
                        <small class="text-muted">${result.name}</small>
                    </div>
                    <div class="text-end">
                        <i class="fas ${iconClass} fa-lg me-2"></i>
                        <span class="small">${result.message}</span>
                    </div>
                </div>
            `;
        });
        
        html += `
            <div class="text-center mt-4">
                <button class="btn btn-primary me-2" onclick="location.reload()">
                    <i class="fas fa-redo me-1"></i>إعادة الاختبار
                </button>
                <button class="btn btn-warning me-2" onclick="clearCacheAndReload()">
                    <i class="fas fa-trash me-1"></i>مسح التخزين المؤقت
                </button>
                <button class="btn btn-info me-2" onclick="forceServiceWorkerUpdate()">
                    <i class="fas fa-sync me-1"></i>تحديث Service Worker
                </button>
                <button class="btn btn-success" onclick="window.location.href='{{ route('dashboard') }}'">
                    <i class="fas fa-home me-1"></i>العودة للوحة التحكم
                </button>
            </div>
        `;
        
        container.innerHTML = html;
    }

    // Clear cache and reload
    async function clearCacheAndReload() {
        try {
            if ('caches' in window) {
                const cacheNames = await caches.keys();
                await Promise.all(cacheNames.map(name => caches.delete(name)));
                console.log('All caches cleared');
            }

            if ('serviceWorker' in navigator) {
                const registrations = await navigator.serviceWorker.getRegistrations();
                await Promise.all(registrations.map(reg => reg.unregister()));
                console.log('All service workers unregistered');
            }

            alert('تم مسح التخزين المؤقت وإلغاء تسجيل Service Workers. سيتم إعادة تحميل الصفحة.');
            location.reload(true);
        } catch (error) {
            console.error('Error clearing cache:', error);
            alert('خطأ في مسح التخزين المؤقت: ' + error.message);
        }
    }

    // Force service worker update
    async function forceServiceWorkerUpdate() {
        try {
            if ('serviceWorker' in navigator) {
                const registration = await navigator.serviceWorker.getRegistration();
                if (registration) {
                    await registration.update();
                    alert('تم طلب تحديث Service Worker. قد تحتاج لإعادة تحميل الصفحة.');
                } else {
                    alert('لا يوجد Service Worker مسجل للتحديث');
                }
            } else {
                alert('Service Worker غير مدعوم في هذا المتصفح');
            }
        } catch (error) {
            console.error('Error updating service worker:', error);
            alert('خطأ في تحديث Service Worker: ' + error.message);
        }
    }
</script>
@endsection
