<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Models\Notification;

echo "=== Latest Notifications with Attachments ===\n\n";

$notifications = Notification::latest()->take(5)->get();

foreach ($notifications as $notification) {
    echo "ID: {$notification->id}\n";
    echo "Title: {$notification->title}\n";
    echo "Created: {$notification->created_at}\n";
    
    // Check attachment_path
    echo "Attachment Path (type): " . gettype($notification->attachment_path) . "\n";
    echo "Attachment Path (value): " . json_encode($notification->attachment_path) . "\n";
    
    // Check attachment_name
    echo "Attachment Name (type): " . gettype($notification->attachment_name) . "\n";
    echo "Attachment Name (value): " . json_encode($notification->attachment_name) . "\n";
    
    // Check if has attachments
    $attachmentPaths = $notification->attachment_path ?? [];
    if (!is_array($attachmentPaths)) {
        $attachmentPaths = [$attachmentPaths];
    }
    $hasAttachment = !empty($attachmentPaths) && !empty(array_filter($attachmentPaths));
    
    echo "Has Attachments: " . ($hasAttachment ? "YES" : "NO") . "\n";
    
    if ($hasAttachment) {
        echo "Attachment Count: " . count($attachmentPaths) . "\n";
        foreach ($attachmentPaths as $index => $path) {
            $name = $notification->attachment_name[$index] ?? "Unknown";
            echo "  {$index}: {$path} -> {$name}\n";
        }
    }
    
    echo "---\n\n";
}

echo "✅ Check completed!\n";
