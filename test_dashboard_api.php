<?php

$baseUrl = 'http://127.0.0.1:8000/api';

echo "=== Testing Employee Dashboard API ===\n";

// Test 0: Web test endpoint
echo "0. Testing web endpoint...\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://127.0.0.1:8000/test-simple');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

echo "Web Test HTTP Code: $httpCode\n";
echo "Web Test Response: $response\n\n";

// Test 1: Direct route test
echo "1. Testing direct route...\n";
curl_setopt($ch, CURLOPT_URL, $baseUrl . '/direct-test');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

echo "Direct Route HTTP Code: $httpCode\n";
echo "Direct Route Response: $response\n\n";

// Test 2: Simple test endpoint
echo "2. Testing simple endpoint...\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl . '/employee-test/simple');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

echo "Simple Test HTTP Code: $httpCode\n";
echo "Simple Test Response: $response\n\n";

// Test 3: Test controller simple
echo "3. Testing test controller simple...\n";
curl_setopt($ch, CURLOPT_URL, $baseUrl . '/test/simple');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

echo "Test Controller HTTP Code: $httpCode\n";
echo "Test Controller Response: $response\n\n";

// Test 4: Test controller dashboard
echo "4. Testing test controller dashboard...\n";
curl_setopt($ch, CURLOPT_URL, $baseUrl . '/test/dashboard');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

echo "Test Dashboard HTTP Code: $httpCode\n";
echo "Test Dashboard Response: $response\n\n";

// Test 5: Web dashboard endpoint
echo "5. Testing web dashboard endpoint...\n";
curl_setopt($ch, CURLOPT_URL, 'http://127.0.0.1:8000/api/employee/dashboard');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

echo "Web Dashboard HTTP Code: $httpCode\n";
echo "Web Dashboard Response: $response\n";

curl_close($ch);

echo "\n=== Test Complete ===\n";
