<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Notification extends Model
{
    use HasFactory;
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'title',
        'message',
        'sender_type',
        'sender_id',
        'sender_name',
        'recipient_type',
        'recipient_ids',
        'attachment_path',
        'attachment_name',
        'priority',
        'status',
        'target_audience',
        'is_active',
        'content',
        'type',
        'scheduled_at',
        'expires_at',
        'attachments',
    ];
    
    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'recipient_ids' => 'array',
        'attachment_path' => 'array',
        'attachment_name' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];
    
    /**
     * Get the recipients for the notification.
     */
    public function recipients()
    {
        return $this->hasMany(NotificationRecipient::class);
    }
}
