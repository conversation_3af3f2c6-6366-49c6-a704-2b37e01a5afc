<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Employee;
use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class EmployeeController extends Controller
{
    /**
     * Display a listing of employees.
     */
    public function index()
    {
        try {
            $employees = Employee::orderBy('created_at', 'desc')->get();
            
            return response()->json([
                'success' => true,
                'data' => $employees,
                'message' => 'Employees retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve employees: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created employee.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'full_name' => 'required|string|max:255',
            'phone' => 'nullable|string|max:50',
            'contract_type' => 'required|string|max:255',
            'employee_type' => 'required|string|max:255',
            'job_status' => 'required|string|max:255',
            'automatic_number' => 'nullable|string|max:100',
            'financial_number' => 'nullable|string|max:100',
            'state_cooperative_number' => 'nullable|string|max:100',
            'bank_account_number' => 'nullable|string|max:100',
            'username' => 'nullable|string|max:50|unique:employees,username',
            'password' => 'nullable|string|min:3',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $employee = Employee::create([
                'full_name' => $request->full_name,
                'phone' => $request->phone,
                'contract_type' => $request->contract_type,
                'employee_type' => $request->employee_type,
                'job_status' => $request->job_status,
                'automatic_number' => $request->automatic_number,
                'financial_number' => $request->financial_number,
                'state_cooperative_number' => $request->state_cooperative_number,
                'bank_account_number' => $request->bank_account_number,
                'username' => $request->username,
                'password' => Hash::make($request->password ?? 'emp123'),
            ]);

            return response()->json([
                'success' => true,
                'data' => $employee,
                'message' => 'Employee created successfully'
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create employee: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified employee.
     */
    public function show(string $id)
    {
        try {
            $employee = Employee::findOrFail($id);
            
            return response()->json([
                'success' => true,
                'data' => $employee,
                'message' => 'Employee retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Employee not found'
            ], 404);
        }
    }

    /**
     * Update the specified employee.
     */
    public function update(Request $request, string $id)
    {
        $validator = Validator::make($request->all(), [
            'full_name' => 'required|string|max:255',
            'phone' => 'nullable|string|max:50',
            'contract_type' => 'required|string|max:255',
            'employee_type' => 'required|string|max:255',
            'job_status' => 'required|string|max:255',
            'automatic_number' => 'nullable|string|max:100',
            'financial_number' => 'nullable|string|max:100',
            'state_cooperative_number' => 'nullable|string|max:100',
            'bank_account_number' => 'nullable|string|max:100',
            'username' => 'nullable|string|max:50|unique:employees,username,' . $id,
            'password' => 'nullable|string|min:3',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $employee = Employee::findOrFail($id);

            $updateData = [
                'full_name' => $request->full_name,
                'phone' => $request->phone,
                'contract_type' => $request->contract_type,
                'employee_type' => $request->employee_type,
                'job_status' => $request->job_status,
                'automatic_number' => $request->automatic_number,
                'financial_number' => $request->financial_number,
                'state_cooperative_number' => $request->state_cooperative_number,
                'bank_account_number' => $request->bank_account_number,
                'username' => $request->username,
            ];

            if ($request->filled('password')) {
                $updateData['password'] = Hash::make($request->password);
            }

            $employee->update($updateData);

            return response()->json([
                'success' => true,
                'data' => $employee,
                'message' => 'Employee updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update employee: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified employee.
     */
    public function destroy(string $id)
    {
        try {
            $employee = Employee::findOrFail($id);
            $employee->delete();

            return response()->json([
                'success' => true,
                'message' => 'Employee deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete employee: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get unique contract types from employees table
     */
    public function getContractTypes()
    {
        try {
            $contractTypes = Employee::select('contract_type')
                ->distinct()
                ->whereNotNull('contract_type')
                ->where('contract_type', '!=', '')
                ->orderBy('contract_type')
                ->pluck('contract_type');

            return response()->json([
                'success' => true,
                'data' => $contractTypes,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching contract types: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get unique employee types from employees table
     */
    public function getEmployeeTypes()
    {
        try {
            $employeeTypes = Employee::select('employee_type')
                ->distinct()
                ->whereNotNull('employee_type')
                ->where('employee_type', '!=', '')
                ->orderBy('employee_type')
                ->pluck('employee_type');

            return response()->json([
                'success' => true,
                'data' => $employeeTypes,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching employee types: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get unique job statuses from employees table
     */
    public function getJobStatuses()
    {
        try {
            $jobStatuses = Employee::select('job_status')
                ->distinct()
                ->whereNotNull('job_status')
                ->where('job_status', '!=', '')
                ->orderBy('job_status')
                ->pluck('job_status');

            return response()->json([
                'success' => true,
                'data' => $jobStatuses,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching job statuses: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete all employees (Admin only)
     */
    public function deleteAll()
    {
        try {
            $count = Employee::count();

            if ($count === 0) {
                return response()->json([
                    'success' => true,
                    'message' => 'No employees to delete',
                    'deleted_count' => 0
                ]);
            }

            Employee::truncate(); // This will delete all records and reset auto-increment

            return response()->json([
                'success' => true,
                'message' => "Successfully deleted all $count employees",
                'deleted_count' => $count
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete all employees: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test authentication
     */
    public function testAuth()
    {
        try {
            $employee = Auth::guard('employee_api')->user();

            if (!$employee) {
                return response()->json([
                    'success' => false,
                    'message' => 'Token is invalid or expired',
                    'authenticated' => false
                ], 401);
            }

            return response()->json([
                'success' => true,
                'message' => 'Authentication successful',
                'authenticated' => true,
                'employee' => [
                    'id' => $employee->id,
                    'username' => $employee->username,
                    'full_name' => $employee->full_name,
                    'employee_type' => $employee->employee_type,
                    'contract_type' => $employee->contract_type,
                    'job_status' => $employee->job_status,
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication error: ' . $e->getMessage(),
                'authenticated' => false
            ], 500);
        }
    }

    /**
     * Simple test endpoint without authentication
     */
    public function testSimple()
    {
        try {
            return response()->json([
                'success' => true,
                'message' => 'Simple test successful',
                'timestamp' => now(),
                'server_status' => 'running'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Test failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get employee dashboard data
     */
    public function getDashboard()
    {
        try {
            Log::info('Employee dashboard request received');

            // Try to get authenticated employee first
            $employee = Auth::guard('employee_api')->user();

            // If no authenticated employee, use test employee for now
            if (!$employee) {
                Log::info('No authenticated employee, using test employee');
                $employee = Employee::where('username', 'test_employee')->first();
            }

            if (!$employee) {
                Log::warning('No employee found');
                return response()->json([
                    'success' => false,
                    'message' => 'Employee not found'
                ], 404);
            }

            Log::info('Using employee: ' . $employee->id . ' - ' . $employee->full_name);

            // Get notifications count for this employee
            $notificationsCount = Notification::where(function($query) use ($employee) {
                $query->where('target_audience', 'جميع الموظفين')
                      ->orWhere('target_audience', 'like', '%موظف%')
                      ->orWhere('target_audience', 'all')  // Handle English 'all'
                      ->orWhere('target_audience', 'employees')  // Handle English 'employees'
                      ->orWhere('target_audience', $employee->employee_type)
                      ->orWhere('target_audience', $employee->contract_type);
            })->where('is_active', true)->count();

            // For now, assume all notifications are unread since we don't have read tracking yet
            $unreadNotificationsCount = $notificationsCount;

            return response()->json([
                'success' => true,
                'data' => [
                    'employee_info' => [
                        'name' => $employee->full_name,
                        'username' => $employee->username,
                        'employee_type' => $employee->employee_type,
                        'contract_type' => $employee->contract_type,
                        'job_status' => $employee->job_status,
                        'phone' => $employee->phone,
                    ],
                    'notifications' => [
                        'total' => $notificationsCount,
                        'unread' => $unreadNotificationsCount,
                    ],
                    'work_info' => [
                        'employee_type' => $employee->employee_type,
                        'contract_type' => $employee->contract_type,
                        'job_status' => $employee->job_status,
                        'automatic_number' => $employee->automatic_number,
                        'financial_number' => $employee->financial_number,
                        'state_cooperative_number' => $employee->state_cooperative_number,
                        'bank_account_number' => $employee->bank_account_number,
                    ],
                    'statistics' => [
                        'total_notifications' => $notificationsCount,
                        'unread_notifications' => $unreadNotificationsCount,
                        'read_notifications' => 0, // Will be implemented later
                    ]
                ],
                'message' => 'Dashboard data retrieved successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to retrieve employee dashboard data: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve dashboard data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get employee notifications
     */
    public function getNotifications(Request $request)
    {
        try {
            // Get the authenticated employee from JWT
            $employee = Auth::guard('employee_api')->user();

            if (!$employee) {
                return response()->json([
                    'success' => false,
                    'message' => 'Token is invalid or expired'
                ], 401);
            }

            // Get notifications for this specific employee using NotificationRecipient table
            $query = \App\Models\NotificationRecipient::where('recipient_id', $employee->id)
                ->where('recipient_type', 'employee')
                ->with('notification');

            // Filter by status if provided
            if ($request->has('status')) {
                if ($request->status === 'read') {
                    $query->whereNotNull('read_at');
                } elseif ($request->status === 'unread') {
                    $query->whereNull('read_at');
                }
            }

            // Get paginated recipients
            $recipients = $query->orderBy('created_at', 'desc')
                               ->paginate($request->get('per_page', 20));

            // Transform recipients to notification format
            $notifications = $recipients->getCollection()->map(function ($recipient) {
                $notification = $recipient->notification;
                if ($notification) {
                    $notification->is_read = !is_null($recipient->read_at);
                    $notification->status = is_null($recipient->read_at) ? 'unread' : 'read';
                    $notification->read_at = $recipient->read_at;
                    return $notification;
                }
                return null;
            })->filter(); // Remove null values

            return response()->json([
                'success' => true,
                'data' => $notifications->values(), // Use values() to reset array keys
                'pagination' => [
                    'current_page' => $recipients->currentPage(),
                    'last_page' => $recipients->lastPage(),
                    'per_page' => $recipients->perPage(),
                    'total' => $recipients->total(),
                ],
                'message' => 'Notifications retrieved successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to retrieve employee notifications: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve notifications: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mark notification as read
     */
    public function markNotificationAsRead($notificationId)
    {
        try {
            // Get the authenticated employee from JWT
            $employee = Auth::guard('employee_api')->user();

            if (!$employee) {
                return response()->json([
                    'success' => false,
                    'message' => 'Token is invalid or expired'
                ], 401);
            }

            $notification = Notification::find($notificationId);

            if (!$notification) {
                return response()->json([
                    'success' => false,
                    'message' => 'Notification not found'
                ], 404);
            }

            // Check if notification is relevant to this employee
            $isRelevant = $notification->target_audience === 'جميع الموظفين' ||
                         str_contains($notification->target_audience, 'موظف') ||
                         $notification->target_audience === 'all' ||
                         $notification->target_audience === 'employees' ||
                         $notification->target_audience === $employee->employee_type ||
                         $notification->target_audience === $employee->contract_type;

            if (!$isRelevant) {
                return response()->json([
                    'success' => false,
                    'message' => 'Notification not relevant to this employee'
                ], 403);
            }

            // TODO: Implement proper read tracking with notification_reads table
            // For now, we'll just return success without actually tracking reads

            return response()->json([
                'success' => true,
                'message' => 'Notification marked as read (tracking not implemented yet)'
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to mark notification as read: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to mark notification as read: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mark notification as unread
     */
    public function markNotificationAsUnread($notificationId)
    {
        try {
            // Get the authenticated employee from JWT
            $employee = Auth::guard('employee_api')->user();

            if (!$employee) {
                return response()->json([
                    'success' => false,
                'message' => 'Token is invalid or expired'
                ], 401);
            }

            $notification = Notification::find($notificationId);

            if (!$notification) {
                return response()->json([
                    'success' => false,
                    'message' => 'Notification not found'
                ], 404);
            }

            // Check if notification is relevant to this employee
            $isRelevant = $notification->target_audience === 'جميع الموظفين' ||
                         str_contains($notification->target_audience, 'موظف') ||
                         $notification->target_audience === 'all' ||
                         $notification->target_audience === 'employees' ||
                         $notification->target_audience === $employee->employee_type ||
                         $notification->target_audience === $employee->contract_type;

            if (!$isRelevant) {
                return response()->json([
                    'success' => false,
                    'message' => 'Notification not relevant to this employee'
                ], 403);
            }

            // TODO: Implement proper read tracking with notification_reads table
            // For now, we'll just return success without actually tracking reads

            return response()->json([
                'success' => true,
                'message' => 'Notification marked as unread (tracking not implemented yet)'
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to mark notification as unread: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to mark notification as unread: ' . $e->getMessage()
            ], 500);
        }
    }
}
