@extends('layouts.app')

@section('styles')
<style>
    .notification-card {
        border: none;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        transition: box-shadow 0.3s ease;
    }
    
    .notification-card:hover {
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .notification-item {
        border-left: 3px solid transparent;
        transition: all 0.2s ease;
        padding: 16px 20px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .notification-item:last-child {
        border-bottom: none;
    }
    
    .notification-item:hover {
        background-color: rgba(242, 242, 242, 0.5);
    }
    
    .notification-item.unread {
        border-left-color: var(--costa-del-sol);
        background-color: rgba(230, 235, 214, 0.2);
    }
    
    .notification-badge {
        border-radius: 30px;
        padding: 5px 12px;
        font-size: 0.8rem;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
    }
    
    .badge-unread {
        background-color: rgba(255, 193, 7, 0.15);
        color: #856404;
        border: 1px solid rgba(255, 193, 7, 0.3);
    }
    
    .badge-read {
        background-color: rgba(108, 117, 125, 0.15);
        color: #6c757d;
        border: 1px solid rgba(108, 117, 125, 0.3);
    }
    
    .attachment-container {
        background-color: rgba(230, 235, 214, 0.3);
        border: 1px solid rgba(93, 110, 53, 0.2);
        border-radius: 8px;
    }
    
    .attachment-badge {
        display: inline-flex;
        align-items: center;
        background-color: var(--locust);
        color: white;
        border-radius: 30px;
        padding: 6px 15px;
        margin-right: 8px;
        margin-bottom: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: all 0.2s ease;
        text-decoration: none;
    }
    
    .attachment-badge:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }
    
    .btn-mark-read {
        background-color: var(--costa-del-sol);
        color: white;
        border: none;
        border-radius: 30px;
        padding: 6px 16px;
        font-size: 0.875rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: all 0.2s ease;
    }
    
    .btn-mark-read:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }
    
    .filter-card {
        background-color: #FAFAFA;
        border: none;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }
    
    .filter-header {
        background-color: rgba(230, 235, 214, 0.3);
        color: var(--costa-del-sol);
        padding: 12px 16px;
        font-weight: 600;
    }

    .highlighted-notification {
        animation: highlight-pulse 2s ease-in-out infinite;
    }
    
    @keyframes highlight-pulse {
        0% { box-shadow: 0 0 5px rgba(93, 110, 53, 0.3); }
        50% { box-shadow: 0 0 15px rgba(93, 110, 53, 0.5); }
        100% { box-shadow: 0 0 5px rgba(93, 110, 53, 0.3); }
    }
</style>
@endsection

@section('content')
<div class="container-fluid main-content px-4">
    <div class="row">
        <!-- Main Content Column -->
        <div class="col-md-9">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4 mt-2">
                <div>
                    <h2 class="mb-1" style="color: var(--costa-del-sol); font-weight: 700;">My Notifications</h2>
                    <p class="mb-0" style="color: var(--gurkha);">View and manage your notifications</p>
                </div>
                <div>
                    <a href="{{ route('student.dashboard') }}" class="btn btn-sm" style="background-color: var(--tana); color: var(--costa-del-sol);">
                        <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
                    </a>
                </div>
            </div>

            <!-- Notifications Card -->
            <div class="card notification-card mb-4">
                <div class="card-header" style="background-color: var(--thistle-green); color: var(--costa-del-sol);">
                    <i class="fas fa-bell me-2"></i> Notifications
                </div>
                <div class="card-body p-0">
                    @if(count($notifications) > 0)
                        <div class="list-group list-group-flush">
                            @foreach($notifications as $recipient)
                                @php
                                    $notification = $recipient->notification;
                                    $attachments = [];
                                    if ($notification->attachments) {
                                        try {
                                            $attachments = json_decode($notification->attachments, true);
                                        } catch (\Exception $e) {
                                            $attachments = [];
                                        }
                                    }
                                    $isUnread = is_null($recipient->read_at);
                                    $isHighlighted = isset($highlightedNotificationId) && $notification->id == $highlightedNotificationId;
                                @endphp
                                
                                <!-- Notification Item -->
                                <div id="notification-{{ $notification->id }}" class="notification-item {{ $isUnread ? 'unread' : '' }} {{ $isHighlighted ? 'highlighted-notification' : '' }}">
                                    <!-- Header: Title and Date -->
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h5 class="mb-1" style="font-weight: 600; color: var(--costa-del-sol);">{{ $notification->title }}</h5>
                                        <small class="text-muted bg-light px-2 py-1 rounded-pill">{{ $notification->created_at->diffForHumans() }}</small>
                                    </div>
                                    
                                    <!-- Content -->
                                    <p class="mb-3" style="color: var(--gray); line-height: 1.5;">{{ $notification->content }}</p>
                                    
                                    <!-- Attachments Section -->
                                    @if(count($attachments) > 0)
                                        <div class="attachment-container p-3 my-3">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="fas fa-paperclip me-2" style="color: var(--costa-del-sol);"></i>
                                                <h6 class="m-0" style="color: var(--costa-del-sol); font-weight: 600;">Attachments</h6>
                                            </div>
                                            <div class="d-flex flex-wrap">
                                                @foreach($attachments as $attachment)
                                                    @if(isset($attachment['path']) && isset($attachment['name']))
                                                        <a href="{{ asset('storage/' . $attachment['path']) }}" 
                                                           class="attachment-badge"
                                                           target="_blank" 
                                                           title="Download {{ $attachment['name'] }}">
                                                            <i class="fas fa-file-download me-2"></i>
                                                            <span style="max-width: 150px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">{{ $attachment['name'] }}</span>
                                                        </a>
                                                    @endif
                                                @endforeach
                                            </div>
                                        </div>
                                    @endif
                                    
                                    <!-- Footer: Status and Actions -->
                                    <hr class="my-3" style="opacity: 0.1;">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            @if($isUnread)
                                                <span class="notification-badge badge-unread">
                                                    <i class="fas fa-circle me-1" style="font-size: 8px;"></i> Unread
                                                </span>
                                            @else
                                                <span class="notification-badge badge-read">
                                                    <i class="fas fa-check-circle me-1"></i> Read {{ $recipient->read_at->diffForHumans() }}
                                                </span>
                                            @endif
                                        </div>
                                        <div>
                                            @if($isUnread)
                                                <form action="{{ route('student.notifications.mark-read', $notification->id) }}" method="POST" class="d-inline">
                                                    @csrf
                                                    <button type="submit" class="btn btn-mark-read">
                                                        <i class="fas fa-check-circle me-1"></i> Mark as Read
                                                    </button>
                                                </form>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                        
                        <!-- Pagination -->
                        <div class="d-flex justify-content-center p-3">
                            {{ $notifications->links() }}
                        </div>
                    @else
                        <div class="alert alert-info m-3">No notifications found</div>
                    @endif
                </div>
            </div>
        </div>
        
        <!-- Sidebar Column -->
        <div class="col-md-3">
            <!-- Filter Card -->
            <div class="card filter-card mb-4">
                <div class="card-header filter-header">
                    <i class="fas fa-filter me-2"></i> Filter
                </div>
                <div class="card-body">
                    <form action="{{ route('student.notifications') }}" method="GET">
                        <div class="mb-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">All</option>
                                <option value="read" {{ request('status') == 'read' ? 'selected' : '' }}>Read</option>
                                <option value="unread" {{ request('status') == 'unread' ? 'selected' : '' }}>Unread</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="date" class="form-label">Date Range</label>
                            <select class="form-select" id="date" name="date">
                                <option value="">All Time</option>
                                <option value="today" {{ request('date') == 'today' ? 'selected' : '' }}>Today</option>
                                <option value="week" {{ request('date') == 'week' ? 'selected' : '' }}>This Week</option>
                                <option value="month" {{ request('date') == 'month' ? 'selected' : '' }}>This Month</option>
                            </select>
                        </div>
                        <button type="submit" class="btn w-100 btn-mark-read">
                            <i class="fas fa-search me-1"></i> Filter
                        </button>
                    </form>
                </div>
            </div>
            
            <!-- Quick Links Card -->
            <div class="card filter-card mb-4">
                <div class="card-header filter-header">
                    <i class="fas fa-link me-2"></i> Quick Links
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush border-0">
                        <a href="{{ route('student.dashboard') }}" class="list-group-item list-group-item-action d-flex align-items-center" style="color: var(--costa-del-sol);">
                            <i class="fas fa-tachometer-alt me-3"></i> Dashboard
                        </a>
                        <a href="#" class="list-group-item list-group-item-action d-flex align-items-center" style="color: var(--costa-del-sol);">
                            <i class="fas fa-user-cog me-3"></i> Update Profile
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@if(isset($highlightedNotificationId))
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const highlightedElement = document.getElementById('notification-{{ $highlightedNotificationId }}');
        if (highlightedElement) {
            setTimeout(function() {
                highlightedElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }, 300);
        }
    });
</script>
@endif
@endsection
