@extends('layouts.app')

@section('content')
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4 mt-3">
        <div>
            <h2 class="mb-1" style="color: var(--costa-del-sol); font-weight: 700;">Database Backups</h2>
            <p class="mb-0" style="color: var(--gurkha);">Manage your database backups</p>
        </div>
        <div>
            <a href="{{ route('dashboard') }}" class="btn btn-sm btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
            </a>
        </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <div class="row mb-4">
        <div class="col-12">
            <div class="card modern-card">
                <div class="card-header bg-white">
                    <i class="fas fa-info-circle me-2"></i> Backup Information
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <p class="mb-2">
                                <i class="fas fa-database me-2" style="color: var(--costa-del-sol);"></i> 
                                Database backups help you recover your data in case of system failure or data loss.
                            </p>
                            <p class="mb-0">
                                <i class="fas fa-shield-alt me-2" style="color: var(--costa-del-sol);"></i>
                                It is recommended to create regular backups and store them in multiple locations.
                            </p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <form action="{{ route('backups.create') }}" method="POST">
                                @csrf
                                <button type="submit" class="btn" style="background-color: var(--costa-del-sol); color: white;">
                                    <i class="fas fa-plus me-1"></i> Create New Backup
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card modern-card">
        <div class="card-header bg-white d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-database me-2"></i> Backup Files
            </div>
            <div>
                <a href="{{ route('backups.index') }}" class="btn btn-sm btn-outline-secondary">
                    <i class="fas fa-sync-alt me-1"></i> Refresh
                </a>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th class="ps-3">Backup File</th>
                            <th>Size</th>
                            <th>Created</th>
                            <th class="text-end pe-3">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($backupFiles as $backup)
                        <tr>
                            <td class="ps-3">
                                <i class="fas fa-database me-2" style="color: var(--costa-del-sol);"></i> {{ $backup['name'] }}
                            </td>
                            <td>{{ $backup['size'] }}</td>
                            <td>{{ $backup['modified'] }}</td>
                            <td class="text-end pe-3">
                                <a href="{{ route('backups.download', $backup['name']) }}" class="btn btn-sm btn-info me-1" title="Download">
                                    <i class="fas fa-download"></i>
                                </a>
                                <form action="{{ route('backups.destroy', $backup['name']) }}" method="POST" class="d-inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-sm btn-danger" title="Delete" 
                                        onclick="return confirm('Are you sure you want to delete this backup? This action cannot be undone.')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="4" class="text-center py-4">
                                <div class="empty-state">
                                    <i class="fas fa-database fa-2x mb-3 text-muted"></i>
                                    <p>No backup files found</p>
                                    <p class="text-muted">Create your first database backup by clicking the 'Create New Backup' button above.</p>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@endsection

@section('styles')
<style>
.modern-card {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    border: none;
    transition: all 0.3s ease;
}

.empty-state {
    padding: 2rem;
    text-align: center;
}
</style>
@endsection
