<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Service Worker Test - معهد النبطية الفني</title>
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            margin: 20px;
            background: #f8f9fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-item {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .success { background: #d4edda; border-color: #28a745; }
        .error { background: #f8d7da; border-color: #dc3545; }
        .warning { background: #fff3cd; border-color: #ffc107; }
        .info { background: #d1ecf1; border-color: #17a2b8; }
        .status { font-weight: bold; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .log { max-height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Service Worker Test - اختبار Service Worker</h1>
        
        <div id="browser-support" class="test-item">
            <h3>🌐 Browser Support - دعم المتصفح</h3>
            <div id="browser-support-result">Testing...</div>
        </div>

        <div id="sw-registration" class="test-item">
            <h3>📝 Service Worker Registration - تسجيل Service Worker</h3>
            <div id="sw-registration-result">Testing...</div>
            <button onclick="registerServiceWorker()">Register SW</button>
            <button onclick="registerSimpleServiceWorker()">Register Simple SW</button>
            <button onclick="unregisterServiceWorker()">Unregister SW</button>
        </div>

        <div id="cache-api" class="test-item">
            <h3>💾 Cache API - واجهة التخزين المؤقت</h3>
            <div id="cache-api-result">Testing...</div>
            <button onclick="testCacheAPI()">Test Cache</button>
        </div>

        <div id="push-api" class="test-item">
            <h3>📱 Push API - واجهة الإشعارات</h3>
            <div id="push-api-result">Testing...</div>
            <button onclick="testPushAPI()">Test Push</button>
        </div>

        <div id="notification-api" class="test-item">
            <h3>🔔 Notification API - واجهة الإشعارات</h3>
            <div id="notification-api-result">Testing...</div>
            <button onclick="testNotificationAPI()">Test Notifications</button>
        </div>

        <div id="console-log" class="test-item">
            <h3>📋 Console Log - سجل وحدة التحكم</h3>
            <div id="console-log-result" class="log">
                <pre id="log-content"></pre>
            </div>
            <button onclick="clearLog()">Clear Log</button>
        </div>
    </div>

    <script>
        // Enhanced logging
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        let logContent = '';
        
        function addToLog(type, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            
            logContent += `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            document.getElementById('log-content').textContent = logContent;
            document.getElementById('log-content').scrollTop = document.getElementById('log-content').scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToLog('log', ...args);
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToLog('error', ...args);
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToLog('warn', ...args);
        };

        function clearLog() {
            logContent = '';
            document.getElementById('log-content').textContent = '';
        }

        function updateResult(elementId, success, message) {
            const element = document.getElementById(elementId);
            element.className = `test-item ${success ? 'success' : 'error'}`;
            element.querySelector('div').innerHTML = `
                <span class="status">${success ? '✅' : '❌'}</span> ${message}
            `;
        }

        function updateWarning(elementId, message) {
            const element = document.getElementById(elementId);
            element.className = 'test-item warning';
            element.querySelector('div').innerHTML = `
                <span class="status">⚠️</span> ${message}
            `;
        }

        // Test browser support
        function testBrowserSupport() {
            console.log('🔍 Testing browser support...');
            
            const features = {
                'Service Worker': 'serviceWorker' in navigator,
                'Cache API': 'caches' in window,
                'Push API': 'PushManager' in window,
                'Notification API': 'Notification' in window,
                'Fetch API': 'fetch' in window
            };
            
            const results = Object.entries(features).map(([name, supported]) => 
                `${supported ? '✅' : '❌'} ${name}: ${supported ? 'Supported' : 'Not Supported'}`
            ).join('<br>');
            
            const allSupported = Object.values(features).every(Boolean);
            
            updateResult('browser-support-result', allSupported, results);
            console.log('Browser support results:', features);
        }

        // Register Service Worker
        async function registerServiceWorker() {
            console.log('🔄 Attempting to register Service Worker...');

            if (!('serviceWorker' in navigator)) {
                updateResult('sw-registration-result', false, 'Service Worker not supported');
                return;
            }

            try {
                const registration = await navigator.serviceWorker.register('/sw.js', {
                    scope: '/'
                });

                console.log('✅ Service Worker registered:', registration);
                console.log('📍 Scope:', registration.scope);
                console.log('📍 State:', registration.installing ? 'installing' : registration.waiting ? 'waiting' : registration.active ? 'active' : 'unknown');

                updateResult('sw-registration-result', true,
                    `Registered successfully<br>Scope: ${registration.scope}<br>State: ${registration.installing ? 'installing' : registration.waiting ? 'waiting' : registration.active ? 'active' : 'unknown'}`
                );

                // Wait for service worker to be ready
                const swReady = await navigator.serviceWorker.ready;
                console.log('✅ Service Worker ready:', swReady);

            } catch (error) {
                console.error('❌ Service Worker registration failed:', error);
                updateResult('sw-registration-result', false, `Registration failed: ${error.message}`);
            }
        }

        // Register Simple Service Worker
        async function registerSimpleServiceWorker() {
            console.log('🔄 Attempting to register Simple Service Worker...');

            if (!('serviceWorker' in navigator)) {
                updateResult('sw-registration-result', false, 'Service Worker not supported');
                return;
            }

            try {
                const registration = await navigator.serviceWorker.register('/sw-simple.js', {
                    scope: '/'
                });

                console.log('✅ Simple Service Worker registered:', registration);
                console.log('📍 Scope:', registration.scope);
                console.log('📍 State:', registration.installing ? 'installing' : registration.waiting ? 'waiting' : registration.active ? 'active' : 'unknown');

                updateResult('sw-registration-result', true,
                    `Simple SW registered successfully<br>Scope: ${registration.scope}<br>State: ${registration.installing ? 'installing' : registration.waiting ? 'waiting' : registration.active ? 'active' : 'unknown'}`
                );

                // Wait for service worker to be ready
                const swReady = await navigator.serviceWorker.ready;
                console.log('✅ Simple Service Worker ready:', swReady);

            } catch (error) {
                console.error('❌ Simple Service Worker registration failed:', error);
                updateResult('sw-registration-result', false, `Simple SW registration failed: ${error.message}`);
            }
        }

        // Unregister Service Worker
        async function unregisterServiceWorker() {
            console.log('🗑️ Unregistering Service Worker...');
            
            try {
                const registrations = await navigator.serviceWorker.getRegistrations();
                for (let registration of registrations) {
                    await registration.unregister();
                    console.log('✅ Service Worker unregistered');
                }
                updateResult('sw-registration-result', true, 'Service Worker unregistered');
            } catch (error) {
                console.error('❌ Failed to unregister Service Worker:', error);
                updateResult('sw-registration-result', false, `Unregistration failed: ${error.message}`);
            }
        }

        // Test Cache API
        async function testCacheAPI() {
            console.log('💾 Testing Cache API...');
            
            if (!('caches' in window)) {
                updateResult('cache-api-result', false, 'Cache API not supported');
                return;
            }

            try {
                const cacheName = 'test-cache-v1';
                const cache = await caches.open(cacheName);
                console.log('✅ Cache opened:', cache);
                
                // Test adding to cache
                await cache.add('/manifest.json');
                console.log('✅ Added file to cache');
                
                // Test retrieving from cache
                const cachedResponse = await cache.match('/manifest.json');
                console.log('✅ Retrieved from cache:', cachedResponse);
                
                // Clean up
                await caches.delete(cacheName);
                console.log('✅ Cache cleaned up');
                
                updateResult('cache-api-result', true, 'Cache API working correctly');
                
            } catch (error) {
                console.error('❌ Cache API test failed:', error);
                updateResult('cache-api-result', false, `Cache API failed: ${error.message}`);
            }
        }

        // Test Push API
        async function testPushAPI() {
            console.log('📱 Testing Push API...');
            
            if (!('PushManager' in window)) {
                updateResult('push-api-result', false, 'Push API not supported');
                return;
            }

            if (!('serviceWorker' in navigator)) {
                updateResult('push-api-result', false, 'Service Worker required for Push API');
                return;
            }

            try {
                const registration = await navigator.serviceWorker.ready;
                console.log('✅ Service Worker ready for push');
                
                // Check if push is supported
                const pushSupported = 'PushManager' in window && 'serviceWorker' in navigator;
                console.log('📱 Push supported:', pushSupported);
                
                updateResult('push-api-result', pushSupported, 
                    pushSupported ? 'Push API supported (requires HTTPS for full functionality)' : 'Push API not supported'
                );
                
            } catch (error) {
                console.error('❌ Push API test failed:', error);
                updateResult('push-api-result', false, `Push API failed: ${error.message}`);
            }
        }

        // Test Notification API
        async function testNotificationAPI() {
            console.log('🔔 Testing Notification API...');
            
            if (!('Notification' in window)) {
                updateResult('notification-api-result', false, 'Notification API not supported');
                return;
            }

            try {
                console.log('🔔 Current permission:', Notification.permission);
                
                if (Notification.permission === 'default') {
                    console.log('🔔 Requesting notification permission...');
                    const permission = await Notification.requestPermission();
                    console.log('🔔 Permission result:', permission);
                }
                
                if (Notification.permission === 'granted') {
                    // Test showing notification
                    const notification = new Notification('Test Notification', {
                        body: 'This is a test notification from AppNote PWA',
                        icon: '/icons/icon-192x192.png',
                        badge: '/icons/icon-72x72.png'
                    });
                    
                    setTimeout(() => notification.close(), 3000);
                    
                    updateResult('notification-api-result', true, 'Notification API working correctly');
                } else {
                    updateWarning('notification-api-result', `Notification permission: ${Notification.permission}`);
                }
                
            } catch (error) {
                console.error('❌ Notification API test failed:', error);
                updateResult('notification-api-result', false, `Notification API failed: ${error.message}`);
            }
        }

        // Run tests on page load
        window.addEventListener('load', () => {
            console.log('🚀 Starting PWA tests...');
            testBrowserSupport();
            
            // Auto-register service worker
            setTimeout(registerServiceWorker, 1000);
        });
    </script>
</body>
</html>
