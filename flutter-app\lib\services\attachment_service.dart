import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../utils/constants.dart';

class AttachmentService {
  static const String _baseStorageUrl = '${ApiConstants.baseUrl}/../storage';

  /// Open attachment file
  static Future<void> openAttachment(
    BuildContext context,
    Map<String, dynamic> attachment,
  ) async {
    try {
      final String fileName = attachment['name'] ?? 'ملف مرفق';
      final String filePath = attachment['path'] ?? '';

      if (filePath.isEmpty) {
        _showError(context, 'مسار الملف غير صحيح');
        return;
      }

      // Show loading
      _showLoading(context, 'جاري فتح الملف...');

      if (kIsWeb) {
        // For web, open file in new tab
        await _openFileInWeb(context, filePath, fileName);
      } else {
        // For mobile/desktop, download and open
        await _downloadAndOpenFile(context, filePath, fileName);
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error opening attachment: $e');
      }
      _showError(context, 'فشل في فتح الملف: $e');
    }
  }

  /// Open file in web browser (for web platform)
  static Future<void> _openFileInWeb(
    BuildContext context,
    String filePath,
    String fileName,
  ) async {
    try {
      // Remove 'public/' prefix if present
      String cleanPath = filePath;
      if (cleanPath.startsWith('public/')) {
        cleanPath = cleanPath.substring(7);
      }

      final String fileUrl = '$_baseStorageUrl/$cleanPath';

      if (kDebugMode) {
        print('🌐 Opening file in web: $fileUrl');
      }

      final Uri uri = Uri.parse(fileUrl);
      
      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
        Navigator.of(context).pop(); // Close loading dialog
        _showSuccess(context, 'تم فتح الملف في نافذة جديدة');
      } else {
        Navigator.of(context).pop(); // Close loading dialog
        _showError(context, 'لا يمكن فتح الملف');
      }
    } catch (e) {
      Navigator.of(context).pop(); // Close loading dialog
      _showError(context, 'فشل في فتح الملف: $e');
    }
  }

  /// Download and open file (for mobile/desktop)
  static Future<void> _downloadAndOpenFile(
    BuildContext context,
    String filePath,
    String fileName,
  ) async {
    try {
      // Remove 'public/' prefix if present
      String cleanPath = filePath;
      if (cleanPath.startsWith('public/')) {
        cleanPath = cleanPath.substring(7);
      }

      final String fileUrl = '$_baseStorageUrl/$cleanPath';

      if (kDebugMode) {
        print('📱 Downloading file: $fileUrl');
      }

      // Download file
      final response = await http.get(Uri.parse(fileUrl));

      if (response.statusCode == 200) {
        // Get app documents directory
        final directory = await getApplicationDocumentsDirectory();
        final file = File('${directory.path}/$fileName');

        // Write file
        await file.writeAsBytes(response.bodyBytes);

        Navigator.of(context).pop(); // Close loading dialog

        if (kDebugMode) {
          print('✅ File downloaded to: ${file.path}');
        }

        // Try to open the file
        final Uri fileUri = Uri.file(file.path);
        
        if (await canLaunchUrl(fileUri)) {
          await launchUrl(fileUri);
          _showSuccess(context, 'تم تحميل وفتح الملف بنجاح');
        } else {
          // If can't open directly, show file location
          _showFileDownloaded(context, file.path, fileName);
        }
      } else {
        Navigator.of(context).pop(); // Close loading dialog
        _showError(context, 'فشل في تحميل الملف (${response.statusCode})');
      }
    } catch (e) {
      Navigator.of(context).pop(); // Close loading dialog
      _showError(context, 'فشل في تحميل الملف: $e');
    }
  }

  /// Show loading dialog
  static void _showLoading(BuildContext context, String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: 16),
            Expanded(child: Text(message)),
          ],
        ),
      ),
    );
  }

  /// Show success message
  static void _showSuccess(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// Show error message
  static void _showError(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 4),
      ),
    );
  }

  /// Show file downloaded dialog
  static void _showFileDownloaded(
    BuildContext context,
    String filePath,
    String fileName,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تم تحميل الملف'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('تم تحميل الملف بنجاح:'),
            const SizedBox(height: 8),
            Text(
              fileName,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              'المسار: $filePath',
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  /// Get file icon based on file extension
  static IconData getFileIcon(String fileName) {
    final extension = fileName.toLowerCase().split('.').last;
    
    switch (extension) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart;
      case 'ppt':
      case 'pptx':
        return Icons.slideshow;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return Icons.image;
      case 'mp4':
      case 'avi':
      case 'mov':
        return Icons.video_file;
      case 'mp3':
      case 'wav':
        return Icons.audio_file;
      case 'zip':
      case 'rar':
        return Icons.archive;
      case 'txt':
        return Icons.text_snippet;
      default:
        return Icons.attach_file;
    }
  }

  /// Get file type display name
  static String getFileTypeDisplay(String fileName) {
    final extension = fileName.toLowerCase().split('.').last;
    
    switch (extension) {
      case 'pdf':
        return 'PDF';
      case 'doc':
      case 'docx':
        return 'Word';
      case 'xls':
      case 'xlsx':
        return 'Excel';
      case 'ppt':
      case 'pptx':
        return 'PowerPoint';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return 'صورة';
      case 'mp4':
      case 'avi':
      case 'mov':
        return 'فيديو';
      case 'mp3':
      case 'wav':
        return 'صوت';
      case 'zip':
      case 'rar':
        return 'أرشيف';
      case 'txt':
        return 'نص';
      default:
        return extension.toUpperCase();
    }
  }
}
