<?php

require_once 'vendor/autoload.php';

use App\Http\Controllers\EmployeeController;
use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;

// Test the employee import functionality
echo "Testing Employee Import Functionality\n";
echo "=====================================\n";

// Create a test CSV file
$csvContent = "full_name,contract_type,employee_type,phone,job_status,automatic_number,financial_number,state_cooperative_number,bank_account_number,username,password\n";
$csvContent .= "Test Employee,Full-time,Teacher,***********,Active,AUTO001,FIN001,STATE001,BANK001,test.employee,emp123\n";

$tempFile = tempnam(sys_get_temp_dir(), 'employee_test');
file_put_contents($tempFile, $csvContent);

echo "Created test CSV file: $tempFile\n";
echo "CSV Content:\n$csvContent\n";

// Test the processEmployeeRows method
$controller = new EmployeeController();

// Simulate CSV data
$rows = [
    ['Test Employee', 'Full-time', 'Teacher', '***********', 'Active', 'AUTO001', 'FIN001', 'STATE001', 'BANK001', 'test.employee', 'emp123']
];

echo "Testing processEmployeeRows method...\n";

try {
    $reflection = new ReflectionClass($controller);
    $method = $reflection->getMethod('processEmployeeRows');
    $method->setAccessible(true);
    
    list($successCount, $errorCount) = $method->invoke($controller, $rows);
    
    echo "Success Count: $successCount\n";
    echo "Error Count: $errorCount\n";
    
    if ($successCount > 0) {
        echo "✅ Employee import test PASSED!\n";
    } else {
        echo "❌ Employee import test FAILED!\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error during test: " . $e->getMessage() . "\n";
}

// Clean up
unlink($tempFile);
echo "Test completed.\n";
