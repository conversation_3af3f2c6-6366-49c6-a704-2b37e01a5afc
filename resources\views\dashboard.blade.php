@extends('layouts.app')

@section('content')
<div class="container-fluid main-content px-4">
    <div class="row">
        <div class="col-md-9">
            <div class="d-flex justify-content-between align-items-center mb-4 mt-2">
                <div>
                    <h2 class="mb-1" style="color: var(--costa-del-sol); font-weight: 700;">Dashboard Overview</h2>
                    <p class="mb-0" style="color: var(--gurkha);">Monitor your API performance and student data</p>
                </div>
                <div class="d-flex align-items-center gap-3">
                    <span class="badge bg-success p-2">System Online</span>
                    <form method="POST" action="{{ route('logout') }}" class="d-inline">
                        @csrf
                        <button type="submit" class="btn btn-outline-danger btn-sm">
                            <i class="fas fa-sign-out-alt me-1"></i>
                            Logout
                        </button>
                    </form>
                </div>
            </div>

            <div class="row g-4 mb-4">
                <div class="col-md-4">
                    <div class="stats-card modern-card">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="text-muted mb-2">Total Students</h6>
                                <h2 class="mb-0" style="color: var(--costa-del-sol); font-weight: 700;">{{ $stats['students']['total'] }}</h2>
                                <div class="mt-2">
                                    <span class="badge bg-light text-dark">All Time</span>
                                </div>
                            </div>
                            <div class="stats-icon-container">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>
                        <div class="progress mt-3" style="height: 5px;">
                            <div class="progress-bar" role="progressbar" style="width: 100%; background-color: var(--costa-del-sol);" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-card modern-card">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="text-muted mb-2">Active Students</h6>
                                <h2 class="mb-0" style="color: var(--costa-del-sol); font-weight: 700;">{{ $stats['students']['active'] }}</h2>
                                <div class="mt-2">
                                    <span class="badge bg-success">Online</span>
                                </div>
                            </div>
                            <div class="stats-icon-container">
                                <i class="fas fa-user-check"></i>
                            </div>
                        </div>
                        <div class="progress mt-3" style="height: 5px;">
                            <div class="progress-bar" role="progressbar" style="width: {{ ($stats['students']['active'] / max(1, $stats['students']['total'])) * 100 }}%; background-color: var(--costa-del-sol);" aria-valuenow="{{ ($stats['students']['active'] / max(1, $stats['students']['total'])) * 100 }}" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-card modern-card">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="text-muted mb-2">API Requests</h6>
                                <h2 class="mb-0" style="color: var(--costa-del-sol); font-weight: 700;">{{ $stats['api_requests'] }}</h2>
                                <div class="mt-2">
                                    <span class="badge bg-info">Last 24h</span>
                                </div>
                            </div>
                            <div class="stats-icon-container">
                                <i class="fas fa-exchange-alt"></i>
                            </div>
                        </div>
                        <div class="progress mt-3" style="height: 5px;">
                            <div class="progress-bar" role="progressbar" style="width: 75%; background-color: var(--costa-del-sol);" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Employee Statistics Cards -->
            <div class="row g-4 mb-4">
                <div class="col-md-4">
                    <div class="stats-card modern-card">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="text-muted mb-2">Total Employees</h6>
                                <h2 class="mb-0" style="color: var(--costa-del-sol); font-weight: 700;">{{ $stats['employees']['total'] }}</h2>
                                <div class="mt-2">
                                    <span class="badge bg-light text-dark">All Time</span>
                                </div>
                            </div>
                            <div class="stats-icon-container">
                                <i class="fas fa-id-card"></i>
                            </div>
                        </div>
                        <div class="progress mt-3" style="height: 5px;">
                            <div class="progress-bar" role="progressbar" style="width: 100%; background-color: var(--costa-del-sol);" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-card modern-card">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="text-muted mb-2">Active Employees</h6>
                                <h2 class="mb-0" style="color: var(--costa-del-sol); font-weight: 700;">{{ $stats['employees']['active'] }}</h2>
                                <div class="mt-2">
                                    <span class="badge bg-success">Active</span>
                                </div>
                            </div>
                            <div class="stats-icon-container">
                                <i class="fas fa-user-tie"></i>
                            </div>
                        </div>
                        <div class="progress mt-3" style="height: 5px;">
                            <div class="progress-bar" role="progressbar" style="width: {{ ($stats['employees']['active'] / max(1, $stats['employees']['total'])) * 100 }}%; background-color: var(--costa-del-sol);" aria-valuenow="{{ ($stats['employees']['active'] / max(1, $stats['employees']['total'])) * 100 }}" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-card modern-card">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="text-muted mb-2">Inactive Employees</h6>
                                <h2 class="mb-0" style="color: var(--costa-del-sol); font-weight: 700;">{{ $stats['employees']['inactive'] }}</h2>
                                <div class="mt-2">
                                    <span class="badge bg-secondary">Terminated</span>
                                </div>
                            </div>
                            <div class="stats-icon-container">
                                <i class="fas fa-user-slash"></i>
                            </div>
                        </div>
                        <div class="progress mt-3" style="height: 5px;">
                            <div class="progress-bar" role="progressbar" style="width: {{ ($stats['employees']['inactive'] / max(1, $stats['employees']['total'])) * 100 }}%; background-color: var(--costa-del-sol);" aria-valuenow="{{ ($stats['employees']['inactive'] / max(1, $stats['employees']['total'])) * 100 }}" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-12">
                    <div class="card modern-card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-server me-2"></i> API Services Status
                            </div>
                            <div>
                                <span class="badge bg-success">All Systems Operational</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <div class="service-status-card">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="d-flex align-items-center">
                                                <div class="status-indicator active me-3">
                                                    <i class="fas fa-check"></i>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0" style="color: var(--costa-del-sol);">Authentication Service</h6>
                                                    <small class="text-muted">JWT & Sanctum</small>
                                                </div>
                                            </div>
                                            <span class="uptime">99.9%</span>
                                        </div>
                                        <div class="progress mt-2" style="height: 4px;">
                                            <div class="progress-bar bg-success" role="progressbar" style="width: 99.9%" aria-valuenow="99.9" aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="service-status-card">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="d-flex align-items-center">
                                                <div class="status-indicator active me-3">
                                                    <i class="fas fa-check"></i>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0" style="color: var(--costa-del-sol);">Notification Service</h6>
                                                    <small class="text-muted">Push & Email</small>
                                                </div>
                                            </div>
                                            <span class="uptime">100%</span>
                                        </div>
                                        <div class="progress mt-2" style="height: 4px;">
                                            <div class="progress-bar bg-success" role="progressbar" style="width: 100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="service-status-card">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="d-flex align-items-center">
                                                <div class="status-indicator active me-3">
                                                    <i class="fas fa-check"></i>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0" style="color: var(--costa-del-sol);">Database Service</h6>
                                                    <small class="text-muted">MySQL</small>
                                                </div>
                                            </div>
                                            <span class="uptime">99.8%</span>
                                        </div>
                                        <div class="progress mt-2" style="height: 4px;">
                                            <div class="progress-bar bg-success" role="progressbar" style="width: 99.8%" aria-valuenow="99.8" aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-12">
                    <div class="card modern-card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-history me-2"></i> Recent API Requests
                            </div>
                            <div>
                                <button class="btn btn-sm btn-outline-secondary"><i class="fas fa-sync-alt"></i></button>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th class="ps-3">Timestamp</th>
                                            <th>Endpoint</th>
                                            <th>Method</th>
                                            <th>Status</th>
                                            <th class="text-end pe-3">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse($stats['recent_requests'] as $request)
                                        <tr>
                                            <td class="ps-3">
                                                <div>{{ \Carbon\Carbon::parse($request->created_at)->format('Y-m-d') }}</div>
                                                <small class="text-muted">{{ \Carbon\Carbon::parse($request->created_at)->format('H:i:s') }}</small>
                                            </td>
                                            <td>
                                                <div class="text-truncate" style="max-width: 200px;">
                                                    <span class="text-primary">{{ $request->endpoint }}</span>
                                                </div>
                                            </td>
                                            <td>
                                                @php
                                                    $methodClass = 'secondary';
                                                    if ($request->method == 'GET') $methodClass = 'info';
                                                    if ($request->method == 'POST') $methodClass = 'success';
                                                    if ($request->method == 'PUT' || $request->method == 'PATCH') $methodClass = 'warning';
                                                    if ($request->method == 'DELETE') $methodClass = 'danger';
                                                @endphp
                                                <span class="badge bg-{{ $methodClass }}-subtle text-{{ $methodClass }} method-badge">{{ $request->method }}</span>
                                            </td>
                                            <td>
                                                @php
                                                    $statusClass = 'success';
                                                    if ($request->status_code >= 400 && $request->status_code < 500) $statusClass = 'warning';
                                                    if ($request->status_code >= 500) $statusClass = 'danger';
                                                @endphp
                                                <span class="badge bg-{{ $statusClass }}">{{ $request->status_code }}</span>
                                            </td>
                                            <td class="text-end pe-3">
                                                <button class="btn btn-sm btn-outline-secondary" title="View Details"><i class="fas fa-eye"></i></button>
                                            </td>
                                        </tr>
                                        @empty
                                        <tr>
                                            <td colspan="5" class="text-center py-4">
                                                <div class="empty-state">
                                                    <i class="fas fa-exchange-alt fa-2x mb-3 text-muted"></i>
                                                    <p>No API requests recorded yet</p>
                                                </div>
                                            </td>
                                        </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="card-footer text-end">
                            <a href="#" class="btn btn-sm" style="background-color: var(--costa-del-sol); color: white; border: none;">View All Requests</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            @include('partials.notification_widget')
            
            <div class="alert alert-info modern-card" role="alert">
                <div class="d-flex align-items-center">
                    <i class="fas fa-info-circle fs-4 me-3"></i>
                    <div>
                        <strong>Welcome to Dashboard!</strong>
                        @if(isset($isStudent) && $isStudent)
                            <div class="small">You are logged in as student: {{ $user->full_name ?? $user->username }}</div>
                        @else
                            <div class="small">You are logged in as administrator.</div>
                        @endif
                    </div>
                </div>
            </div>

            <div class="card modern-card mb-4">
                <div class="card-header">
                    <i class="fas fa-info-circle me-2"></i> Server Info
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted d-block mb-1">Server Version</small>
                        <div class="fw-medium">Laravel v{{ app()->version() }}</div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted d-block mb-1">PHP Version</small>
                        <div class="fw-medium">{{ phpversion() }}</div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted d-block mb-1">System Time</small>
                        <div class="fw-medium">{{ now()->format('Y-m-d H:i:s') }}</div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted d-block mb-1">Environment</small>
                        <div class="fw-medium">{{ app()->environment() }}</div>
                    </div>
                </div>
            </div>

            <div class="card modern-card">
                <div class="card-header">
                    <i class="fas fa-link me-2"></i> Quick Links
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush border-0">
                        <a href="{{ url('/api/docs') }}" class="list-group-item list-group-item-action d-flex align-items-center" style="color: var(--costa-del-sol);" target="_blank">
                            <i class="fas fa-book me-3"></i> API Documentation
                        </a>
                        <a href="{{ route('students.index') }}" class="list-group-item list-group-item-action d-flex align-items-center" style="color: var(--costa-del-sol);">
                            <i class="fas fa-user-graduate me-3"></i> Student Management
                        </a>
                        <a href="{{ route('employees.index') }}" class="list-group-item list-group-item-action d-flex align-items-center" style="color: var(--costa-del-sol);">
                            <i class="fas fa-id-card me-3"></i> Employee Management
                        </a>
                        <a href="{{ route('notifications.index') }}" class="list-group-item list-group-item-action d-flex align-items-center" style="color: var(--costa-del-sol);">
                            <i class="fas fa-bell me-3"></i> Notification Management
                        </a>
                        <a href="{{ route('logs.index') }}" class="list-group-item list-group-item-action d-flex align-items-center" style="color: var(--costa-del-sol);">
                            <i class="fas fa-clipboard-list me-3"></i> System Logs
                        </a>
                        <a href="{{ route('backups.index') }}" class="list-group-item list-group-item-action d-flex align-items-center" style="color: var(--costa-del-sol);">
                            <i class="fas fa-database me-3"></i> Database Backups
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section('styles')
<style>
    .stats-icon-container {
        width: 60px;
        height: 60px;
        background: linear-gradient(45deg, var(--costa-del-sol), var(--locust));
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        box-shadow: 0 4px 10px rgba(93, 110, 53, 0.2);
    }
    
    .modern-card {
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        border: none;
        transition: all 0.3s ease;
    }
    
    .modern-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }
    
    .service-status-card {
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 8px;
        transition: all 0.3s ease;
    }
    
    .service-status-card:hover {
        background-color: #fff;
        box-shadow: 0 4px 8px rgba(0,0,0,0.05);
    }
    
    .status-indicator {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: #e9ecef;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .status-indicator.active {
        background-color: #28a745;
        color: white;
    }
    
    .uptime {
        font-weight: 600;
        color: var(--costa-del-sol);
    }
    
    .method-badge {
        min-width: 65px;
        text-align: center;
    }
    
    .empty-state {
        padding: 20px;
        text-align: center;
        color: #6c757d;
    }
    
    @media (max-width: 768px) {
        .stats-card {
            margin-bottom: 1rem;
        }
    }
</style>
@endsection
@endsection
