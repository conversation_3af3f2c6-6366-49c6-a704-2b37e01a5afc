@php
use Illuminate\Support\Facades\Auth;
@endphp
@extends('layouts.app')

@section('styles')
<style>
    .notification-title {
        color: var(--costa-del-sol);
        transition: color 0.2s ease;
        text-decoration: none;
        position: relative;
    }
    
    .notification-title:hover {
        color: var(--locust);
    }
    
    .notification-title:after {
        content: '';
        position: absolute;
        width: 100%;
        transform: scaleX(0);
        height: 1px;
        bottom: -2px;
        left: 0;
        background-color: var(--locust);
        transform-origin: bottom right;
        transition: transform 0.2s ease-out;
    }
    
    .notification-title:hover:after {
        transform: scaleX(1);
        transform-origin: bottom left;
    }
</style>
@endsection

@section('content')
<div class="container-fluid main-content px-4">
    <div class="row">
        <div class="col-md-9">
            <div class="d-flex justify-content-between align-items-center mb-4 mt-2">
                <div>
                    <h2 class="mb-1" style="color: var(--costa-del-sol); font-weight: 700;">Student Dashboard</h2>
                    <p class="mb-0" style="color: var(--gurkha);">Welcome, {{ $student->full_name ?? $student->username }}</p>
                </div>
                <div>
                    <span class="badge bg-success p-2">Student Portal</span>
                </div>
            </div>

            <!-- Personal Information, Academic Information and Notifications in same row -->
            <div class="row mb-4">
                <!-- Personal Information Card -->
                <div class="col-md-4">
                    <div class="card h-100">
                        <div class="card-header" style="background-color: var(--thistle-green);">
                            <i class="fas fa-user-circle me-2"></i> Personal Information
                        </div>
                        <div class="card-body" style="background-color: var(--parchment);">
                            <div class="row mb-2">
                                <div class="col-md-5 fw-bold">Full Name:</div>
                                <div class="col-md-7">{{ $student->full_name }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-md-5 fw-bold">Username:</div>
                                <div class="col-md-7">{{ $student->username }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-md-5 fw-bold">Phone:</div>
                                <div class="col-md-7">{{ $student->phone }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-md-5 fw-bold">Nationality:</div>
                                <div class="col-md-7">{{ $student->nationality }}</div>
                            </div>
                            <div class="mt-3 d-flex flex-wrap justify-content-center gap-2">
                                <a href="{{ route('student.profile.edit') }}" class="btn btn-sm" style="background-color: var(--costa-del-sol); color: white;">
                                    <i class="fas fa-user-edit me-1"></i> Update Profile
                                </a>
                                <a href="{{ route('student.profile.change-password') }}" class="btn btn-sm" style="background-color: var(--costa-del-sol); color: white;">
                                    <i class="fas fa-key me-1"></i> Change Password
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Academic Information Card -->
                <div class="col-md-4">
                    <div class="card h-100">
                        <div class="card-header" style="background-color: var(--thistle-green);">
                            <i class="fas fa-graduation-cap me-2"></i> Academic Information
                        </div>
                        <div class="card-body" style="background-color: var(--parchment);">
                            <div class="row mb-2">
                                <div class="col-md-5 fw-bold">Specialization:</div>
                                <div class="col-md-7">{{ $student->specialization }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-md-5 fw-bold">Result:</div>
                                <div class="col-md-7">
                                    @php
                                        $resultText = $student->result ?? 'Not specified';
                                        $resultColor = '';
                                        if ($resultText == 'ناجح') {
                                            $resultColor = '#28a745'; // Green
                                        } elseif ($resultText == 'راسب') {
                                            $resultColor = '#dc3545'; // Red
                                        } elseif ($resultText == 'اكمال') {
                                            $resultColor = '#fd7e14'; // Orange
                                        }
                                    @endphp
                                    @if ($resultColor)
                                        <span style="color: {{ $resultColor }}; font-weight: bold;">{{ $resultText }}</span>
                                    @else
                                        {{ $resultText }}
                                    @endif
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-md-5 fw-bold">Status:</div>
                                <div class="col-md-7">
                                    <span style="color: #28a745; font-weight: bold;">{{ $student->status ?? 'Active' }}</span>
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-md-5 fw-bold">Class:</div>
                                <div class="col-md-7">{{ $student->class ?? 'Not specified' }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-md-5 fw-bold">Section:</div>
                                <div class="col-md-7">{{ $student->section ?? 'Not specified' }}</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Notification Icon Card -->
                <div class="col-md-4">
                    <div class="card h-100">
                        <div class="card-header" style="background-color: var(--thistle-green);">
                            <i class="fas fa-bell me-2"></i> Notifications
                        </div>
                        <div class="card-body d-flex align-items-center justify-content-center" style="background-color: var(--parchment);">
                            <a href="{{ route('student.notifications') }}" style="text-decoration: none; display: block; text-align: center;">
                                <div class="position-relative d-inline-block mb-3">
                                    <i class="fas fa-bell fa-5x" style="color: var(--costa-del-sol);"></i>
                                    
                                    @php
                                    // Count unread notifications
                                    $badgeCount = 0;
                                    if (Auth::guard('student')->check()) {
                                        $studentId = Auth::guard('student')->id();
                                        $badgeCount = \App\Models\NotificationRecipient::where('recipient_id', $studentId)
                                            ->where('recipient_type', 'student')
                                            ->whereNull('read_at')
                                            ->count();
                                    }
                                    @endphp
                                    
                                    <!-- Only show badge if there are notifications -->
                                    @if($badgeCount > 0)
                                    <div class="position-absolute" 
                                         style="top: -10px; right: -10px; 
                                         min-width: 28px; height: 28px; 
                                         background-color: #ff0000; 
                                         border-radius: 50%; 
                                         border: 2px solid white; 
                                         color: white; 
                                         font-weight: bold; 
                                         display: flex; 
                                         justify-content: center; 
                                         align-items: center; 
                                         font-size: 16px; 
                                         box-shadow: 0 2px 5px rgba(0,0,0,0.3); 
                                         padding: 2px;">
                                        {{ $badgeCount }}
                                    </div>
                                    @endif
                                </div>
                                <div>
                                    <span class="btn btn-sm" style="background-color: var(--costa-del-sol); color: white; border: none;">
                                        <i class="fas fa-eye me-1"></i> View Notifications
                                    </span>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Academic Performance -->
            <!-- <div class="row">
                <div class="col-12">
                    <div class="card modern-card mb-4">
                        <div class="card-header" style="background-color: var(--thistle-green); color: var(--costa-del-sol);">
                            <i class="fas fa-graduation-cap me-2"></i> Academic Information
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <div class="border rounded p-3 text-center">
                                        <h5>Specialization</h5>
                                        <p class="mb-0 fw-bold">{{ $student->specialization ?? 'Not specified' }}</p>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="border rounded p-3 text-center">
                                        <h5>Result</h5>
                                        <p class="mb-0 fw-bold">{{ $student->result ?? 'Not specified' }}</p>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <div class="border rounded p-3 text-center">
                                        <h5>Status</h5>
                                        <p class="mb-0 fw-bold">
                                            @if($student->is_active)
                                                <span class="text-success">Active</span>
                                            @else
                                                <span class="text-danger">Inactive</span>
                                            @endif
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div> -->
        </div>
        
        <div class="col-md-3">
            <div class="alert alert-info modern-card" role="alert">
                <div class="d-flex align-items-center">
                    <i class="fas fa-info-circle fs-4 me-3"></i>
                    <div>
                        <strong>Student Portal</strong>
                        <div class="small">You are logged in as student</div>
                    </div>
                </div>
            </div>
            
            <!-- <div class="card modern-card mb-4 d-none d-md-block">
                <div class="card-header">
                    <i class="fas fa-link me-2"></i> Quick Links
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush border-0">
                        <a href="{{ route('student.notifications') }}" class="list-group-item list-group-item-action d-flex align-items-center" style="color: var(--costa-del-sol);">
                            <i class="fas fa-bell me-3"></i> My Notifications
                        </a>
                        <a href="{{ route('student.profile.edit') }}" class="list-group-item list-group-item-action d-flex align-items-center" style="color: var(--costa-del-sol);">
                            <i class="fas fa-user-cog me-3"></i> Update Profile
                        </a>
                        <a href="{{ route('student.profile.change-password') }}" class="list-group-item list-group-item-action d-flex align-items-center" style="color: var(--costa-del-sol);">
                            <i class="fas fa-key me-3"></i> Change Password
                        </a>
                    </div>
                </div>
            </div> -->
        </div>
    </div>
</div>

<!-- Global Notification Modal -->
<div class="modal fade" id="globalNotificationModal" tabindex="-1" aria-labelledby="notificationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header" style="background-color: var(--thistle-green);">
                <h5 class="modal-title" id="notificationModalTitle" style="color: var(--costa-del-sol);">Notification Title</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <div id="notificationModalContent" class="notification-message mb-4" style="font-size: 1.1rem;"></div>
                <div id="notificationModalAttachments" class="mt-4 mb-3 d-none">
                    <h6><i class="fas fa-paperclip me-1"></i> Attachments</h6>
                    <div id="notificationAttachmentsList" class="d-flex flex-wrap"></div>
                </div>
                <div class="mt-3" style="border-top: 1px solid #eee; padding-top: 10px;">
                    <small class="text-muted" id="notificationModalDate"></small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <a id="notificationDetailsLink" href="#" class="btn" style="background-color: var(--costa-del-sol); color: white;">View Details</a>
            </div>
        </div>
    </div>
</div>

@section('scripts')
<script>
    // Global variables
    let notificationModal;
    
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize modal
        const modalElement = document.getElementById('globalNotificationModal');
        if (modalElement) {
            notificationModal = new bootstrap.Modal(modalElement);
        }
    });
    
    function showNotificationPopup(notificationId) {
        console.log('Opening notification:', notificationId);
        
        // Show loading state in modal
        document.getElementById('notificationModalTitle').textContent = 'Loading...';
        document.getElementById('notificationModalContent').innerHTML = '<p class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading notification content...</p>';
        document.getElementById('notificationModalDate').textContent = '';
        
        // Clear any existing attachments
        const attachmentsContainer = document.getElementById('notificationModalAttachments');
        if (attachmentsContainer) {
            attachmentsContainer.classList.add('d-none');
        }
        
        // Show the modal immediately while content loads
        if (notificationModal) {
            notificationModal.show();
        } else {
            console.error('Modal not initialized');
            return;
        }
        
        // Set the details link
        document.getElementById('notificationDetailsLink').href = `{{ route('student.notifications') }}?notification_id=${notificationId}`;
        
        // Fetch notification data via AJAX
        fetch('{{ url("/student/notification") }}/' + notificationId, {
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json'
            }
        })
            .then(response => response.json())
            .then(data => {
                console.log('Notification data:', data);
                console.log('Message received:', data.notification?.message);
                console.log('Message type:', typeof data.notification?.message);
                console.log('Message length:', data.notification?.message?.length || 0);
                
                if (data.success && data.notification) {
                    try {
                        // Update modal content with notification data
                        const titleElement = document.getElementById('notificationModalTitle');
                        titleElement.textContent = data.notification.title || 'Notification';
                        
                        // Display message with proper formatting
                        const contentElement = document.getElementById('notificationModalContent');
                        contentElement.innerHTML = '';
                        const paragraph = document.createElement('p');
                        paragraph.style.whiteSpace = 'pre-wrap';
                        paragraph.style.marginBottom = '15px';
                        
                        // Make sure message exists and handle empty message gracefully
                        if (data.notification.message && typeof data.notification.message === 'string' && data.notification.message.trim() !== '') {
                            paragraph.textContent = data.notification.message;
                        } else {
                            paragraph.textContent = '(No message available)';
                            console.warn('No message available for notification:', data.notification.id);
                        }
                        
                        contentElement.appendChild(paragraph);
                    } catch (error) {
                        console.error('Error updating notification modal content:', error);
                        document.getElementById('notificationModalContent').innerHTML = '<p class="text-danger">Error displaying notification content. Please try again or view the full notification details.</p>';
                    }
                    
                    // Update date
                    document.getElementById('notificationModalDate').textContent = `Sent ${data.notification.created_at}`;
                    
                    // Display attachments if any
                    const attachmentsContainer = document.getElementById('notificationModalAttachments');
                    const attachmentsList = document.getElementById('notificationAttachmentsList');
                    
                    console.log('Checking for attachments:', data.notification.attachments);
                    console.log('Attachments type:', typeof data.notification.attachments);
                    
                    if (attachmentsContainer && attachmentsList) {
                        // Clear previous attachments
                        attachmentsList.innerHTML = '';
                        
                        // More robust attachment handling
                        let attachmentsToShow = [];
                        
                        if (Array.isArray(data.notification.attachments) && data.notification.attachments.length > 0) {
                            attachmentsToShow = data.notification.attachments;
                        }
                        // Try parsing if it's a string (JSON)
                        else if (typeof data.notification.attachments === 'string' && data.notification.attachments.trim() !== '') {
                            try {
                                const parsed = JSON.parse(data.notification.attachments);
                                if (Array.isArray(parsed)) {
                                    attachmentsToShow = parsed;
                                    console.log('Parsed attachments from JSON string');
                                }
                            } catch (e) {
                                console.error('Failed to parse attachments JSON:', e);
                            }
                        }
                        
                        console.log('Processed attachments to show:', attachmentsToShow);
                        
                        if (attachmentsToShow.length > 0) {
                            console.log('Found attachments:', attachmentsToShow.length);
                            
                            // Show attachments section with animation
                            attachmentsContainer.classList.remove('d-none');
                            attachmentsContainer.style.opacity = '0';
                            attachmentsContainer.style.transition = 'opacity 0.3s';
                            setTimeout(() => { attachmentsContainer.style.opacity = '1'; }, 10);
                            
                            // Add each attachment
                            attachmentsToShow.forEach(attachment => {
                                console.log('Processing attachment:', attachment);
                                if (attachment.path && attachment.name) {
                                    const attachmentLink = document.createElement('a');
                                    attachmentLink.href = `/storage/${attachment.path}`;
                                    attachmentLink.className = 'badge rounded-pill text-decoration-none me-2 mb-2 p-2';
                                    attachmentLink.style.backgroundColor = 'var(--locust)';
                                    attachmentLink.style.color = 'white';
                                    attachmentLink.target = '_blank';
                                    
                                    // Determine icon based on file extension
                                    let fileIcon = 'fa-file';
                                    const ext = attachment.name.split('.').pop().toLowerCase();
                                    if (['pdf'].includes(ext)) fileIcon = 'fa-file-pdf';
                                    else if (['doc', 'docx'].includes(ext)) fileIcon = 'fa-file-word';
                                    else if (['xls', 'xlsx'].includes(ext)) fileIcon = 'fa-file-excel';
                                    else if (['jpg', 'jpeg', 'png', 'gif'].includes(ext)) fileIcon = 'fa-file-image';
                                    
                                    attachmentLink.innerHTML = `<i class="fas ${fileIcon} me-1"></i> ${attachment.name}`;
                                    attachmentsList.appendChild(attachmentLink);
                                }
                            });
                        } else {
                            console.log('No attachments found');
                            // Hide attachments section if none
                            attachmentsContainer.classList.add('d-none');
                        }
                    }
                    
                    // Mark notification as read via AJAX
                    fetch(`{{ url('/student/notifications/mark-read') }}/${notificationId}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        }
                    }).then(response => {
                        console.log('Notification marked as read');
                    }).catch(error => {
                        console.error('Error marking notification as read:', error);
                    });
                    
                } else {
                    // Show error
                    document.getElementById('notificationModalTitle').textContent = 'Error';
                    document.getElementById('notificationModalContent').innerHTML = '<p class="text-danger">Could not load notification content. Please try again later.</p>';
                }
            })
            .catch(error => {
                console.error('Error fetching notification:', error);
                document.getElementById('notificationModalTitle').textContent = 'Error';
                document.getElementById('notificationModalContent').innerHTML = '<p class="text-danger">Failed to load notification data. Please try again later.</p>';
            });
            
        // Prevent default behavior if called from link
        return false;
    } // Close the function properly
</script>
@endsection

@endsection
