@extends('layouts.app')

@section('styles')
<style>
    /* Custom checkbox styling */
    .form-check-input {
        border-color: var(--thistle-green);
        background-color: var(--conch);
        cursor: pointer;
        transition: all 0.2s ease;
    }
    .form-check-input:checked {
        background-color: var(--thistle-green);
        border-color: var(--thistle-green);
        box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.15);
    }
    .form-check-input:focus {
        border-color: var(--costa-del-sol);
        box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.25);
    }
    
    /* Custom pagination styling */
    .custom-pagination .pagination {
        margin-bottom: 0;
    }
    .page-item.active .page-link {
        background-color: var(--thistle-green);
        border-color: var(--thistle-green);
        color: white;
    }
    .page-link {
        color: var(--costa-del-sol);
        border-color: #dee2e6;
        transition: all 0.2s ease;
    }
    .page-link:hover {
        color: white;
        background-color: var(--costa-del-sol);
        border-color: var(--costa-del-sol);
    }
    .page-item.disabled .page-link {
        color: #6c757d;
        background-color: #fff;
    }
</style>
@endsection

@section('content')
<div class="container main-content">
    @if(session('debug'))
        <div class="alert alert-info mb-4">
            <h5>Debug Information</h5>
            <pre>{{ json_encode(session('debug'), JSON_PRETTY_PRINT|JSON_UNESCAPED_UNICODE) }}</pre>
        </div>
    @endif
    
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div class="d-flex align-items-center">
            <h5 style="color: var(--costa-del-sol); margin-right: 15px;">Employee Management</h5>
            <a href="{{ route('employees.refresh-cache') }}" class="btn btn-sm btn-outline-secondary" title="Refresh dropdown data">
                <i class="fas fa-sync-alt"></i> Refresh Data
            </a>
        </div>
        <div>
            <button type="button" class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#addEmployeeModal">
                <i class="fas fa-plus"></i> Add Employee
            </button>
            <button type="button" class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#importExcelModal">
                <i class="fas fa-file-excel"></i> Import from Excel
            </button>
            <div class="dropdown d-inline-block bulk-actions" id="bulk-actions-container" style="display: none;">
                <button class="btn btn-secondary dropdown-toggle" type="button" id="bulkActionsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    Bulk Actions
                </button>
                <ul class="dropdown-menu" aria-labelledby="bulkActionsDropdown">
                    <li><a class="dropdown-item bulk-action" href="#" data-action="activate">
                        <i class="fas fa-check-circle text-success"></i> Activate Selected
                    </a></li>
                    <li><a class="dropdown-item bulk-action" href="#" data-action="deactivate">
                        <i class="fas fa-times-circle text-warning"></i> Deactivate Selected
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item bulk-action text-danger" href="#" data-action="delete">
                        <i class="fas fa-trash"></i> Delete Selected
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item bulk-action text-danger fw-bold" href="#" data-action="delete-all">
                        <i class="fas fa-exclamation-triangle"></i> Delete All Employees
                    </a></li>
                </ul>
            </div>
        </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success">
            {{ session('success') }}
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger">
            {{ session('error') }}
        </div>
    @endif

    <!-- Employees Table -->
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <span>Employees List</span>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-secondary btn-sm" type="button" data-bs-toggle="collapse" data-bs-target="#searchFilters" aria-expanded="false">
                        <i class="fas fa-filter"></i> Filters
                    </button>
                </div>
            </div>

            <!-- Active Filters Display -->
            @if(request('search') || request('contract_type') || request('employee_type'))
                <div class="mb-3">
                    <div class="d-flex flex-wrap gap-2 align-items-center">
                        <span class="text-muted">Active filters:</span>

                        @if(request('search'))
                            <span class="badge bg-primary">
                                Search: "{{ request('search') }}"
                                <a href="{{ request()->fullUrlWithQuery(['search' => null]) }}" class="text-white ms-1">×</a>
                            </span>
                        @endif

                        @if(request('contract_type'))
                            <span class="badge bg-info">
                                Contract: {{ request('contract_type') }}
                                <a href="{{ request()->fullUrlWithQuery(['contract_type' => null]) }}" class="text-white ms-1">×</a>
                            </span>
                        @endif

                        @if(request('employee_type'))
                            <span class="badge bg-success">
                                Type: {{ request('employee_type') }}
                                <a href="{{ request()->fullUrlWithQuery(['employee_type' => null]) }}" class="text-white ms-1">×</a>
                            </span>
                        @endif

                        <a href="{{ route('employees.index') }}" class="btn btn-sm btn-outline-secondary">
                            Clear All
                        </a>
                    </div>
                </div>
            @endif

            <!-- Search and Filter Form -->
            <div class="collapse {{ request('search') || request('contract_type') || request('employee_type') ? 'show' : '' }}" id="searchFilters">
                <form action="{{ route('employees.index') }}" method="GET" class="row g-3 p-3 bg-light rounded">
                    <!-- General Search -->
                    <div class="col-md-4">
                        <label for="search" class="form-label">General Search</label>
                        <input class="form-control" type="search" name="search" id="search"
                               placeholder="Search by name, phone, username..." value="{{ request('search') }}">
                    </div>

                    <!-- Contract Type Filter -->
                    <div class="col-md-4">
                        <label for="contract_type" class="form-label">Contract Type</label>
                        <select class="form-select" name="contract_type" id="contract_type">
                            <option value="">All Contract Types</option>
                            @foreach($contractTypes as $type)
                                <option value="{{ $type }}" {{ request('contract_type') == $type ? 'selected' : '' }}>
                                    {{ $type }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Employee Type Filter -->
                    <div class="col-md-4">
                        <label for="employee_type" class="form-label">Employee Type</label>
                        <select class="form-select" name="employee_type" id="employee_type">
                            <option value="">All Employee Types</option>
                            @foreach($employeeTypes as $type)
                                <option value="{{ $type }}" {{ request('employee_type') == $type ? 'selected' : '' }}>
                                    {{ $type }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Action Buttons -->
                    <div class="col-12">
                        <div class="d-flex gap-2">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search"></i> Apply Filters
                            </button>
                            <a href="{{ route('employees.index') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> Clear Filters
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped employee-table">
                    <thead>
                        <tr>
                            <th>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAll">
                                </div>
                            </th>
                            <th>ID</th>
                            <th>Username</th>
                            <th>Full Name</th>
                            <th>Phone</th>
                            <th>Contract Type</th>
                            <th>Employee Type</th>
                            <th>Job Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($employees as $employee)
                            <tr>
                                <td>
                                    <div class="form-check">
                                        <input class="form-check-input employee-checkbox" type="checkbox" value="{{ $employee->id }}" id="employee-{{ $employee->id }}">
                                    </div>
                                </td>
                                <td>{{ $employee->id }}</td>
                                <td>{{ $employee->username }}</td>
                                <td>{{ $employee->full_name }}</td>
                                <td>{{ $employee->phone }}</td>
                                <td>{{ $employee->contract_type }}</td>
                                <td>{{ $employee->employee_type }}</td>
                                <td>{{ $employee->job_status }}</td>
                                <td>
                                    <button class="btn btn-sm btn-primary edit-employee" 
                                        data-id="{{ $employee->id }}"
                                        data-full-name="{{ $employee->full_name }}"
                                        data-phone="{{ $employee->phone }}"
                                        data-contract-type="{{ $employee->contract_type }}"
                                        data-employee-type="{{ $employee->employee_type }}"
                                        data-job-status="{{ $employee->job_status }}"
                                        data-automatic-number="{{ $employee->automatic_number ?? '' }}"
                                        data-financial-number="{{ $employee->financial_number ?? '' }}"
                                        data-state-cooperative-number="{{ $employee->state_cooperative_number ?? '' }}"
                                        data-bank-account-number="{{ $employee->bank_account_number ?? '' }}"
                                        data-username="{{ $employee->username }}"
                                        data-bs-toggle="modal" 
                                        data-bs-target="#editEmployeeModal">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <form class="d-inline delete-form" action="{{ route('employees.destroy', $employee->id) }}" method="POST">
                                        @csrf
                                        @method('DELETE')
                                        <button type="button" class="btn btn-sm btn-danger delete-btn">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="8" class="text-center">No employees found</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
            <div class="d-flex justify-content-center mt-4">
                <div class="custom-pagination">
                    {{ $employees->links('pagination::bootstrap-5') }}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Import Excel Modal -->
<div class="modal fade" id="importExcelModal" tabindex="-1" aria-labelledby="importExcelModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header" style="background-color: var(--thistle-green);">
                <h5 class="modal-title" id="importExcelModalLabel" style="color: var(--costa-del-sol);">Import Employees from Excel</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="importExcelForm" action="{{ route('employees.import') }}" method="POST" enctype="multipart/form-data">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="excel_file" class="form-label">Excel File</label>
                        <input type="file" class="form-control" id="excel_file" name="excel_file" accept=".xlsx,.xls,.csv" required>
                    </div>
                    <div class="mb-3">
                        <div class="alert alert-info">
                            <h6>Excel File Format:</h6>
                            <p class="mb-1">Your Excel file should contain the following columns:</p>
                            <ul class="mb-0">
                                <!-- employee_id field removed -->
                                <li>full_name</li>
                                <li>contract_type (required, must match existing values in system)</li>
                                <li>employee_type (required, must match existing values in system)</li>
                                <li>phone (optional)</li>
                                <li>job_status (must match existing values in system)</li>
                                <li>automatic_number (optional)</li>
                                <li>financial_number (optional)</li>
                                <li>state_cooperative_number (optional)</li>
                                <li>bank_account_number (optional)</li>
                                <li>username (or will be auto-generated)</li>
                                <li>password (or will default to 'emp123')</li>
                            </ul>
                        </div>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="has_header" name="has_header" checked>
                        <label class="form-check-label" for="has_header">File has header row</label>
                    </div>
                </div>
                <div class="progress mb-3" style="display: none;" id="import-progress-container">
                    <div id="import-progress-bar" class="progress-bar bg-success progress-bar-striped progress-bar-animated" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 0%">0%</div>
                </div>
                <div id="import-status" class="alert alert-info mb-3" style="display: none;"></div>
                <div class="modal-footer">
                    <a href="{{ route('employees.export.template') }}" class="btn btn-outline-secondary">Download Template</a>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="import-submit-btn">Import</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Employee Modal -->
<div class="modal fade" id="editEmployeeModal" tabindex="-1" aria-labelledby="editEmployeeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header" style="background-color: var(--thistle-green);">
                <h5 class="modal-title" id="editEmployeeModalLabel" style="color: var(--costa-del-sol);">Edit Employee</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editEmployeeForm" action="" method="POST">
                @csrf
                @method('PUT')
                <div class="modal-body">
                    <!-- Employee ID field removed as not needed -->
                    <div class="mb-3">
                        <label for="edit_full_name" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="edit_full_name" name="full_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_contract_type" class="form-label">Contract Type</label>
                        <select class="form-select" id="edit_contract_type" name="contract_type" required>
                            <option value="">Select Contract Type</option>
                            @foreach($contractTypes as $type)
                                <option value="{{ $type }}">{{ $type }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_employee_type" class="form-label">Employee Type</label>
                        <select class="form-select" id="edit_employee_type" name="employee_type" required>
                            <option value="">Select Employee Type</option>
                            @foreach($employeeTypes as $type)
                                <option value="{{ $type }}">{{ $type }}</option>
                            @endforeach
                        </select>

                    </div>
                    <div class="mb-3">
                        <label for="edit_phone" class="form-label">Phone</label>
                        <input type="text" class="form-control" id="edit_phone" name="phone">
                    </div>
                    <div class="mb-3">
                        <label for="edit_job_status" class="form-label">Job Status</label>
                        <select class="form-select" id="edit_job_status" name="job_status">
                            <option value="">Select Job Status</option>
                            @foreach($jobStatuses as $status)
                                <option value="{{ $status }}">{{ $status }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_automatic_number" class="form-label">Automatic Number</label>
                        <input type="text" class="form-control" id="edit_automatic_number" name="automatic_number" placeholder="Enter automatic number (optional)">
                    </div>
                    <div class="mb-3">
                        <label for="edit_financial_number" class="form-label">Financial Number</label>
                        <input type="text" class="form-control" id="edit_financial_number" name="financial_number" placeholder="Enter financial number (optional)">
                    </div>
                    <div class="mb-3">
                        <label for="edit_state_cooperative_number" class="form-label">State Cooperative Number</label>
                        <input type="text" class="form-control" id="edit_state_cooperative_number" name="state_cooperative_number" placeholder="Enter state cooperative number (optional)">
                    </div>
                    <div class="mb-3">
                        <label for="edit_bank_account_number" class="form-label">Bank Account Number</label>
                        <input type="text" class="form-control" id="edit_bank_account_number" name="bank_account_number" placeholder="Enter bank account number (optional)">
                    </div>
                    <div class="mb-3">
                        <label for="edit_username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="edit_username" name="username" required readonly style="background-color: #f8f9fa;">
                    </div>
                    <div class="mb-3">
                        <label for="edit_password" class="form-label">New Password (leave blank to keep current)</label>
                        <input type="password" class="form-control" id="edit_password" name="password">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Employee</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bulk Action Form -->
<form id="bulk-action-form" action="{{ route('employees.bulk') }}" method="POST" style="display: none;">
    @csrf
    <input type="hidden" name="action" id="bulk-action">
    <input type="hidden" name="ids" id="selected-ids">
</form>

<!-- Add Employee Modal -->
<div class="modal fade" id="addEmployeeModal" tabindex="-1" aria-labelledby="addEmployeeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header" style="background-color: var(--thistle-green);">
                <h5 class="modal-title" id="addEmployeeModalLabel" style="color: var(--costa-del-sol);">Add New Employee</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ route('employees.store') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <!-- Employee ID field removed as not needed -->
                    <div class="mb-3">
                        <label for="full_name" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="full_name" name="full_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="contract_type" class="form-label">Contract Type</label>
                        <select class="form-select" id="contract_type" name="contract_type" required>
                            <option value="">Select Contract Type</option>
                            @foreach($contractTypes as $type)
                                <option value="{{ $type }}">{{ $type }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="employee_type" class="form-label">Employee Type</label>
                        <select class="form-select" id="employee_type" name="employee_type" required>
                            <option value="">Select Employee Type</option>
                            @foreach($employeeTypes as $type)
                                <option value="{{ $type }}">{{ $type }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="phone" class="form-label">Phone</label>
                        <input type="text" class="form-control" id="phone" name="phone">
                    </div>
                    <div class="mb-3">
                        <label for="job_status" class="form-label">Job Status</label>
                        <select class="form-select" id="job_status" name="job_status">
                            @foreach($jobStatuses as $status)
                                <option value="{{ $status }}" {{ $status === 'Active' ? 'selected' : '' }}>{{ $status }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="automatic_number" class="form-label">Automatic Number</label>
                        <input type="text" class="form-control" id="automatic_number" name="automatic_number" placeholder="Enter automatic number (optional)">
                    </div>
                    <div class="mb-3">
                        <label for="financial_number" class="form-label">Financial Number</label>
                        <input type="text" class="form-control" id="financial_number" name="financial_number" placeholder="Enter financial number (optional)">
                    </div>
                    <div class="mb-3">
                        <label for="state_cooperative_number" class="form-label">State Cooperative Number</label>
                        <input type="text" class="form-control" id="state_cooperative_number" name="state_cooperative_number" placeholder="Enter state cooperative number (optional)">
                    </div>
                    <div class="mb-3">
                        <label for="bank_account_number" class="form-label">Bank Account Number</label>
                        <input type="text" class="form-control" id="bank_account_number" name="bank_account_number" placeholder="Enter bank account number (optional)">
                    </div>
                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" name="username" placeholder="e.g. emp12345">
                        <small class="text-muted">Leave blank to auto-generate</small>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password">
                        <small class="text-muted">Leave blank for default password (emp123)</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Employee</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Bulk actions logic
        const bulkActionsContainer = document.getElementById('bulk-actions-container');
        const checkboxes = document.querySelectorAll('.employee-checkbox');
        const selectAllCheckbox = document.getElementById('selectAll');
        
        // Show/hide bulk actions dropdown based on checkbox selection
        function updateBulkActionsVisibility() {
            const checkedCount = document.querySelectorAll('.employee-checkbox:checked').length;
            if (checkedCount > 0) {
                bulkActionsContainer.style.display = 'inline-block';
            } else {
                bulkActionsContainer.style.display = 'none';
            }
        }
        
        // Handle checkbox changes
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateBulkActionsVisibility);
        });
        
        // Handle select all checkbox
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                checkboxes.forEach(cb => { cb.checked = this.checked; });
                updateBulkActionsVisibility();
            });
        }
        
        // Handle bulk action selection
        const bulkActionLinks = document.querySelectorAll('.bulk-action');
        bulkActionLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();

                const action = this.dataset.action;
                const form = document.getElementById('bulk-action-form');
                const actionInput = document.getElementById('bulk-action');
                const selectedIds = [];
                
                // Get selected employee IDs (except for delete-all action)
                if (action !== 'delete-all') {
                    document.querySelectorAll('.employee-checkbox:checked').forEach(cb => {
                        selectedIds.push(cb.value);
                    });

                    if (selectedIds.length === 0) {
                        alert('No employees selected!');
                        return;
                    }
                }
                
                // Set action and employee IDs
                actionInput.value = action;
                document.getElementById('selected-ids').value = selectedIds.join(',');
                
                // Confirm action
                let confirmMessage = '';
                switch(action) {
                    case 'activate':
                        confirmMessage = `Are you sure you want to activate ${selectedIds.length} selected employees?`;
                        break;
                    case 'deactivate':
                        confirmMessage = `Are you sure you want to deactivate ${selectedIds.length} selected employees?`;
                        break;
                    case 'delete':
                        confirmMessage = `⚠️ WARNING: Delete ${selectedIds.length} selected employees?\n\nThis action cannot be undone!`;
                        break;
                    case 'delete-all':
                        confirmMessage = '🚨 DANGER: Delete ALL employees from the system?\n\nThis will permanently remove ALL employee records and cannot be undone!\n\nType "DELETE ALL" to confirm this action.';
                        break;
                }
                
                // Special confirmation for delete-all
                if (action === 'delete-all') {
                    const userInput = prompt(confirmMessage);
                    if (userInput === 'DELETE ALL') {
                        form.submit();
                    } else if (userInput !== null) {
                        alert('Confirmation text does not match. Action cancelled.');
                    }
                } else {
                    if (confirm(confirmMessage)) {
                        form.submit();
                    }
                }
            });
        });
        
        // Edit employee modal population
        const editButtons = document.querySelectorAll('.edit-employee');
        editButtons.forEach(button => {
            button.addEventListener('click', function() {
                console.log('=== Edit Employee Modal ===');

                // Get all data using getAttribute for consistency
                const id = this.getAttribute('data-id');
                const fullName = this.getAttribute('data-full-name');
                const phone = this.getAttribute('data-phone');
                const contractType = this.getAttribute('data-contract-type');
                const employeeType = this.getAttribute('data-employee-type');
                const jobStatus = this.getAttribute('data-job-status');
                const automaticNumber = this.getAttribute('data-automatic-number') || '';
                const financialNumber = this.getAttribute('data-financial-number') || '';
                const stateCooperativeNumber = this.getAttribute('data-state-cooperative-number') || '';
                const bankAccountNumber = this.getAttribute('data-bank-account-number') || '';
                const username = this.getAttribute('data-username');

                // Debug the four problem fields
                console.log('Edit Employee - ID:', id, 'Name:', fullName);
                console.log('Additional fields:', {automaticNumber, financialNumber, stateCooperativeNumber, bankAccountNumber});

                // Set form action URL
                document.getElementById('editEmployeeForm').action = `/employees/${id}`;

                // Fill basic form fields
                document.getElementById('edit_full_name').value = fullName || '';
                document.getElementById('edit_phone').value = phone || '';
                document.getElementById('edit_username').value = username || '';

                // Fill the four additional fields
                document.getElementById('edit_automatic_number').value = automaticNumber;
                document.getElementById('edit_financial_number').value = financialNumber;
                document.getElementById('edit_state_cooperative_number').value = stateCooperativeNumber;
                document.getElementById('edit_bank_account_number').value = bankAccountNumber;

                // Set dropdown values
                try {
                    document.getElementById('edit_contract_type').value = contractType || '';
                    document.getElementById('edit_employee_type').value = employeeType || '';
                    document.getElementById('edit_job_status').value = jobStatus || '';
                } catch (error) {
                    console.log('Error setting dropdowns:', error);
                }

                console.log('=== Modal populated successfully ===');
            });
        });
        
        // Delete confirmation
        const deleteForms = document.querySelectorAll('.delete-form');
        deleteForms.forEach(form => {
            const deleteBtn = form.querySelector('.delete-btn');
            deleteBtn.addEventListener('click', function() {
                if (confirm('Are you sure you want to delete this employee? This action cannot be undone.')) {
                    form.submit();
                }
            });
        });
        
        // Import Excel logic
        const importForm = document.getElementById('importExcelForm');
        const progressBar = document.getElementById('import-progress-bar');
        const progressContainer = document.getElementById('import-progress-container');
        const statusContainer = document.getElementById('import-status');
        const submitBtn = document.getElementById('import-submit-btn');
        
        if (importForm) {
            importForm.addEventListener('submit', function(e) {
                const fileInput = document.getElementById('excel_file');
                
                // Validate file selection
                if (fileInput && fileInput.files.length === 0) {
                    e.preventDefault();
                    alert('Please select a file to import');
                    return;
                }
                
                // Show importing message
                statusContainer.style.display = 'block';
                statusContainer.innerHTML = '<strong>Importing...</strong> Please wait while your file is processed.';
                submitBtn.disabled = true;
                
                // Show progress bar (actual progress happens on the server)
                progressContainer.style.display = 'flex';
                progressBar.style.width = '100%';
                progressBar.innerHTML = 'Processing...';
                
                // Allow the form to submit normally - no need to prevent default
                // The controller will process the upload and redirect
            });
        }
    });
</script>
@endsection
