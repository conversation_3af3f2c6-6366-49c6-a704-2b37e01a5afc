<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class NotificationRecipient extends Model
{
    use HasFactory;
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'notification_id',
        'recipient_id',
        'recipient_type',
        'read_at',
    ];
    
    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'read_at' => 'datetime',
    ];
    
    /**
     * Get the notification that owns the recipient.
     */
    public function notification()
    {
        return $this->belongsTo(Notification::class);
    }
    
    /**
     * Get the recipient model (student or employee).
     */
    public function recipient()
    {
        if ($this->recipient_type === 'student') {
            return $this->belongsTo(Student::class, 'recipient_id');
        } elseif ($this->recipient_type === 'employee') {
            return $this->belongsTo(Employee::class, 'recipient_id');
        }
        
        return null;
    }
}
