<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Creating Test Attachment Files ===\n\n";

// Create storage/app/public/notifications directory if it doesn't exist
$notificationsDir = storage_path('app/public/notifications');
if (!file_exists($notificationsDir)) {
    mkdir($notificationsDir, 0755, true);
    echo "✅ Created notifications directory: {$notificationsDir}\n";
} else {
    echo "✅ Notifications directory already exists: {$notificationsDir}\n";
}

// Create test PDF file
$pdfPath = $notificationsDir . '/test_document.pdf';
$pdfContent = "%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
100 700 Td
(Test Document) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
300
%%EOF";

file_put_contents($pdfPath, $pdfContent);
echo "✅ Created test PDF: {$pdfPath}\n";

// Create test DOCX file (simplified)
$docxPath = $notificationsDir . '/sample_file.docx';
$docxContent = "This is a test document file for attachment testing.
It contains sample content to verify that file downloads work correctly.

Test Document Content:
- Line 1: Sample text
- Line 2: More sample text
- Line 3: Final test line

End of document.";

file_put_contents($docxPath, $docxContent);
echo "✅ Created test DOCX: {$docxPath}\n";

// Create symbolic link if it doesn't exist
$publicStorageLink = public_path('storage');
if (!file_exists($publicStorageLink)) {
    // Try to create symbolic link
    if (function_exists('symlink')) {
        $target = storage_path('app/public');
        if (symlink($target, $publicStorageLink)) {
            echo "✅ Created storage symbolic link\n";
        } else {
            echo "⚠️  Could not create symbolic link. Run: php artisan storage:link\n";
        }
    } else {
        echo "⚠️  Symlink function not available. Run: php artisan storage:link\n";
    }
} else {
    echo "✅ Storage symbolic link already exists\n";
}

// Verify files are accessible
$publicPdfPath = public_path('storage/notifications/test_document.pdf');
$publicDocxPath = public_path('storage/notifications/sample_file.docx');

echo "\n=== File Accessibility Check ===\n";
echo "PDF accessible at public path: " . (file_exists($publicPdfPath) ? "YES" : "NO") . "\n";
echo "DOCX accessible at public path: " . (file_exists($publicDocxPath) ? "YES" : "NO") . "\n";

echo "\n=== URLs for Testing ===\n";
echo "PDF URL: " . url('storage/notifications/test_document.pdf') . "\n";
echo "DOCX URL: " . url('storage/notifications/sample_file.docx') . "\n";

echo "\n✅ Test files created successfully!\n";
echo "You can now test the notification with ID 26 in the web interface.\n";
