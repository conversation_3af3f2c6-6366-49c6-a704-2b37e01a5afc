<?php

require_once 'vendor/autoload.php';

use Minishlink\WebPush\VAPID;

echo "🔑 Generating VAPID Keys for Push Notifications...\n\n";

try {
    // Generate VAPID keys
    $keys = VAPID::createVapidKeys();
    
    $publicKey = $keys['publicKey'];
    $privateKey = $keys['privateKey'];
    
    echo "✅ VAPID Keys Generated Successfully!\n\n";
    echo "📋 Add these to your .env file:\n";
    echo str_repeat("=", 50) . "\n";
    echo "VAPID_PUBLIC_KEY={$publicKey}\n";
    echo "VAPID_PRIVATE_KEY={$privateKey}\n";
    echo "VAPID_SUBJECT=" . (getenv('APP_URL') ?: 'http://localhost:8000') . "\n";
    echo str_repeat("=", 50) . "\n\n";
    
    echo "📝 Public Key (for client-side):\n";
    echo $publicKey . "\n\n";
    
    echo "🔐 Private Key (keep secret!):\n";
    echo $privateKey . "\n\n";
    
    echo "⚠️  Important Notes:\n";
    echo "1. Keep the private key secret and secure\n";
    echo "2. Add these to your .env file\n";
    echo "3. Restart your Laravel application after adding the keys\n";
    echo "4. The public key will be used in your JavaScript code\n\n";
    
    // Try to update .env file automatically
    $envPath = '.env';
    if (file_exists($envPath)) {
        echo "🔄 Attempting to update .env file...\n";
        
        $envContent = file_get_contents($envPath);
        
        // Check if VAPID keys already exist
        if (strpos($envContent, 'VAPID_PUBLIC_KEY=') !== false) {
            // Update existing keys
            $envContent = preg_replace('/VAPID_PUBLIC_KEY=.*/', 'VAPID_PUBLIC_KEY=' . $publicKey, $envContent);
            $envContent = preg_replace('/VAPID_PRIVATE_KEY=.*/', 'VAPID_PRIVATE_KEY=' . $privateKey, $envContent);
            
            // Add VAPID_SUBJECT if it doesn't exist
            if (strpos($envContent, 'VAPID_SUBJECT=') === false) {
                $envContent .= "\nVAPID_SUBJECT=" . (getenv('APP_URL') ?: 'http://localhost:8000');
            }
        } else {
            // Add new keys
            $envContent .= "\n# VAPID Keys for Web Push Notifications\n";
            $envContent .= "VAPID_PUBLIC_KEY=" . $publicKey . "\n";
            $envContent .= "VAPID_PRIVATE_KEY=" . $privateKey . "\n";
            $envContent .= "VAPID_SUBJECT=" . (getenv('APP_URL') ?: 'http://localhost:8000') . "\n";
        }
        
        if (file_put_contents($envPath, $envContent)) {
            echo "✅ .env file updated successfully!\n";
        } else {
            echo "❌ Failed to update .env file. Please add the keys manually.\n";
        }
    } else {
        echo "⚠️  .env file not found. Please create it and add the keys manually.\n";
    }
    
    echo "\n🎉 VAPID keys generation completed!\n";
    
} catch (Exception $e) {
    echo "❌ Error generating VAPID keys: " . $e->getMessage() . "\n";
    echo "\n🔧 Troubleshooting:\n";
    echo "1. Make sure OpenSSL is installed and enabled\n";
    echo "2. Check PHP extensions: openssl, sodium\n";
    echo "3. Try running: php -m | grep -E '(openssl|sodium)'\n";
    exit(1);
}
?>
