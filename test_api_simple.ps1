# Test API Notifications with Attachments
$baseUrl = "http://localhost/appnote-api/public/api"

Write-Host "=== Testing API Notifications with Attachments ===" -ForegroundColor Green

# Get admin token first
Write-Host "`n1. Admin Login..." -ForegroundColor Yellow
try {
    $loginData = @{
        username = "admin123"
        password = "admin123"
    } | ConvertTo-Json

    $loginResponse = Invoke-WebRequest -Uri "$baseUrl/auth/admin/login" -Method POST -ContentType "application/json" -Body $loginData
    $loginResult = $loginResponse.Content | ConvertFrom-Json

    if ($loginResult.success) {
        $token = $loginResult.data.token
        Write-Host "✅ Login successful" -ForegroundColor Green
    } else {
        Write-Host "❌ Login failed: $($loginResult.message)" -ForegroundColor Red
        exit
    }
} catch {
    Write-Host "❌ Login error: $($_.Exception.Message)" -ForegroundColor Red
    exit
}

# Test 1: Send notification with direct paths
Write-Host "`n2. Testing notification with direct paths..." -ForegroundColor Yellow
try {
    $notificationData = @{
        title = "Test Notification with Attachments"
        message = "This is a test notification with attachments from PowerShell"
        sender_id = 1
        sender_name = "PowerShell Test"
        sender_type = "admin"
        recipient_type = "students"
        recipient_ids = @(1, 2, 3)
        priority = "high"
        attachment_paths = @("notifications/test_document.pdf", "notifications/sample_file.docx")
        attachment_names = @("Test Document.pdf", "Sample File.docx")
    } | ConvertTo-Json -Depth 3

    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }

    $response = Invoke-WebRequest -Uri "$baseUrl/notifications" -Method POST -Headers $headers -Body $notificationData
    $result = $response.Content | ConvertFrom-Json

    if ($result.success) {
        Write-Host "✅ Notification sent successfully (ID: $($result.data.id))" -ForegroundColor Green
        Write-Host "   Attachments: $($result.data.attachment_path)" -ForegroundColor Gray
    } else {
        Write-Host "❌ Failed to send notification: $($result.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Error sending notification: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode
        Write-Host "   Status Code: $statusCode" -ForegroundColor Red
    }
}

Write-Host "`n=== Test Complete ===" -ForegroundColor Green
Write-Host "✅ Attachment API tested!" -ForegroundColor Green
