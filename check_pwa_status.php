<?php
/**
 * PWA Status Checker for AppNote
 * This script checks if all PWA components are properly configured
 */

echo "🔍 Checking PWA Configuration for AppNote...\n\n";

$errors = [];
$warnings = [];
$success = [];

// Check 1: Manifest file exists and is valid
echo "1. Checking manifest.json...\n";
$manifestPath = __DIR__ . '/public/manifest.json';
if (file_exists($manifestPath)) {
    $manifestContent = file_get_contents($manifestPath);
    $manifest = json_decode($manifestContent, true);
    
    if ($manifest) {
        $success[] = "✅ manifest.json exists and is valid JSON";
        
        // Check required fields
        $requiredFields = ['name', 'short_name', 'start_url', 'display', 'icons'];
        foreach ($requiredFields as $field) {
            if (isset($manifest[$field])) {
                $success[] = "✅ manifest.json has required field: $field";
            } else {
                $errors[] = "❌ manifest.json missing required field: $field";
            }
        }
        
        // Check icons
        if (isset($manifest['icons']) && is_array($manifest['icons'])) {
            $iconSizes = [];
            foreach ($manifest['icons'] as $icon) {
                if (isset($icon['sizes'])) {
                    $iconSizes[] = $icon['sizes'];
                    
                    // Check if icon file exists
                    $iconPath = __DIR__ . '/public' . $icon['src'];
                    if (file_exists($iconPath)) {
                        $success[] = "✅ Icon exists: {$icon['src']} ({$icon['sizes']})";
                    } else {
                        $errors[] = "❌ Icon missing: {$icon['src']} ({$icon['sizes']})";
                    }
                }
            }
            
            // Check for required icon sizes
            $requiredSizes = ['192x192', '512x512'];
            foreach ($requiredSizes as $size) {
                if (in_array($size, $iconSizes)) {
                    $success[] = "✅ Required icon size present: $size";
                } else {
                    $errors[] = "❌ Missing required icon size: $size";
                }
            }
        }
    } else {
        $errors[] = "❌ manifest.json exists but contains invalid JSON";
    }
} else {
    $errors[] = "❌ manifest.json not found";
}

// Check 2: Service Worker file
echo "\n2. Checking service worker...\n";
$swPath = __DIR__ . '/public/sw.js';
if (file_exists($swPath)) {
    $success[] = "✅ sw.js exists";
    
    $swContent = file_get_contents($swPath);
    
    // Check for essential service worker features
    $requiredFeatures = [
        'install' => "addEventListener('install'",
        'activate' => "addEventListener('activate'",
        'fetch' => "addEventListener('fetch'",
        'caches' => 'caches.open'
    ];
    
    foreach ($requiredFeatures as $feature => $pattern) {
        if (strpos($swContent, $pattern) !== false) {
            $success[] = "✅ Service worker has $feature event handler";
        } else {
            $warnings[] = "⚠️ Service worker missing $feature event handler";
        }
    }
} else {
    $errors[] = "❌ sw.js not found";
}

// Check 3: Offline page
echo "\n3. Checking offline page...\n";
$offlinePath = __DIR__ . '/public/offline.html';
if (file_exists($offlinePath)) {
    $success[] = "✅ offline.html exists";
} else {
    $warnings[] = "⚠️ offline.html not found (recommended for better offline experience)";
}

// Check 4: Layout integration
echo "\n4. Checking layout integration...\n";
$layoutPath = __DIR__ . '/resources/views/layouts/app.blade.php';
if (file_exists($layoutPath)) {
    $layoutContent = file_get_contents($layoutPath);
    
    // Check for PWA meta tags
    $pwaFeatures = [
        'manifest link' => 'rel="manifest"',
        'theme-color meta' => 'name="theme-color"',
        'apple-mobile-web-app-capable' => 'name="apple-mobile-web-app-capable"',
        'service worker registration' => 'serviceWorker.register'
    ];
    
    foreach ($pwaFeatures as $feature => $pattern) {
        if (strpos($layoutContent, $pattern) !== false) {
            $success[] = "✅ Layout has $feature";
        } else {
            $errors[] = "❌ Layout missing $feature";
        }
    }
} else {
    $errors[] = "❌ Layout file not found";
}

// Check 5: Icon files
echo "\n5. Checking icon files...\n";
$iconDir = __DIR__ . '/public/icons/';
if (is_dir($iconDir)) {
    $requiredIcons = [
        'icon-72x72.png',
        'icon-96x96.png',
        'icon-128x128.png',
        'icon-144x144.png',
        'icon-152x152.png',
        'icon-192x192.png',
        'icon-384x384.png',
        'icon-512x512.png'
    ];
    
    foreach ($requiredIcons as $icon) {
        if (file_exists($iconDir . $icon)) {
            $success[] = "✅ Icon exists: $icon";
        } else {
            $errors[] = "❌ Icon missing: $icon";
        }
    }
} else {
    $errors[] = "❌ Icons directory not found";
}

// Check 6: HTTPS requirement
echo "\n6. Checking HTTPS configuration...\n";
if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
    $success[] = "✅ HTTPS is enabled";
} else {
    $warnings[] = "⚠️ HTTPS not detected - PWA requires HTTPS in production";
}

// Summary
echo "\n" . str_repeat("=", 50) . "\n";
echo "PWA CONFIGURATION SUMMARY\n";
echo str_repeat("=", 50) . "\n\n";

if (!empty($success)) {
    echo "✅ SUCCESS (" . count($success) . " items):\n";
    foreach ($success as $item) {
        echo "   $item\n";
    }
    echo "\n";
}

if (!empty($warnings)) {
    echo "⚠️ WARNINGS (" . count($warnings) . " items):\n";
    foreach ($warnings as $item) {
        echo "   $item\n";
    }
    echo "\n";
}

if (!empty($errors)) {
    echo "❌ ERRORS (" . count($errors) . " items):\n";
    foreach ($errors as $item) {
        echo "   $item\n";
    }
    echo "\n";
}

// Overall status
$totalIssues = count($errors);
$totalWarnings = count($warnings);

if ($totalIssues === 0) {
    if ($totalWarnings === 0) {
        echo "🎉 PWA CONFIGURATION: EXCELLENT\n";
        echo "Your PWA is fully configured and ready for production!\n";
    } else {
        echo "✅ PWA CONFIGURATION: GOOD\n";
        echo "Your PWA is working but has some minor recommendations.\n";
    }
} else {
    echo "⚠️ PWA CONFIGURATION: NEEDS ATTENTION\n";
    echo "Please fix the errors above before deploying to production.\n";
}

echo "\n📋 Next Steps:\n";
echo "1. Test your PWA at: " . (isset($_SERVER['HTTP_HOST']) ? "http://{$_SERVER['HTTP_HOST']}/pwa-test" : "your-domain.com/pwa-test") . "\n";
echo "2. Use Chrome DevTools > Application > Manifest to verify PWA\n";
echo "3. Test offline functionality in DevTools > Network > Offline\n";
echo "4. Deploy with HTTPS for full PWA functionality\n";
echo "\n";
?>
