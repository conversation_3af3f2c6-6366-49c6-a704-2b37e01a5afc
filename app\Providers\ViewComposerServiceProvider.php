<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\View;
use App\Models\NotificationRecipient;

class ViewComposerServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Share notification stats with all views
        View::composer('*', function ($view) {
            if (Auth::guard('student')->check()) {
                $student = Auth::guard('student')->user();
                
                $totalForStudent = NotificationRecipient::where('recipient_id', $student->id)
                    ->where('recipient_type', 'student')
                    ->count();
                    
                $unreadCount = NotificationRecipient::where('recipient_id', $student->id)
                    ->where('recipient_type', 'student')
                    ->whereNull('read_at')
                    ->count();
                
                $stats = [
                    'notifications' => [
                        'total_for_student' => $totalForStudent,
                        'unread' => $unreadCount,
                    ],
                ];
                
                $view->with('stats', $stats);
            }
        });
    }
}
