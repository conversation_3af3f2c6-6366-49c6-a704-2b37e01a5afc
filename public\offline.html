<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>غير متصل - معهد النبطية الفني</title>
    <style>
        :root {
            --parchment: #f4ecdc;
            --costa-del-sol: #5d6e35;
            --locust: #abae88;
            --gurkha: #949c74;
            --thistle-green: #d0cdb0;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--parchment) 0%, var(--thistle-green) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
            text-align: center;
        }
        
        .offline-container {
            background: white;
            border-radius: 20px;
            padding: 3rem 2rem;
            box-shadow: 0 20px 40px rgba(93, 110, 53, 0.1);
            max-width: 500px;
            width: 90%;
            margin: 1rem;
        }
        
        .offline-icon {
            font-size: 4rem;
            color: var(--costa-del-sol);
            margin-bottom: 1.5rem;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .offline-title {
            color: var(--costa-del-sol);
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        
        .offline-message {
            color: var(--gurkha);
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 2rem;
        }
        
        .retry-button {
            background: var(--costa-del-sol);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0.5rem;
        }
        
        .retry-button:hover {
            background: var(--gurkha);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(93, 110, 53, 0.3);
        }
        
        .offline-tips {
            background: var(--thistle-green);
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 2rem;
            text-align: right;
        }
        
        .offline-tips h3 {
            color: var(--costa-del-sol);
            font-size: 1.2rem;
            margin-bottom: 1rem;
        }
        
        .offline-tips ul {
            list-style: none;
            color: var(--gurkha);
        }
        
        .offline-tips li {
            margin-bottom: 0.5rem;
            padding-right: 1.5rem;
            position: relative;
        }
        
        .offline-tips li::before {
            content: "•";
            color: var(--costa-del-sol);
            font-weight: bold;
            position: absolute;
            right: 0;
        }
        
        .connection-status {
            margin-top: 1rem;
            padding: 0.5rem;
            border-radius: 10px;
            font-weight: 600;
        }
        
        .status-offline {
            background: #ffebee;
            color: #c62828;
        }
        
        .status-online {
            background: #e8f5e8;
            color: #2e7d32;
        }
        
        @media (max-width: 768px) {
            .offline-container {
                padding: 2rem 1.5rem;
            }
            
            .offline-title {
                font-size: 1.5rem;
            }
            
            .offline-message {
                font-size: 1rem;
            }
            
            .retry-button {
                padding: 0.8rem 1.5rem;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">📡</div>
        <h1 class="offline-title">غير متصل بالإنترنت</h1>
        <p class="offline-message">
            عذراً، يبدو أنك غير متصل بالإنترنت حالياً. يرجى التحقق من اتصالك والمحاولة مرة أخرى.
        </p>
        
        <button class="retry-button" onclick="location.reload()">
            🔄 إعادة المحاولة
        </button>
        
        <button class="retry-button" onclick="goHome()">
            🏠 العودة للرئيسية
        </button>
        
        <div class="connection-status status-offline" id="connectionStatus">
            ❌ غير متصل
        </div>
        
        <div class="offline-tips">
            <h3>💡 نصائح للاتصال:</h3>
            <ul>
                <li>تحقق من اتصال الواي فاي أو البيانات</li>
                <li>تأكد من أن الإنترنت يعمل على التطبيقات الأخرى</li>
                <li>جرب إعادة تشغيل المتصفح</li>
                <li>تحقق من إعدادات الشبكة</li>
            </ul>
        </div>
    </div>

    <script>
        // Check connection status
        function updateConnectionStatus() {
            const statusElement = document.getElementById('connectionStatus');
            if (navigator.onLine) {
                statusElement.textContent = '✅ متصل';
                statusElement.className = 'connection-status status-online';
            } else {
                statusElement.textContent = '❌ غير متصل';
                statusElement.className = 'connection-status status-offline';
            }
        }

        // Listen for connection changes
        window.addEventListener('online', () => {
            updateConnectionStatus();
            setTimeout(() => {
                location.reload();
            }, 1000);
        });

        window.addEventListener('offline', updateConnectionStatus);

        // Go to home page
        function goHome() {
            window.location.href = '/';
        }

        // Initial status check
        updateConnectionStatus();

        // Auto-retry when back online
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.ready.then(() => {
                console.log('Service Worker is ready');
            });
        }
    </script>
</body>
</html>
