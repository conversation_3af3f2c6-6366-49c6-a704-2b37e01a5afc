<?php
// This script updates ONLY student login redirects in the AuthController.php

$file = __DIR__ . '/app/Http/Controllers/AuthController.php';
$content = file_get_contents($file);

// Define the patterns to identify student login sections and replace the redirects
$pattern1 = '/(\s+\/\/ Use the specific student guard\s+Auth::guard\(\'student\'\)->login\(\$student, \$request->filled\(\'remember\'\)\);\s+\$request->session\(\)->regenerate\(\);\s+)return redirect\(\)->route\(\'.*?\'\);/';
$replacement1 = '$1return redirect()->route(\'student.dashboard\');';

// Apply the replacements
$updatedContent = preg_replace($pattern1, $replacement1, $content);

// Write the updated content back to the file
file_put_contents($file, $updatedContent);

// Update admin login to ensure it correctly goes to admin dashboard
$pattern2 = '/(\s+Auth::guard\(\'web\'\)->login\(\$admin, \$request->filled\(\'remember\'\)\);\s+\$request->session\(\)->regenerate\(\);\s+)return redirect\(\)->route\(\'.*?\'\);/';
$replacement2 = '$1return redirect()->route(\'dashboard\');';

$content = file_get_contents($file);
$updatedContent = preg_replace($pattern2, $replacement2, $content);
file_put_contents($file, $updatedContent);

echo "Precisely updated AuthController.php with correct redirects for both student and admin logins.\n";
