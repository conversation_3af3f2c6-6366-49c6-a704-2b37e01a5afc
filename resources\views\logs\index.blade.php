@extends('layouts.app')

@section('content')
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4 mt-3">
        <div>
            <h2 class="mb-1" style="color: var(--costa-del-sol); font-weight: 700;">System Logs</h2>
            <p class="mb-0" style="color: var(--gurkha);">View and manage application log files</p>
        </div>
        <div>
            <a href="{{ route('dashboard') }}" class="btn btn-sm btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
            </a>
        </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <div class="card modern-card">
        <div class="card-header bg-white d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-clipboard-list me-2"></i> Log Files
            </div>
            <div>
                <a href="{{ route('logs.index') }}" class="btn btn-sm btn-outline-secondary">
                    <i class="fas fa-sync-alt me-1"></i> Refresh
                </a>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th class="ps-3">Log File</th>
                            <th>Size</th>
                            <th>Last Modified</th>
                            <th class="text-end pe-3">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($logFiles as $log)
                        <tr>
                            <td class="ps-3">
                                <i class="fas fa-file-alt me-2" style="color: var(--costa-del-sol);"></i> {{ $log['name'] }}
                            </td>
                            <td>{{ $log['size'] }}</td>
                            <td>{{ $log['modified'] }}</td>
                            <td class="text-end pe-3">
                                <a href="{{ route('logs.show', $log['name']) }}" class="btn btn-sm btn-info me-1" title="View">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <form action="{{ route('logs.destroy', $log['name']) }}" method="POST" class="d-inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-sm btn-danger" title="Delete" 
                                        onclick="return confirm('Are you sure you want to delete this log file? This action cannot be undone.')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="4" class="text-center py-4">
                                <div class="empty-state">
                                    <i class="fas fa-clipboard-list fa-2x mb-3 text-muted"></i>
                                    <p>No log files found</p>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@endsection
