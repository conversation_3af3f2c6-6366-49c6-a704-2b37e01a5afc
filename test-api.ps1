# AppNote API Testing Script
# PowerShell script to test API endpoints

$baseUrl = "http://localhost/appnote-api/public/api"

Write-Host "=== AppNote API Testing ===" -ForegroundColor Green

# Test 1: Health Check
Write-Host "`n1. Testing Health Check..." -ForegroundColor Yellow
try {
    $healthResponse = Invoke-WebRequest -Uri "$baseUrl/health" -Method GET -ContentType "application/json"
    $healthData = $healthResponse.Content | ConvertFrom-Json
    Write-Host "✅ Health Check: " -ForegroundColor Green -NoNewline
    Write-Host $healthData.message
} catch {
    Write-Host "❌ Health Check Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Admin Login with username
Write-Host "`n2. Testing Admin Login with username..." -ForegroundColor Yellow
try {
    $loginBody = @{
        username = "admin123"
        password = "admin123"
    } | ConvertTo-Json

    $loginResponse = Invoke-WebRequest -Uri "$baseUrl/auth/admin/login" -Method POST -ContentType "application/json" -Body $loginBody
    $loginData = $loginResponse.Content | ConvertFrom-Json

    if ($loginData.success) {
        Write-Host "✅ Admin Login (username): " -ForegroundColor Green -NoNewline
        Write-Host $loginData.message
        Write-Host "   User: $($loginData.data.user.name) ($($loginData.data.user.email))"
        Write-Host "   Token: $($loginData.data.token.Substring(0,50))..."

        # Store token for further tests
        $global:authToken = $loginData.data.token
    } else {
        Write-Host "❌ Admin Login (username) Failed: $($loginData.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Admin Login (username) Failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "   Response: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
}

# Test 2b: Admin Login with name
Write-Host "`n2b. Testing Admin Login with name..." -ForegroundColor Yellow
try {
    $loginBody = @{
        username = "admin123"
        password = "admin123"
    } | ConvertTo-Json

    $loginResponse = Invoke-WebRequest -Uri "$baseUrl/auth/admin/login" -Method POST -ContentType "application/json" -Body $loginBody
    $loginData = $loginResponse.Content | ConvertFrom-Json

    if ($loginData.success) {
        Write-Host "✅ Admin Login (name): " -ForegroundColor Green -NoNewline
        Write-Host $loginData.message
        Write-Host "   User: $($loginData.data.user.name) ($($loginData.data.user.email))"
    } else {
        Write-Host "❌ Admin Login (name) Failed: $($loginData.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Admin Login (name) Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2c: Admin Login with email
Write-Host "`n2c. Testing Admin Login with email..." -ForegroundColor Yellow
try {
    $loginBody = @{
        username = "<EMAIL>"
        password = "admin123"
    } | ConvertTo-Json

    $loginResponse = Invoke-WebRequest -Uri "$baseUrl/auth/admin/login" -Method POST -ContentType "application/json" -Body $loginBody
    $loginData = $loginResponse.Content | ConvertFrom-Json

    if ($loginData.success) {
        Write-Host "✅ Admin Login (email): " -ForegroundColor Green -NoNewline
        Write-Host $loginData.message
        Write-Host "   User: $($loginData.data.user.name) ($($loginData.data.user.email))"
    } else {
        Write-Host "❌ Admin Login (email) Failed: $($loginData.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Admin Login (email) Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Test Database Connection
Write-Host "`n3. Testing Database Connection..." -ForegroundColor Yellow
try {
    $dbResponse = Invoke-WebRequest -Uri "$baseUrl/test-db" -Method GET -ContentType "application/json"
    $dbData = $dbResponse.Content | ConvertFrom-Json
    Write-Host "✅ Database: " -ForegroundColor Green -NoNewline
    Write-Host $dbData.message
} catch {
    Write-Host "❌ Database Test Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: Test Protected Endpoint (if we have a token)
if ($global:authToken) {
    Write-Host "`n4. Testing Protected Endpoint (Students List)..." -ForegroundColor Yellow
    try {
        $headers = @{
            "Authorization" = "Bearer $global:authToken"
            "Content-Type" = "application/json"
        }
        
        $studentsResponse = Invoke-WebRequest -Uri "$baseUrl/students" -Method GET -Headers $headers
        $studentsData = $studentsResponse.Content | ConvertFrom-Json
        Write-Host "✅ Students Endpoint: " -ForegroundColor Green -NoNewline
        Write-Host "Retrieved $($studentsData.data.Count) students"
    } catch {
        Write-Host "❌ Students Endpoint Failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test 5: API Documentation
Write-Host "`n5. Testing API Documentation..." -ForegroundColor Yellow
try {
    $docsResponse = Invoke-WebRequest -Uri "$baseUrl/docs" -Method GET -ContentType "application/json"
    $docsData = $docsResponse.Content | ConvertFrom-Json
    Write-Host "✅ API Docs: " -ForegroundColor Green -NoNewline
    Write-Host $docsData.message
} catch {
    Write-Host "❌ API Docs Failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== Testing Complete ===" -ForegroundColor Green
