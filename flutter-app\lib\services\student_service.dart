import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';
import '../models/notification.dart';
import '../utils/constants.dart';
import 'auth_service.dart';

class StudentService {
  static const String _baseUrl = ApiConstants.students;

  static final Map<String, String> _headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  // Get authorization headers with token
  static Future<Map<String, String>> _getAuthHeaders() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString(
      StorageKeys.userToken,
    ); // Use correct storage key

    final headers = Map<String, String>.from(_headers);
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
      if (kDebugMode) {
        print('🔑 Using token for students API: ${token.substring(0, 20)}...');
      }
    } else {
      if (kDebugMode) {
        print('❌ No token found for students API');
      }
    }

    return headers;
  }

  // Get students with pagination and search
  static Future<Map<String, dynamic>> getStudents({
    int page = 1,
    int perPage = 20,
    String? search,
    String? orderBy = 'full_name',
    String? orderDirection = 'asc',
  }) async {
    try {
      final headers = await _getAuthHeaders();

      // Build query parameters
      final queryParams = <String, String>{
        'page': page.toString(),
        'per_page': perPage.toString(),
        'order_by': orderBy ?? 'full_name',
        'order_direction': orderDirection ?? 'asc',
      };

      if (search != null && search.isNotEmpty) {
        queryParams['search'] = search;
      }

      final uri = Uri.parse(_baseUrl).replace(queryParameters: queryParams);
      final response = await http.get(uri, headers: headers);

      if (kDebugMode) {
        print(
          'Get students response: ${response.statusCode} - ${response.body}',
        );
      }

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        // Handle Laravel API response (all students in one response)
        if (data is Map && data.containsKey('data')) {
          final studentsJson = data['data'] as List<dynamic>;
          final allStudents = studentsJson
              .map((json) => Student.fromJson(json))
              .toList();

          // Apply search filter
          List<Student> filteredStudents = allStudents;
          if (search != null && search.isNotEmpty) {
            filteredStudents = allStudents.where((student) {
              return student.fullName.toLowerCase().contains(
                    search.toLowerCase(),
                  ) ||
                  student.username.toLowerCase().contains(
                    search.toLowerCase(),
                  ) ||
                  student.specialization.toLowerCase().contains(
                    search.toLowerCase(),
                  ) ||
                  student.studentClass.toLowerCase().contains(
                    search.toLowerCase(),
                  ) ||
                  student.section.toLowerCase().contains(search.toLowerCase());
            }).toList();
          }

          // Apply pagination manually
          int totalStudents = filteredStudents.length;
          int totalPages = (totalStudents / perPage).ceil();
          int startIndex = (page - 1) * perPage;
          int endIndex = (startIndex + perPage).clamp(0, totalStudents);

          List<Student> pageStudents = filteredStudents.sublist(
            startIndex.clamp(0, totalStudents),
            endIndex,
          );

          if (kDebugMode) {
            print('📊 Total students: $totalStudents');
            print('📄 Page $page of $totalPages');
            print('👥 Showing ${pageStudents.length} students');
          }

          return {
            'students': pageStudents,
            'total': totalStudents,
            'current_page': page,
            'last_page': totalPages,
            'per_page': perPage,
            'from': startIndex + 1,
            'to': endIndex,
          };
        } else if (data is List) {
          // Fallback for simple array response
          final students = data.map((json) => Student.fromJson(json)).toList();
          return {
            'students': students,
            'total': students.length,
            'current_page': 1,
            'last_page': 1,
            'per_page': students.length,
            'from': 1,
            'to': students.length,
          };
        } else {
          if (kDebugMode) {
            print('Unexpected response structure: $data');
          }
          return {
            'students': <Student>[],
            'total': 0,
            'current_page': 1,
            'last_page': 1,
            'per_page': perPage,
            'from': 0,
            'to': 0,
          };
        }
      } else if (response.statusCode == 401) {
        if (kDebugMode) {
          print('❌ Authentication failed for students API');
          print('🔄 Attempting to refresh authentication...');
        }

        // Try to refresh authentication
        final authService = AuthService();
        final isLoggedIn = authService.isAuthenticated;

        if (!isLoggedIn) {
          throw Exception('Authentication required. Please login again.');
        }

        // If still authenticated but API fails, there might be a token issue
        throw Exception('Authentication token invalid. Please login again.');
      } else {
        if (kDebugMode) {
          print(
            'Failed to get students: ${response.statusCode} - ${response.body}',
          );
        }

        throw Exception('Failed to load students: HTTP ${response.statusCode}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Get students error: $e');
        print('🔧 Please ensure Laravel API is running and admin is logged in');
      }

      // Don't return demo data - force real API usage
      throw Exception(
        'Failed to connect to Laravel API. Please check your connection and login status.',
      );
    }
  }

  // Removed demo data - now using only real API data

  // Demo helper functions removed - using real API data only

  // Get all students (backward compatibility)
  static Future<List<Student>> getAllStudents() async {
    final result = await getStudents(
      perPage: 50,
    ); // Limit to 50 for performance
    return result['students'] as List<Student>;
  }

  // Get student by ID
  static Future<Student?> getStudentById(int id) async {
    try {
      final headers = await _getAuthHeaders();
      final response = await http.get(
        Uri.parse('$_baseUrl/$id'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        // Handle different response structures
        Map<String, dynamic> studentJson;
        if (data is Map && data.containsKey('data')) {
          studentJson = data['data'];
        } else if (data is Map && data.containsKey('student')) {
          studentJson = data['student'];
        } else {
          studentJson = data;
        }

        return Student.fromJson(studentJson);
      } else {
        if (kDebugMode) {
          print(
            'Failed to get student: ${response.statusCode} - ${response.body}',
          );
        }
        return null;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Get student error: $e');
      }
      return null;
    }
  }

  // Create new student
  static Future<Student?> createStudent(
    Map<String, dynamic> studentData,
  ) async {
    try {
      final headers = await _getAuthHeaders();
      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: headers,
        body: jsonEncode(studentData),
      );

      if (kDebugMode) {
        print(
          'Create student response: ${response.statusCode} - ${response.body}',
        );
      }

      if (response.statusCode == 201 || response.statusCode == 200) {
        final data = jsonDecode(response.body);

        // Handle different response structures
        Map<String, dynamic> studentJson;
        if (data is Map && data.containsKey('data')) {
          studentJson = data['data'];
        } else if (data is Map && data.containsKey('student')) {
          studentJson = data['student'];
        } else {
          studentJson = data;
        }

        return Student.fromJson(studentJson);
      } else {
        if (kDebugMode) {
          print(
            'Failed to create student: ${response.statusCode} - ${response.body}',
          );
        }
        return null;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Create student error: $e');
      }
      return null;
    }
  }

  // Update student
  static Future<Student?> updateStudent(
    int id,
    Map<String, dynamic> studentData,
  ) async {
    try {
      final headers = await _getAuthHeaders();
      final response = await http.put(
        Uri.parse('$_baseUrl/$id'),
        headers: headers,
        body: jsonEncode(studentData),
      );

      if (kDebugMode) {
        print(
          'Update student response: ${response.statusCode} - ${response.body}',
        );
      }

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        // Handle different response structures
        Map<String, dynamic> studentJson;
        if (data is Map && data.containsKey('data')) {
          studentJson = data['data'];
        } else if (data is Map && data.containsKey('student')) {
          studentJson = data['student'];
        } else {
          studentJson = data;
        }

        return Student.fromJson(studentJson);
      } else {
        if (kDebugMode) {
          print(
            'Failed to update student: ${response.statusCode} - ${response.body}',
          );
        }
        return null;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Update student error: $e');
      }
      return null;
    }
  }

  // Delete student
  static Future<bool> deleteStudent(int id) async {
    try {
      final headers = await _getAuthHeaders();
      final response = await http.delete(
        Uri.parse('$_baseUrl/$id'),
        headers: headers,
      );

      if (kDebugMode) {
        print(
          'Delete student response: ${response.statusCode} - ${response.body}',
        );
      }

      return response.statusCode == 200 || response.statusCode == 204;
    } catch (e) {
      if (kDebugMode) {
        print('Delete student error: $e');
      }
      return false;
    }
  }

  // Cache for student data to avoid multiple API calls
  static List<Student>? _cachedStudents;
  static DateTime? _cacheTime;
  static const Duration _cacheExpiry = Duration(minutes: 5);

  // Get cached students or fetch new ones
  static Future<List<Student>> _getCachedStudents() async {
    final now = DateTime.now();

    // Check if cache is valid
    if (_cachedStudents != null &&
        _cacheTime != null &&
        now.difference(_cacheTime!).compareTo(_cacheExpiry) < 0) {
      return _cachedStudents!;
    }

    // Fetch new data
    try {
      final studentsData = await getStudents(perPage: 500); // Reduced from 1000
      final students = studentsData['students'] as List<Student>;

      _cachedStudents = students;
      _cacheTime = now;

      if (kDebugMode) {
        print('🔄 Cached ${students.length} students for form data');
      }

      return students;
    } catch (e) {
      if (kDebugMode) {
        print('Error caching students: $e');
      }
      return _cachedStudents ?? [];
    }
  }

  // Clear cache when needed
  static void clearCache() {
    _cachedStudents = null;
    _cacheTime = null;
  }

  // Get all form data in one call
  static Future<Map<String, dynamic>> getFormData() async {
    try {
      final students = await _getCachedStudents();

      // Extract all unique values
      final specializations =
          students
              .map((student) => student.specialization)
              .where((spec) => spec.isNotEmpty)
              .toSet()
              .toList()
            ..sort();

      final sections =
          students
              .map((student) => student.section)
              .where((section) => section.isNotEmpty)
              .toSet()
              .toList()
            ..sort();

      final classes =
          students
              .map((student) => student.studentClass)
              .where((cls) => cls.isNotEmpty)
              .toSet()
              .toList()
            ..sort();

      final levels =
          students
              .map((student) => student.level)
              .where((level) => level.isNotEmpty)
              .toSet()
              .toList()
            ..sort();

      final results = students
          .map((student) => student.result)
          .where((result) => result != null && result.isNotEmpty)
          .cast<String>()
          .toSet()
          .toList();

      // Add common results if not found
      if (!results.contains('نجح')) results.add('نجح');
      if (!results.contains('راسب')) results.add('راسب');
      if (!results.contains('منقول')) results.add('منقول');
      if (!results.contains('مكمل')) results.add('مكمل');
      if (!results.contains('اكمال')) results.add('اكمال');
      results.sort();

      // Extract nationalities from database
      final nationalities =
          students
              .map((student) => student.nationality)
              .where((nationality) => nationality.isNotEmpty)
              .toSet()
              .toList()
            ..sort();

      // Generate next username
      final numbers = students
          .map((student) => student.username)
          .where((username) => username.startsWith('stu'))
          .map((username) => username.substring(3))
          .where((numberStr) => RegExp(r'^\d+$').hasMatch(numberStr))
          .map((numberStr) => int.tryParse(numberStr))
          .where((number) => number != null)
          .cast<int>()
          .toList();

      int nextNumber = 1;
      if (numbers.isNotEmpty) {
        nextNumber = numbers.reduce((a, b) => a > b ? a : b) + 1;
      }

      final username = 'stu${nextNumber.toString().padLeft(4, '0')}';

      if (kDebugMode) {
        print('📊 Form data extracted:');
        print('   Username: $username');
        print('   Specializations: ${specializations.length}');
        print('   Sections: ${sections.length}');
        print('   Classes: ${classes.length}');
        print('   Levels: ${levels.length}');
        print('   Results: ${results.length}');
        print('   Nationalities: ${nationalities.length}');
      }

      return {
        'username': username,
        'specializations': specializations,
        'sections': sections,
        'classes': classes,
        'levels': levels,
        'results': results,
        'nationalities': nationalities,
      };
    } catch (e) {
      if (kDebugMode) {
        print('Get form data error: $e');
      }

      // Return fallback data
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      return {
        'username': 'stu${timestamp.substring(timestamp.length - 4)}',
        'specializations': <String>[],
        'sections': <String>[],
        'classes': <String>[],
        'levels': <String>[],
        'results': ['نجح', 'راسب', 'منقول', 'مكمل', 'اكمال'],
        'nationalities': ['لبناني', 'سوري', 'فلسطيني', 'مصري', 'أردني'],
      };
    }
  }

  // Legacy methods for backward compatibility (now use cached data)
  static Future<List<String>> getAvailableClasses() async {
    final formData = await getFormData();
    return formData['classes'] as List<String>;
  }

  static Future<List<String>> getAvailableSections() async {
    final formData = await getFormData();
    return formData['sections'] as List<String>;
  }

  static Future<List<String>> getAvailableLevels() async {
    final formData = await getFormData();
    return formData['levels'] as List<String>;
  }

  static Future<List<String>> getAvailableSpecializations() async {
    final formData = await getFormData();
    return formData['specializations'] as List<String>;
  }

  static Future<List<String>> getAvailableResults() async {
    final formData = await getFormData();
    return formData['results'] as List<String>;
  }

  static Future<List<String>> getAvailableNationalities() async {
    final formData = await getFormData();
    return formData['nationalities'] as List<String>;
  }

  static Future<String> generateNextUsername() async {
    final formData = await getFormData();
    return formData['username'] as String;
  }

  // Bulk import students
  static Future<Map<String, dynamic>> importStudents(
    List<Map<String, dynamic>> studentsData,
  ) async {
    try {
      final headers = await _getAuthHeaders();
      final response = await http.post(
        Uri.parse('$_baseUrl/bulk-import'),
        headers: headers,
        body: jsonEncode({'students': studentsData}),
      );

      if (kDebugMode) {
        print(
          'Import students response: ${response.statusCode} - ${response.body}',
        );
      }

      if (response.statusCode == 200 || response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        return {
          'success': false,
          'message': 'فشل في استيراد الطلاب',
          'error': response.body,
        };
      }
    } catch (e) {
      if (kDebugMode) {
        print('Import students error: $e');
      }
      return {
        'success': false,
        'message': 'خطأ في الاتصال',
        'error': e.toString(),
      };
    }
  }

  // ========== STUDENT DASHBOARD METHODS ==========

  // Get student notifications
  static Future<List<AppNotification>> getNotifications() async {
    try {
      final token = await AuthService.getStoredToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      if (kDebugMode) {
        print('📱 Fetching student notifications...');
      }

      final response = await http.get(
        Uri.parse('${ApiConstants.baseUrl}/student/notifications'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      if (kDebugMode) {
        print('📱 Student notifications response: ${response.statusCode}');
        print('📱 Response body: ${response.body}');
      }

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        if (data['success'] == true && data['data'] != null) {
          final List<dynamic> notificationsJson = data['data'];
          return notificationsJson
              .map((json) => AppNotification.fromJson(json))
              .toList();
        } else {
          // If no data, return empty list instead of mock data
          if (kDebugMode) {
            print('📱 No notifications found: ${data['message']}');
          }
          return [];
        }
      } else if (response.statusCode == 401) {
        throw Exception('غير مصرح لك بالوصول. يرجى تسجيل الدخول مرة أخرى.');
      } else {
        throw Exception('خطأ في الخادم: ${response.statusCode}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error fetching student notifications: $e');
      }

      // Re-throw the error instead of returning mock data
      throw Exception('فشل في تحميل الإشعارات: $e');
    }
  }

  // Get student dashboard data
  static Future<Map<String, dynamic>> getDashboardData() async {
    try {
      final token = await AuthService.getStoredToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      if (kDebugMode) {
        print('📱 Fetching student dashboard data...');
      }

      final response = await http.get(
        Uri.parse('${ApiConstants.baseUrl}/student/dashboard'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      if (kDebugMode) {
        print('📱 Student dashboard response: ${response.statusCode}');
      }

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        if (data['success'] == true && data['data'] != null) {
          return data['data'];
        } else {
          // Return empty dashboard data if no data found
          if (kDebugMode) {
            print('📱 No dashboard data found: ${data['message']}');
          }
          return {
            'notifications': {
              'total': 0,
              'unread': 0,
            },
          };
        }
      } else if (response.statusCode == 401) {
        throw Exception('غير مصرح لك بالوصول. يرجى تسجيل الدخول مرة أخرى.');
      } else {
        throw Exception('خطأ في الخادم: ${response.statusCode}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error fetching student dashboard data: $e');
      }

      // Re-throw the error instead of returning mock data
      throw Exception('فشل في تحميل بيانات لوحة التحكم: $e');
    }
  }

  // Mark notification as read
  static Future<void> markNotificationAsRead(int notificationId) async {
    try {
      final token = await AuthService.getStoredToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      if (kDebugMode) {
        print('📱 Marking notification $notificationId as read...');
      }

      final response = await http.post(
        Uri.parse('${ApiConstants.baseUrl}/student/notifications/$notificationId/mark-read'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      if (kDebugMode) {
        print('📱 Mark as read response: ${response.statusCode}');
      }

      if (response.statusCode != 200) {
        final data = jsonDecode(response.body);
        throw Exception(data['message'] ?? 'Failed to mark notification as read');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error marking notification as read: $e');
      }
      // For development, we'll just log the error
      // In production, you might want to throw the error
    }
  }

  // Mark notification as unread
  static Future<void> markNotificationAsUnread(int notificationId) async {
    try {
      final token = await AuthService.getStoredToken();
      if (token == null) {
        throw Exception('No authentication token found');
      }

      if (kDebugMode) {
        print('📱 Marking notification $notificationId as unread...');
      }

      final response = await http.post(
        Uri.parse('${ApiConstants.baseUrl}/student/notifications/$notificationId/mark-unread'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      if (kDebugMode) {
        print('📱 Mark as unread response: ${response.statusCode}');
      }

      if (response.statusCode != 200) {
        final data = jsonDecode(response.body);
        throw Exception(data['message'] ?? 'Failed to mark notification as unread');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error marking notification as unread: $e');
      }
      // For development, we'll just log the error
    }
  }




}
