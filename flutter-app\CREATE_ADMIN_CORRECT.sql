-- إنشاء أدمن في الجدول الصحيح
-- Create Admin in Correct Table

-- ===================================
-- إنشاء أدمن في جدول admins (إذا كان موجود)
-- Create Admin in admins table (if exists)
-- ===================================

-- حذف الأدمن إذا كان موجوداً
DELETE FROM admins WHERE name = 'مدير النظام';

-- إنشاء أدمن جديد في جدول admins
-- Password: admin123 (hashed with bcrypt)
INSERT INTO admins (name, email, password, created_at, updated_at)
VALUES (
  'مدير النظام',
  '<EMAIL>',
  '$2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm',
  NOW(),
  NOW()
);

-- ===================================
-- أو إنشاء أدمن في جدول users (إذا كان موجود)
-- Or Create Admin in users table (if exists)
-- ===================================

-- حذف الأدمن إذا كان موجوداً
-- DELETE FROM users WHERE email = '<EMAIL>';

-- إنشاء أدمن جديد في جدول users
-- Password: admin123 (hashed with bcrypt)
-- INSERT INTO users (name, email, password, role, created_at, updated_at) 
-- VALUES (
--   'مدير النظام',
--   '<EMAIL>',
--   '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
--   'admin',
--   NOW(),
--   NOW()
-- );

-- ===================================
-- التحقق من البيانات
-- Verify Data
-- ===================================

-- عرض الأدمن المُنشأ من جدول admins
SELECT 'Admin Created in admins table:' as info, name, email FROM admins WHERE name = 'مدير النظام';

-- عرض الأدمن المُنشأ من جدول users (إذا كان موجود)
-- SELECT 'Admin Created in users table:' as info, name, email, role FROM users WHERE email = '<EMAIL>';

-- ===================================
-- معلومات تسجيل الدخول
-- Login Information
-- ===================================

/*
معلومات تسجيل الدخول:

🔐 الأدمن:
- اسم المدير: مدير النظام
- كلمة المرور: admin123

📊 ملاحظات:
- تم إنشاء الأدمن في جدول admins
- كلمة المرور مشفرة بـ bcrypt
- يمكن تسجيل الدخول فوراً

🚀 الخطوات التالية:
1. نفذ هذا SQL script في phpMyAdmin
2. افتح التطبيق
3. اضغط "Admin Login"
4. أدخل البيانات أعلاه
5. ستدخل إلى Dashboard مع إدارة 1233 طالب!
*/

-- ===================================
-- إذا كان هناك خطأ في بنية الجدول
-- If there's an error in table structure
-- ===================================

-- تحقق من وجود جدول admins
-- SHOW TABLES LIKE 'admins';

-- تحقق من بنية جدول admins
-- DESCRIBE admins;

-- تحقق من وجود جدول users
-- SHOW TABLES LIKE 'users';

-- تحقق من بنية جدول users
-- DESCRIBE users;

-- ===================================
-- إنشاء جدول admins إذا لم يكن موجود
-- Create admins table if it doesn't exist
-- ===================================

-- CREATE TABLE IF NOT EXISTS admins (
--   id bigint unsigned NOT NULL AUTO_INCREMENT,
--   name varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
--   email varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
--   password varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
--   remember_token varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
--   created_at timestamp NULL DEFAULT NULL,
--   updated_at timestamp NULL DEFAULT NULL,
--   PRIMARY KEY (id),
--   UNIQUE KEY admins_email_unique (email)
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ===================================
-- Hash جديد لكلمة المرور admin123
-- New hash for password admin123
-- ===================================

/*
إذا لم تعمل كلمة المرور، استخدم هذا Hash الجديد:

Password: admin123
Hash: $2y$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm

أو استخدم Laravel Tinker:
php artisan tinker
Hash::make('admin123')
*/
