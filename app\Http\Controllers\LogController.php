<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;

class LogController extends Controller
{
    /**
     * Log files directory
     */
    protected $logPath;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->logPath = storage_path('logs');
    }

    /**
     * Display a listing of log files.
     */
    public function index(): View
    {
        $logFiles = collect(File::files($this->logPath))->map(function ($file) {
            return [
                'name' => $file->getFilename(),
                'size' => $this->formatFileSize($file->getSize()),
                'modified' => date('Y-m-d H:i:s', $file->getMTime()),
            ];
        })->sortByDesc('modified');

        return view('logs.index', compact('logFiles'));
    }

    /**
     * Display the specified log file.
     */
    public function show(string $filename): View
    {
        $filePath = $this->logPath . '/' . $filename;
        
        if (!File::exists($filePath)) {
            abort(404, 'Log file not found');
        }

        $content = File::get($filePath);
        $formattedContent = $this->parseLogContent($content);

        return view('logs.show', [
            'filename' => $filename,
            'content' => $formattedContent,
            'modified' => date('Y-m-d H:i:s', File::lastModified($filePath)),
            'size' => $this->formatFileSize(File::size($filePath)),
        ]);
    }

    /**
     * Remove the specified log file.
     */
    public function destroy(string $filename): RedirectResponse
    {
        $filePath = $this->logPath . '/' . $filename;
        
        if (!File::exists($filePath)) {
            return redirect()->route('logs.index')->with('error', 'Log file not found.');
        }

        try {
            File::delete($filePath);
            return redirect()->route('logs.index')->with('success', "Log file '{$filename}' has been deleted successfully.");
        } catch (\Exception $e) {
            return redirect()->route('logs.index')->with('error', "Failed to delete log file: {$e->getMessage()}");
        }
    }

    /**
     * Format file size to readable format
     */
    protected function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * Parse log content to format for display
     */
    protected function parseLogContent(string $content): array
    {
        $pattern = '/\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\]/';
        $logEntries = preg_split($pattern, $content, -1, PREG_SPLIT_DELIM_CAPTURE | PREG_SPLIT_NO_EMPTY);
        
        $formattedEntries = [];
        $datePattern = '/\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\]/';
        
        foreach ($logEntries as $index => $entry) {
            if (preg_match($datePattern, $entry, $matches)) {
                $date = $matches[1];
                $nextIndex = $index + 1;
                
                if (isset($logEntries[$nextIndex])) {
                    $level = '';
                    if (strpos($logEntries[$nextIndex], '.ERROR') !== false) {
                        $level = 'error';
                    } elseif (strpos($logEntries[$nextIndex], '.WARNING') !== false) {
                        $level = 'warning';
                    } elseif (strpos($logEntries[$nextIndex], '.INFO') !== false) {
                        $level = 'info';
                    } elseif (strpos($logEntries[$nextIndex], '.DEBUG') !== false) {
                        $level = 'debug';
                    }
                    
                    $formattedEntries[] = [
                        'date' => $date,
                        'content' => $logEntries[$nextIndex],
                        'level' => $level
                    ];
                }
            }
        }
        
        return array_reverse($formattedEntries);
    }
}
