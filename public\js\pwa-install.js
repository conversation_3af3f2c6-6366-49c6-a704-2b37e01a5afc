// PWA Installation and Management
class PWAInstaller {
    constructor() {
        this.deferredPrompt = null;
        this.isInstalled = false;
        this.installButton = null;
        this.init();
    }

    init() {
        this.checkInstallation();
        this.registerServiceWorker();
        this.setupInstallPrompt();
        this.createInstallButton();
        this.setupEventListeners();
    }

    // Check if app is already installed
    checkInstallation() {
        // Check if running in standalone mode (installed)
        if (window.matchMedia('(display-mode: standalone)').matches || 
            window.navigator.standalone === true) {
            this.isInstalled = true;
            console.log('PWA: App is installed');
        }

        // Check for related applications
        if ('getInstalledRelatedApps' in navigator) {
            navigator.getInstalledRelatedApps().then(apps => {
                if (apps.length > 0) {
                    this.isInstalled = true;
                    console.log('PWA: Related app is installed');
                }
            });
        }
    }

    // Register service worker
    async registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                const registration = await navigator.serviceWorker.register('/sw.js');
                console.log('PWA: Service Worker registered successfully');
                
                // Handle updates
                registration.addEventListener('updatefound', () => {
                    const newWorker = registration.installing;
                    newWorker.addEventListener('statechange', () => {
                        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                            this.showUpdateNotification();
                        }
                    });
                });

            } catch (error) {
                console.error('PWA: Service Worker registration failed:', error);
            }
        }
    }

    // Setup install prompt
    setupInstallPrompt() {
        window.addEventListener('beforeinstallprompt', (e) => {
            console.log('PWA: Install prompt available');
            e.preventDefault();
            this.deferredPrompt = e;
            this.showInstallButton();
        });

        // Handle successful installation
        window.addEventListener('appinstalled', () => {
            console.log('PWA: App installed successfully');
            this.isInstalled = true;
            this.hideInstallButton();
            this.showInstallSuccessMessage();
        });
    }

    // Create install button
    createInstallButton() {
        // Create install button if it doesn't exist
        if (!document.getElementById('pwa-install-btn')) {
            const installBtn = document.createElement('button');
            installBtn.id = 'pwa-install-btn';
            installBtn.className = 'btn btn-success pwa-install-button';
            installBtn.innerHTML = `
                <i class="fas fa-download me-2"></i>
                <span class="install-text">تثبيت التطبيق</span>
            `;
            installBtn.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                z-index: 1000;
                border-radius: 50px;
                padding: 12px 20px;
                font-weight: 600;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                display: none;
                animation: slideInUp 0.5s ease;
                background: linear-gradient(45deg, #5d6e35, #949c74);
                border: none;
                color: white;
                transition: all 0.3s ease;
            `;
            
            installBtn.addEventListener('click', () => this.installApp());
            document.body.appendChild(installBtn);
            this.installButton = installBtn;
        }
    }

    // Show install button
    showInstallButton() {
        if (this.installButton && !this.isInstalled) {
            this.installButton.style.display = 'block';
            
            // Add animation
            setTimeout(() => {
                this.installButton.style.transform = 'translateY(0)';
                this.installButton.style.opacity = '1';
            }, 100);
        }
    }

    // Hide install button
    hideInstallButton() {
        if (this.installButton) {
            this.installButton.style.display = 'none';
        }
    }

    // Install the app
    async installApp() {
        if (!this.deferredPrompt) {
            this.showManualInstallInstructions();
            return;
        }

        try {
            // Show install prompt
            this.deferredPrompt.prompt();
            
            // Wait for user response
            const { outcome } = await this.deferredPrompt.userChoice;
            
            if (outcome === 'accepted') {
                console.log('PWA: User accepted install prompt');
                this.showInstallPendingMessage();
            } else {
                console.log('PWA: User dismissed install prompt');
            }
            
            this.deferredPrompt = null;
            
        } catch (error) {
            console.error('PWA: Install failed:', error);
            this.showInstallErrorMessage();
        }
    }

    // Setup event listeners
    setupEventListeners() {
        // Listen for online/offline events
        window.addEventListener('online', () => {
            this.showConnectionMessage('متصل بالإنترنت', 'success');
        });

        window.addEventListener('offline', () => {
            this.showConnectionMessage('غير متصل - يمكنك الاستمرار في استخدام التطبيق', 'warning');
        });

        // Add CSS animations
        this.addAnimationStyles();
    }

    // Add CSS animations
    addAnimationStyles() {
        if (!document.getElementById('pwa-styles')) {
            const style = document.createElement('style');
            style.id = 'pwa-styles';
            style.textContent = `
                @keyframes slideInUp {
                    from {
                        transform: translateY(100px);
                        opacity: 0;
                    }
                    to {
                        transform: translateY(0);
                        opacity: 1;
                    }
                }
                
                @keyframes pulse {
                    0% { transform: scale(1); }
                    50% { transform: scale(1.05); }
                    100% { transform: scale(1); }
                }
                
                .pwa-install-button:hover {
                    transform: translateY(-2px) !important;
                    box-shadow: 0 6px 20px rgba(0,0,0,0.2) !important;
                    animation: pulse 1s infinite;
                }
                
                .pwa-notification {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 1001;
                    max-width: 350px;
                    border-radius: 10px;
                    padding: 15px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    animation: slideInDown 0.5s ease;
                }
                
                @keyframes slideInDown {
                    from {
                        transform: translateY(-100px);
                        opacity: 0;
                    }
                    to {
                        transform: translateY(0);
                        opacity: 1;
                    }
                }
                
                @media (max-width: 768px) {
                    .pwa-install-button {
                        bottom: 10px !important;
                        right: 10px !important;
                        padding: 10px 16px !important;
                        font-size: 14px !important;
                    }
                    
                    .pwa-notification {
                        top: 10px !important;
                        right: 10px !important;
                        left: 10px !important;
                        max-width: none !important;
                    }
                }
            `;
            document.head.appendChild(style);
        }
    }

    // Show notification messages
    showNotification(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} pwa-notification`;
        notification.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="fas fa-${this.getIconForType(type)} me-2"></i>
                <span>${message}</span>
                <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Auto remove after duration
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, duration);
    }

    getIconForType(type) {
        const icons = {
            'success': 'check-circle',
            'warning': 'exclamation-triangle',
            'danger': 'times-circle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    // Specific notification methods
    showInstallSuccessMessage() {
        this.showNotification('تم تثبيت التطبيق بنجاح! يمكنك الآن الوصول إليه من الشاشة الرئيسية.', 'success');
    }

    showInstallPendingMessage() {
        this.showNotification('جاري تثبيت التطبيق...', 'info', 3000);
    }

    showInstallErrorMessage() {
        this.showNotification('حدث خطأ أثناء التثبيت. يرجى المحاولة مرة أخرى.', 'danger');
    }

    showConnectionMessage(message, type) {
        this.showNotification(message, type, 3000);
    }

    showUpdateNotification() {
        const updateNotification = document.createElement('div');
        updateNotification.className = 'alert alert-info pwa-notification';
        updateNotification.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="fas fa-sync-alt me-2"></i>
                <span>تحديث جديد متاح للتطبيق</span>
                <button class="btn btn-sm btn-primary ms-2" onclick="location.reload()">تحديث</button>
                <button type="button" class="btn-close ms-2" onclick="this.parentElement.parentElement.remove()"></button>
            </div>
        `;
        document.body.appendChild(updateNotification);
    }

    showManualInstallInstructions() {
        const instructions = this.getManualInstallInstructions();
        this.showNotification(instructions, 'info', 10000);
    }

    getManualInstallInstructions() {
        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
        const isAndroid = /Android/.test(navigator.userAgent);
        
        if (isIOS) {
            return 'لتثبيت التطبيق على iOS: اضغط على زر المشاركة ثم "إضافة إلى الشاشة الرئيسية"';
        } else if (isAndroid) {
            return 'لتثبيت التطبيق على Android: اضغط على قائمة المتصفح ثم "إضافة إلى الشاشة الرئيسية"';
        } else {
            return 'لتثبيت التطبيق: اضغط على أيقونة التثبيت في شريط العنوان أو قائمة المتصفح';
        }
    }
}

// Initialize PWA installer when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.pwaInstaller = new PWAInstaller();
});

// Export for use in other scripts
window.PWAInstaller = PWAInstaller;
