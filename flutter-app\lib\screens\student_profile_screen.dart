import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/auth_service.dart';
import '../models/user_model.dart';
import '../utils/constants.dart';

class StudentProfileScreen extends StatefulWidget {
  const StudentProfileScreen({super.key});

  @override
  State<StudentProfileScreen> createState() => _StudentProfileScreenState();
}

class _StudentProfileScreenState extends State<StudentProfileScreen> {
  bool _isLoading = false;
  bool _isEditing = false;
  
  // Form controllers
  final _formKey = GlobalKey<FormState>();
  final _fullNameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _nationalityController = TextEditingController();
  
  Student? _student;

  @override
  void initState() {
    super.initState();
    _loadStudentProfile();
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _phoneController.dispose();
    _nationalityController.dispose();
    super.dispose();
  }

  Future<void> _loadStudentProfile() async {
    setState(() => _isLoading = true);
    
    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      final currentUser = authService.currentUser;
      
      if (currentUser is Student) {
        setState(() {
          _student = currentUser;
          _fullNameController.text = _student!.fullName;
          _phoneController.text = _student!.phone ?? '';
          _nationalityController.text = _student!.nationality;
        });
      }
    } catch (e) {
      print('Error loading student profile: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;
    
    // Responsive sizing
    final horizontalPadding = isTablet ? screenWidth * 0.05 : 16.0;
    final cardPadding = isTablet ? 20.0 : 16.0;
    final spacing = isTablet ? 20.0 : 16.0;
    final fontSize = isTablet ? 16.0 : 14.0;
    final titleFontSize = isTablet ? 20.0 : 18.0;

    return Scaffold(
      backgroundColor: const Color(0xFFF5F5DC),
      appBar: AppBar(
        title: Text(
          'الملف الشخصي',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: isTablet ? 22 : 18,
          ),
        ),
        backgroundColor: AppColors.studentPrimary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          if (!_isEditing)
            IconButton(
              onPressed: () {
                setState(() => _isEditing = true);
              },
              icon: const Icon(Icons.edit),
              tooltip: 'تعديل',
            ),
          if (_isEditing) ...[
            IconButton(
              onPressed: _cancelEditing,
              icon: const Icon(Icons.close),
              tooltip: 'إلغاء',
            ),
            IconButton(
              onPressed: _saveProfile,
              icon: const Icon(Icons.save),
              tooltip: 'حفظ',
            ),
          ],
        ],
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                color: AppColors.studentPrimary,
              ),
            )
          : SingleChildScrollView(
              padding: EdgeInsets.symmetric(
                horizontal: horizontalPadding,
                vertical: spacing,
              ),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Profile Header
                    _buildProfileHeader(cardPadding, fontSize, titleFontSize),
                    SizedBox(height: spacing),
                    
                    // Personal Information
                    _buildPersonalInfoSection(cardPadding, spacing, fontSize, titleFontSize),
                    SizedBox(height: spacing),
                    
                    // Academic Information
                    _buildAcademicInfoSection(cardPadding, spacing, fontSize, titleFontSize),
                    SizedBox(height: spacing),
                    
                    // Account Information
                    _buildAccountInfoSection(cardPadding, spacing, fontSize, titleFontSize),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildProfileHeader(double cardPadding, double fontSize, double titleFontSize) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(cardPadding),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.studentPrimary,
            AppColors.studentPrimary.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.2),
            spreadRadius: 2,
            blurRadius: 10,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        children: [
          // Avatar
          Container(
            width: titleFontSize * 4,
            height: titleFontSize * 4,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white, width: 3),
            ),
            child: Icon(
              Icons.person,
              size: titleFontSize * 2,
              color: Colors.white,
            ),
          ),
          SizedBox(height: cardPadding * 0.8),
          
          // Name
          Text(
            _student?.fullName ?? 'الطالب',
            style: TextStyle(
              color: Colors.white,
              fontSize: titleFontSize * 1.2,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: cardPadding * 0.3),
          
          // Username
          Text(
            '@${_student?.username ?? 'student'}',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.9),
              fontSize: fontSize,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: cardPadding * 0.3),
          
          // Status
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              _student?.isActive == true ? 'نشط' : 'غير نشط',
              style: TextStyle(
                color: Colors.white,
                fontSize: fontSize * 0.9,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPersonalInfoSection(double cardPadding, double spacing, double fontSize, double titleFontSize) {
    return Container(
      padding: EdgeInsets.all(cardPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.person_outline,
                color: AppColors.studentPrimary,
                size: titleFontSize,
              ),
              SizedBox(width: cardPadding * 0.5),
              Text(
                'المعلومات الشخصية',
                style: TextStyle(
                  fontSize: titleFontSize,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          SizedBox(height: spacing),
          
          // Full Name
          _buildInfoField(
            label: 'الاسم الكامل',
            controller: _fullNameController,
            icon: Icons.person,
            isEditable: true,
            fontSize: fontSize,
            cardPadding: cardPadding,
          ),
          SizedBox(height: spacing * 0.8),
          
          // Phone
          _buildInfoField(
            label: 'رقم الهاتف',
            controller: _phoneController,
            icon: Icons.phone,
            isEditable: true,
            fontSize: fontSize,
            cardPadding: cardPadding,
          ),
          SizedBox(height: spacing * 0.8),
          
          // Nationality
          _buildInfoField(
            label: 'الجنسية',
            controller: _nationalityController,
            icon: Icons.flag,
            isEditable: true,
            fontSize: fontSize,
            cardPadding: cardPadding,
          ),
        ],
      ),
    );
  }

  Widget _buildAcademicInfoSection(double cardPadding, double spacing, double fontSize, double titleFontSize) {
    return Container(
      padding: EdgeInsets.all(cardPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.school_outlined,
                color: AppColors.studentPrimary,
                size: titleFontSize,
              ),
              SizedBox(width: cardPadding * 0.5),
              Text(
                'المعلومات الأكاديمية',
                style: TextStyle(
                  fontSize: titleFontSize,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          SizedBox(height: spacing),
          
          // Academic info in grid
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: spacing * 0.8,
            mainAxisSpacing: spacing * 0.8,
            childAspectRatio: 2.5,
            children: [
              _buildReadOnlyInfoCard(
                'التخصص',
                _student?.specialization ?? '',
                Icons.book,
                fontSize,
                cardPadding,
              ),
              _buildReadOnlyInfoCard(
                'الشعبة',
                _student?.section ?? '',
                Icons.group,
                fontSize,
                cardPadding,
              ),
              _buildReadOnlyInfoCard(
                'الصف',
                _student?.studentClass ?? '',
                Icons.class_,
                fontSize,
                cardPadding,
              ),
              _buildReadOnlyInfoCard(
                'المستوى',
                _student?.level ?? '',
                Icons.trending_up,
                fontSize,
                cardPadding,
              ),
            ],
          ),
          
          if (_student?.result != null) ...[
            SizedBox(height: spacing * 0.8),
            _buildReadOnlyInfoCard(
              'النتيجة',
              _student!.result!,
              Icons.grade,
              fontSize,
              cardPadding,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAccountInfoSection(double cardPadding, double spacing, double fontSize, double titleFontSize) {
    return Container(
      padding: EdgeInsets.all(cardPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.account_circle_outlined,
                color: AppColors.studentPrimary,
                size: titleFontSize,
              ),
              SizedBox(width: cardPadding * 0.5),
              Text(
                'معلومات الحساب',
                style: TextStyle(
                  fontSize: titleFontSize,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          SizedBox(height: spacing),

          _buildReadOnlyInfoCard(
            'اسم المستخدم',
            _student?.username ?? '',
            Icons.alternate_email,
            fontSize,
            cardPadding,
          ),
          SizedBox(height: spacing * 0.8),

          _buildReadOnlyInfoCard(
            'تاريخ الإنشاء',
            _formatDate(_student?.createdAt),
            Icons.calendar_today,
            fontSize,
            cardPadding,
          ),
          SizedBox(height: spacing * 0.8),

          _buildReadOnlyInfoCard(
            'آخر تحديث',
            _formatDate(_student?.updatedAt),
            Icons.update,
            fontSize,
            cardPadding,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoField({
    required String label,
    required TextEditingController controller,
    required IconData icon,
    required bool isEditable,
    required double fontSize,
    required double cardPadding,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: fontSize,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: cardPadding * 0.3),
        TextFormField(
          controller: controller,
          enabled: _isEditing && isEditable,
          style: TextStyle(fontSize: fontSize),
          decoration: InputDecoration(
            prefixIcon: Icon(icon, color: AppColors.studentPrimary),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppColors.studentPrimary),
            ),
            disabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade200),
            ),
            filled: true,
            fillColor: _isEditing && isEditable
                ? Colors.white
                : Colors.grey.shade50,
            contentPadding: EdgeInsets.symmetric(
              horizontal: cardPadding * 0.8,
              vertical: cardPadding * 0.6,
            ),
          ),
          validator: isEditable ? (value) {
            if (value == null || value.trim().isEmpty) {
              return 'هذا الحقل مطلوب';
            }
            return null;
          } : null,
        ),
      ],
    );
  }

  Widget _buildReadOnlyInfoCard(
    String label,
    String value,
    IconData icon,
    double fontSize,
    double cardPadding,
  ) {
    return Container(
      padding: EdgeInsets.all(cardPadding * 0.8),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: fontSize,
                color: AppColors.studentPrimary,
              ),
              SizedBox(width: cardPadding * 0.3),
              Expanded(
                child: Text(
                  label,
                  style: TextStyle(
                    fontSize: fontSize * 0.9,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textSecondary,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: cardPadding * 0.2),
          Text(
            value.isNotEmpty ? value : 'غير محدد',
            style: TextStyle(
              fontSize: fontSize,
              fontWeight: FontWeight.w500,
              color: value.isNotEmpty ? AppColors.textPrimary : Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(String? dateString) {
    if (dateString == null || dateString.isEmpty) return 'غير محدد';

    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return 'غير محدد';
    }
  }

  void _cancelEditing() {
    setState(() {
      _isEditing = false;
      // Reset form to original values
      if (_student != null) {
        _fullNameController.text = _student!.fullName;
        _phoneController.text = _student!.phone ?? '';
        _nationalityController.text = _student!.nationality;
      }
    });
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      // TODO: Implement profile update API call
      // For now, just simulate success
      await Future.delayed(const Duration(seconds: 1));

      setState(() {
        _isEditing = false;
        // Update local student data
        if (_student != null) {
          _student = Student(
            id: _student!.id,
            username: _student!.username,
            fullName: _fullNameController.text.trim(),
            nationality: _nationalityController.text.trim(),
            phone: _phoneController.text.trim().isEmpty
                ? null
                : _phoneController.text.trim(),
            specialization: _student!.specialization,
            section: _student!.section,
            studentClass: _student!.studentClass,
            level: _student!.level,
            result: _student!.result,
            password: _student!.password,
            isActive: _student!.isActive,
            rememberToken: _student!.rememberToken,
            createdAt: _student!.createdAt,
            updatedAt: DateTime.now().toIso8601String(),
          );
        }
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ التغييرات بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ التغييرات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }
}
