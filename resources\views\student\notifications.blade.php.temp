@extends('layouts.app')

@section('styles')
<style>
    .notifications-table {
        border-collapse: separate;
        border-spacing: 0;
        width: 100%;
        border: none;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }
    
    .notifications-table th {
        background-color: var(--thistle-green);
        color: var(--costa-del-sol);
        font-weight: 600;
        padding: 15px 20px;
        text-align: left;
        border: none;
    }
    
    .notifications-table td {
        padding: 12px 20px;
        vertical-align: middle;
        border-top: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .notifications-table tr {
        background-color: #fff;
        transition: background-color 0.2s ease;
    }
    
    .notifications-table tr:hover {
        background-color: rgba(242, 242, 242, 0.5);
    }
    
    .notifications-table tr.unread {
        background-color: rgba(230, 235, 214, 0.2);
    }
    
    .priority-badge {
        padding: 6px 12px;
        border-radius: 30px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .priority-high {
        background-color: rgba(220, 53, 69, 0.15);
        color: #dc3545;
    }
    
    .priority-medium {
        background-color: rgba(255, 193, 7, 0.15);
        color: #856404;
    }
    
    .priority-low {
        background-color: rgba(40, 167, 69, 0.15);
        color: #28a745;
    }
    
    .status-badge {
        padding: 6px 12px;
        border-radius: 30px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .status-sent {
        background-color: rgba(40, 167, 69, 0.15);
        color: #28a745;
        border: 1px solid rgba(40, 167, 69, 0.3);
    }
    
    .status-read {
        background-color: rgba(108, 117, 125, 0.15);
        color: #6c757d;
        border: 1px solid rgba(108, 117, 125, 0.3);
    }
    
    .status-unread {
        background-color: rgba(255, 193, 7, 0.15);
        color: #856404;
        border: 1px solid rgba(255, 193, 7, 0.3);
    }
    
    .view-btn {
        background-color: #17a2b8;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 6px 12px;
        font-size: 0.85rem;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-block;
    }
    
    .view-btn:hover {
        background-color: #138496;
        transform: translateY(-2px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
        color: white;
    }
    
    .mark-read-btn {
        background-color: var(--costa-del-sol);
        color: white;
        border: none;
        border-radius: 4px;
        padding: 6px 12px;
        font-size: 0.85rem;
        transition: all 0.2s ease;
        margin-left: 5px;
    }
    
    .mark-read-btn:hover {
        background-color: #4b5c36;
        transform: translateY(-2px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
    }
    
    .filter-card {
        border: none;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        margin-bottom: 20px;
    }
    
    .filter-header {
        background-color: rgba(230, 235, 214, 0.3);
        color: var(--costa-del-sol);
        padding: 12px 16px;
        font-weight: 600;
    }
    
    .notification-detail-modal .modal-header {
        background-color: var(--thistle-green);
        color: var(--costa-del-sol);
    }
    
    .search-container {
        position: relative;
        margin-bottom: 20px;
    }
    
    .search-container .form-control {
        padding-left: 40px;
        border-radius: 30px;
        border: 1px solid rgba(0, 0, 0, 0.1);
    }
    
    .search-container .form-control:focus {
        box-shadow: 0 0 0 0.25rem rgba(93, 110, 53, 0.25);
        border-color: var(--costa-del-sol);
    }
    
    .search-icon {
        position: absolute;
        left: 15px;
        top: 10px;
        color: #6c757d;
    }
</style>
@endsection

@section('content')
<div class="container-fluid main-content px-4">
    <div class="row">
        <!-- Main Content Column -->
        <div class="col-md-9">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-center mb-4 mt-2">
                <div>
                    <h2 class="mb-1" style="color: var(--costa-del-sol); font-weight: 700;">Notifications History</h2>
                    <p class="mb-0" style="color: var(--gurkha);">View and manage your notifications</p>
                </div>
                <div>
                    <a href="{{ route('student.dashboard') }}" class="btn btn-sm" style="background-color: var(--tana); color: var(--costa-del-sol);">
                        <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
                    </a>
                </div>
            </div>

            <!-- Search Bar -->
            <div class="search-container">
                <form action="{{ route('student.notifications') }}" method="GET">
                    <div class="input-group">
                        <span class="search-icon">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" placeholder="Search notifications" name="search" value="{{ request('search') }}">
                        <button class="btn" type="submit" style="background-color: var(--costa-del-sol); color: white;">Search</button>
                    </div>
                </form>
            </div>

            <!-- Notifications Table -->
            <div class="card filter-card mb-4">
                <div class="card-body p-0">
                    @if(count($notifications) > 0)
                        <table class="notifications-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Title</th>
                                    <th>Priority</th>
                                    <th>Recipient Group</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($notifications as $recipient)
                                @php
                                    $notification = $recipient->notification;
                                    $isUnread = is_null($recipient->read_at);
                                    $isHighlighted = isset($highlightedNotificationId) && $notification->id == $highlightedNotificationId;
                                    
                                    // Determine priority (you may need to adjust this based on your data structure)
                                    $priority = $notification->priority ?? 'medium';
                                @endphp
                                <tr id="notification-{{ $notification->id }}" class="{{ $isUnread ? 'unread' : '' }} {{ $isHighlighted ? 'table-info' : '' }}">
                                    <td>{{ $notification->id }}</td>
                                    <td>{{ $notification->title }}</td>
                                    <td>
                                        <span class="priority-badge priority-{{ strtolower($priority) }}">
                                            {{ ucfirst($priority) }}
                                        </span>
                                    </td>
                                    <td>{{ $notification->recipient_group ?? 'All students' }}</td>
                                    <td>{{ $notification->created_at->format('Y-m-d H:i') }}</td>
                                    <td>
                                        @if($isUnread)
                                            <span class="status-badge status-unread">
                                                <i class="fas fa-circle me-1" style="font-size: 8px;"></i> Unread
                                            </span>
                                        @else
                                            <span class="status-badge status-read">
                                                <i class="fas fa-check-circle me-1"></i> Read
                                            </span>
                                        @endif
                                    </td>
                                    <td>
                                        <a href="#" class="view-btn" data-bs-toggle="modal" data-bs-target="#notificationModal{{ $notification->id }}">
                                            <i class="fas fa-eye me-1"></i> View
                                        </a>
                                        @if($isUnread)
                                            <form action="{{ route('student.notifications.mark-read', $notification->id) }}" method="POST" class="d-inline">
                                                @csrf
                                                <button type="submit" class="mark-read-btn">
                                                    <i class="fas fa-check-circle me-1"></i> Mark Read
                                                </button>
                                            </form>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                        
                        <!-- Pagination -->
                        <div class="d-flex justify-content-center p-3">
                            {{ $notifications->links() }}
                        </div>
                    @else
                        <div class="alert alert-info m-3">No notifications found</div>
                    @endif
                </div>
            </div>
        </div>
        
        <!-- Sidebar Column -->
        <div class="col-md-3">
            <!-- Filter Card -->
            <div class="card filter-card">
                <div class="card-header filter-header">
                    <i class="fas fa-filter me-2"></i> Filter
                </div>
                <div class="card-body">
                    <form action="{{ route('student.notifications') }}" method="GET">
                        <div class="mb-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">All</option>
                                <option value="read" {{ request('status') == 'read' ? 'selected' : '' }}>Read</option>
                                <option value="unread" {{ request('status') == 'unread' ? 'selected' : '' }}>Unread</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="date" class="form-label">Date Range</label>
                            <select class="form-select" id="date" name="date">
                                <option value="">All Time</option>
                                <option value="today" {{ request('date') == 'today' ? 'selected' : '' }}>Today</option>
                                <option value="week" {{ request('date') == 'week' ? 'selected' : '' }}>This Week</option>
                                <option value="month" {{ request('date') == 'month' ? 'selected' : '' }}>This Month</option>
                            </select>
                        </div>
                        <button type="submit" class="btn w-100" style="background-color: var(--costa-del-sol); color: white;">
                            <i class="fas fa-search me-1"></i> Apply Filters
                        </button>
                    </form>
                </div>
            </div>
            
            <!-- Quick Links Card -->
            <div class="card filter-card">
                <div class="card-header filter-header">
                    <i class="fas fa-link me-2"></i> Quick Links
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush border-0">
                        <a href="{{ route('student.dashboard') }}" class="list-group-item list-group-item-action d-flex align-items-center" style="color: var(--costa-del-sol);">
                            <i class="fas fa-tachometer-alt me-3"></i> Dashboard
                        </a>
                        <a href="#" class="list-group-item list-group-item-action d-flex align-items-center" style="color: var(--costa-del-sol);">
                            <i class="fas fa-user-cog me-3"></i> Update Profile
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Notification Detail Modals -->
@foreach($notifications as $recipient)
@php
    $notification = $recipient->notification;
    $attachments = [];
    if ($notification->attachments) {
        try {
            $attachments = json_decode($notification->attachments, true);
        } catch (\Exception $e) {
            $attachments = [];
        }
    }
@endphp
<div class="modal fade notification-detail-modal" id="notificationModal{{ $notification->id }}" tabindex="-1" aria-labelledby="notificationModalLabel{{ $notification->id }}" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="notificationModalLabel{{ $notification->id }}">{{ $notification->title }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <small class="text-muted">{{ $notification->created_at->format('F j, Y, g:i a') }}</small>
                </div>
                <div class="notification-content mb-4">
                    <p>{{ $notification->content }}</p>
                </div>
                
                @if(count($attachments) > 0)
                <div class="attachments-container p-3 mb-3" style="background-color: rgba(230, 235, 214, 0.3); border: 1px solid rgba(93, 110, 53, 0.2); border-radius: 8px;">
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-paperclip me-2" style="color: var(--costa-del-sol);"></i>
                        <h6 class="m-0" style="color: var(--costa-del-sol); font-weight: 600;">Attachments</h6>
                    </div>
                    <div class="d-flex flex-wrap">
                        @foreach($attachments as $attachment)
                            @if(isset($attachment['path']) && isset($attachment['name']))
                                <a href="{{ asset('storage/' . $attachment['path']) }}" 
                                   class="btn me-2 mb-2"
                                   style="background-color: var(--locust); color: white; border-radius: 30px;"
                                   target="_blank" 
                                   title="Download {{ $attachment['name'] }}">
                                    <i class="fas fa-file-download me-2"></i>
                                    <span style="max-width: 150px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">{{ $attachment['name'] }}</span>
                                </a>
                            @endif
                        @endforeach
                    </div>
                </div>
                @endif
            </div>
            <div class="modal-footer">
                @if(is_null($recipient->read_at))
                    <form action="{{ route('student.notifications.mark-read', $notification->id) }}" method="POST" class="d-inline">
                        @csrf
                        <button type="submit" class="btn" style="background-color: var(--costa-del-sol); color: white;">
                            <i class="fas fa-check-circle me-1"></i> Mark as Read
                        </button>
                    </form>
                @endif
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
@endforeach

@if(isset($highlightedNotificationId))
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const highlightedElement = document.getElementById('notification-{{ $highlightedNotificationId }}');
        if (highlightedElement) {
            setTimeout(function() {
                highlightedElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }, 300);
        }
    });
</script>
@endif
@endsection
