@extends('layouts.app')

@section('content')
<div class="container-fluid main-content px-4">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="d-flex justify-content-between align-items-center mb-4 mt-2">
                <div>
                    <h2 class="mb-1" style="color: var(--costa-del-sol); font-weight: 700;">Update Profile</h2>
                    <p class="mb-0" style="color: var(--gurkha);">Manage your personal information</p>
                </div>
                <div>
                    <a href="{{ route('student.dashboard') }}" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
                    </a>
                </div>
            </div>

            @if(session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                {{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            @endif

            @if($errors->any())
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <ul class="mb-0">
                    @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                    @endforeach
                </ul>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            @endif

            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>Profile Information</span>
                    <a href="{{ route('student.profile.change-password') }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-key me-1"></i> Change Password
                    </a>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('student.profile.update') }}">
                        @csrf
                        @method('PUT')
                        
                        <div class="row mb-3">
                            <label for="username" class="col-md-3 col-form-label text-md-end">Username</label>
                            <div class="col-md-9">
                                <input id="username" type="text" class="form-control bg-light" value="{{ $student->username }}" disabled readonly>
                                <small class="text-muted">Username cannot be changed</small>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label for="full_name" class="col-md-3 col-form-label text-md-end">Full Name</label>
                            <div class="col-md-9">
                                <input id="full_name" type="text" class="form-control @error('full_name') is-invalid @enderror" name="full_name" value="{{ old('full_name', $student->full_name) }}" required autofocus>
                                @error('full_name')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label for="phone" class="col-md-3 col-form-label text-md-end">Phone Number</label>
                            <div class="col-md-9">
                                <input id="phone" type="text" class="form-control @error('phone') is-invalid @enderror" name="phone" value="{{ old('phone', $student->phone) }}">
                                @error('phone')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>


                        <div class="row mb-0">
                            <div class="col-md-9 offset-md-3">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i> Update Profile
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">Academic Information</div>
                <div class="card-body" style="background-color: var(--parchment);">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Specialization</h6>
                                    <p class="card-text">{{ $student->specialization ?? 'Not specified' }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Result</h6>
                                    @php
                                        $resultText = $student->result ?? 'Not specified';
                                        $resultColor = '';
                                        if ($resultText == 'ناجح') {
                                            $resultColor = '#28a745'; // Green
                                        } elseif ($resultText == 'راسب') {
                                            $resultColor = '#dc3545'; // Red
                                        } elseif ($resultText == 'اكمال') {
                                            $resultColor = '#fd7e14'; // Orange
                                        }
                                    @endphp
                                    <p class="card-text">
                                        @if ($resultColor)
                                            <span style="color: {{ $resultColor }}; font-weight: bold;">{{ $resultText }}</span>
                                        @else
                                            {{ $resultText }}
                                        @endif
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <h6 class="card-title">Status</h6>
                                    <p class="card-text">
                                        <span style="color: #28a745; font-weight: bold;">{{ $student->status ?? 'Active' }}</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="alert alert-info mb-0 mt-3">
                        <i class="fas fa-info-circle me-2"></i> Contact administration if you need to change any of these details.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
