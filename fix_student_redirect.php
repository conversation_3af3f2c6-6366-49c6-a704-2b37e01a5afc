<?php
// <PERSON>ript to update all student login redirects in AuthController.php

$file = __DIR__ . '/app/Http/Controllers/AuthController.php';
$content = file_get_contents($file);

// Replace all instances of the student login redirect to dashboard with redirect to student.dashboard
$updatedContent = str_replace(
    "return redirect()->route('dashboard');",
    "return redirect()->route('student.dashboard');",
    $content
);

// Write the updated content back to the file
file_put_contents($file, $updatedContent);

echo "Updated AuthController.php successfully with student dashboard redirects.\n";
