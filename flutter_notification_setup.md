# 📱 Flutter Notification Setup Guide

## 🎯 **Complete Configuration for API Notifications**

This guide will help you configure your Flutter app to properly handle notifications from the Laravel API.

**📁 Project Structure:**
```
appnote-api/
├── app/
├── public/
├── ...
└── flutter-app/          # ✅ Your Flutter app is here
    ├── lib/
    ├── pubspec.yaml
    └── ...
```

---

## 📦 **1. Dependencies Setup**

### **pubspec.yaml:**
```yaml
dependencies:
  flutter:
    sdk: flutter

  # HTTP requests
  http: ^1.1.0

  # Local storage
  shared_preferences: ^2.2.2

  # File handling
  file_picker: ^6.1.1
  path: ^1.8.3
  path_provider: ^2.1.1

  # State management
  provider: ^6.1.1

  # Local notifications
  flutter_local_notifications: ^16.3.2

  # Push notifications (Firebase)
  firebase_core: ^2.24.2
  firebase_messaging: ^14.7.10

  # Background tasks
  workmanager: ^0.5.2

  # Permissions
  permission_handler: ^11.1.0

  # JSON handling
  json_annotation: ^4.8.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Code generation
  json_serializable: ^6.7.1
  build_runner: ^2.4.7

  # Testing
  mockito: ^5.4.4
```

---

## 🔧 **2. Project Structure**

Create the following folder structure in your `flutter-app/lib` directory:

```
flutter-app/lib/
├── main.dart
├── config/
│   ├── api_config.dart
│   └── app_config.dart
├── models/
│   ├── notification_model.dart
│   └── api_response.dart
├── services/
│   ├── notification_service.dart
│   ├── auth_service.dart
│   ├── storage_service.dart
│   └── push_notification_service.dart
├── providers/
│   └── notification_provider.dart
├── screens/
│   ├── notification_list_screen.dart
│   ├── send_notification_screen.dart
│   └── notification_detail_screen.dart
├── widgets/
│   ├── notification_card.dart
│   └── attachment_widget.dart
└── utils/
    ├── constants.dart
    ├── helpers.dart
    └── exceptions.dart
```

---

## ⚙️ **3. Configuration Files**

### **config/api_config.dart:**
```dart
class ApiConfig {
  // ✅ Updated for local development (Flutter app in same folder)
  static const String baseUrl = 'http://localhost/appnote-api/public/api';
  static const String storageUrl = 'http://localhost/appnote-api/public/storage';

  // For production, replace with your actual domain:
  // static const String baseUrl = 'https://your-domain.com/api';
  // static const String storageUrl = 'https://your-domain.com/storage';

  // API Endpoints
  static const String loginEndpoint = '/auth/admin/login';
  static const String notificationsEndpoint = '/notifications';
  static const String markReadEndpoint = '/notifications/{id}/mark-read';
  static const String bulkDeleteEndpoint = '/notifications/bulk-delete';

  // Request settings
  static const Duration requestTimeout = Duration(seconds: 30);
  static const int maxRetries = 3;
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB

  // Supported file types
  static const List<String> supportedFileTypes = [
    'pdf', 'doc', 'docx', 'xls', 'xlsx',
    'jpg', 'jpeg', 'png', 'gif', 'txt', 'zip'
  ];
}
```

### **flutter-app/lib/config/app_config.dart:**
```dart
class AppConfig {
  // App settings
  static const String appName = 'School Notification App';
  static const String appVersion = '1.0.0';

  // Notification settings
  static const int notificationCacheExpiry = 5; // minutes
  static const int maxNotificationsPerPage = 15;
  static const int backgroundSyncInterval = 15; // minutes

  // Local notification settings
  static const String notificationChannelId = 'school_notifications';
  static const String notificationChannelName = 'School Notifications';
  static const String notificationChannelDescription = 'Notifications from school system';

  // Storage keys
  static const String tokenKey = 'auth_token';
  static const String userKey = 'user_data';
  static const String notificationCacheKey = 'cached_notifications';
  static const String lastSyncKey = 'last_sync_time';
}

---

## 🔐 **4. Authentication Service**

### **flutter-app/lib/services/auth_service.dart:**
```dart
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../config/api_config.dart';
import '../config/app_config.dart';
import '../utils/exceptions.dart';

class AuthService {
  static AuthService? _instance;
  static AuthService get instance => _instance ??= AuthService._();
  AuthService._();

  String? _token;
  Map<String, dynamic>? _user;

  String? get token => _token;
  Map<String, dynamic>? get user => _user;
  bool get isAuthenticated => _token != null;

  Future<void> initialize() async {
    await _loadStoredAuth();
  }

  Future<bool> login(String username, String password) async {
    try {
      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}${ApiConfig.loginEndpoint}'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode({
          'username': username,
          'password': password,
        }),
      ).timeout(ApiConfig.requestTimeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          _token = data['data']['token'];
          _user = data['data']['user'];
          await _saveAuth();
          return true;
        }
      }
      return false;
    } catch (e) {
      throw AuthException('Login failed: $e');
    }
  }

  Future<void> logout() async {
    _token = null;
    _user = null;
    await _clearAuth();
  }

  Future<void> _saveAuth() async {
    final prefs = await SharedPreferences.getInstance();
    if (_token != null) {
      await prefs.setString(AppConfig.tokenKey, _token!);
    }
    if (_user != null) {
      await prefs.setString(AppConfig.userKey, jsonEncode(_user!));
    }
  }

  Future<void> _loadStoredAuth() async {
    final prefs = await SharedPreferences.getInstance();
    _token = prefs.getString(AppConfig.tokenKey);
    final userJson = prefs.getString(AppConfig.userKey);
    if (userJson != null) {
      _user = jsonDecode(userJson);
    }
  }

  Future<void> _clearAuth() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(AppConfig.tokenKey);
    await prefs.remove(AppConfig.userKey);
  }

  Map<String, String> get authHeaders => {
    'Authorization': 'Bearer $_token',
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  Map<String, String> get multipartHeaders => {
    'Authorization': 'Bearer $_token',
    'Accept': 'application/json',
  };
}
```

---

## 📊 **5. Data Models**

### **flutter-app/lib/models/notification_model.dart:**
```dart
import 'package:json_annotation/json_annotation.dart';

part 'notification_model.g.dart';

@JsonSerializable()
class NotificationModel {
  final int id;
  final String title;
  final String message;
  @JsonKey(name: 'sender_name')
  final String senderName;
  @JsonKey(name: 'recipient_type')
  final String recipientType;
  @JsonKey(name: 'recipient_ids')
  final List<int> recipientIds;
  @JsonKey(name: 'attachment_path')
  final List<String>? attachmentPaths;
  @JsonKey(name: 'attachment_name')
  final List<String>? attachmentNames;
  final String priority;
  final String status;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  NotificationModel({
    required this.id,
    required this.title,
    required this.message,
    required this.senderName,
    required this.recipientType,
    required this.recipientIds,
    this.attachmentPaths,
    this.attachmentNames,
    required this.priority,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) =>
      _$NotificationModelFromJson(json);

  Map<String, dynamic> toJson() => _$NotificationModelToJson(this);

  bool get hasAttachments =>
      attachmentPaths != null && attachmentPaths!.isNotEmpty;

  int get attachmentCount => attachmentPaths?.length ?? 0;

  String get priorityEmoji {
    switch (priority.toLowerCase()) {
      case 'high': return '🔴';
      case 'medium': return '🟡';
      case 'low': return '🟢';
      default: return '⚪';
    }
  }

  String get formattedDate {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
```

---

## 🔔 **6. Notification Service - FIXED VERSION**

### **flutter-app/lib/services/notification_service.dart:**
```dart
import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:path/path.dart';
import '../config/api_config.dart';
import '../models/notification_model.dart';
import '../services/auth_service.dart';
import '../utils/exceptions.dart';

class NotificationService {
  static NotificationService? _instance;
  static NotificationService get instance => _instance ??= NotificationService._();
  NotificationService._();

  final AuthService _authService = AuthService.instance;

  /// ✅ FIXED: Send text-only notification with multiple recipients
  Future<NotificationModel> sendTextNotification({
    required String title,
    required String message,
    required dynamic recipientIds, // Can be List<int> or String
    String recipientType = 'students',
    String priority = 'medium',
    int senderId = 1,
    String senderName = 'Flutter App',
    String senderType = 'admin',
  }) async {
    if (!_authService.isAuthenticated) {
      throw AuthException('User not authenticated');
    }

    try {
      // ✅ FIXED: Ensure recipient_ids is always an array
      List<dynamic> processedRecipientIds;
      if (recipientIds is String) {
        if (recipientIds == 'all') {
          processedRecipientIds = ['all'];
        } else {
          // If it's a comma-separated string, split it
          processedRecipientIds = recipientIds.split(',').map((id) => id.trim()).toList();
        }
      } else if (recipientIds is List) {
        processedRecipientIds = recipientIds;
      } else {
        processedRecipientIds = [recipientIds];
      }

      final requestBody = {
        'title': title,
        'message': message,
        'sender_id': senderId,
        'sender_name': senderName,
        'sender_type': senderType,
        'recipient_type': recipientType,
        'recipient_ids': processedRecipientIds, // ✅ Always an array
        'priority': priority,
      };

      print('📤 Sending notification: ${jsonEncode(requestBody)}');

      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}${ApiConfig.notificationsEndpoint}'),
        headers: _authService.authHeaders,
        body: jsonEncode(requestBody),
      ).timeout(ApiConfig.requestTimeout);

      print('📥 Response: ${response.statusCode} - ${response.body}');

      if (response.statusCode == 201) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          return NotificationModel.fromJson(data['data']);
        } else {
          throw ApiException(data['message'] ?? 'Failed to send notification');
        }
      } else if (response.statusCode == 401) {
        throw AuthException('Authentication failed');
      } else {
        throw ApiException('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      print('❌ Error: $e');
      if (e is AuthException || e is ApiException) rethrow;
      throw NetworkException('Network error: $e');
    }
  }

  /// ✅ FIXED: Send notification with file attachments
  Future<NotificationModel> sendNotificationWithFiles({
    required String title,
    required String message,
    required dynamic recipientIds, // Can be List<int> or String
    required List<File> files,
    String recipientType = 'students',
    String priority = 'high',
    int senderId = 1,
    String senderName = 'Flutter App',
    String senderType = 'admin',
  }) async {
    if (!_authService.isAuthenticated) {
      throw AuthException('User not authenticated');
    }

    // Validate files
    for (File file in files) {
      if (!await file.exists()) {
        throw ValidationException('File does not exist: ${file.path}');
      }

      final fileSize = await file.length();
      if (fileSize > ApiConfig.maxFileSize) {
        throw ValidationException('File too large: ${basename(file.path)}');
      }

      final extension = extension(file.path).toLowerCase().replaceFirst('.', '');
      if (!ApiConfig.supportedFileTypes.contains(extension)) {
        throw ValidationException('Unsupported file type: $extension');
      }
    }

    try {
      // ✅ FIXED: Process recipient_ids to ensure it's an array
      List<dynamic> processedRecipientIds;
      if (recipientIds is String) {
        if (recipientIds == 'all') {
          processedRecipientIds = ['all'];
        } else {
          processedRecipientIds = recipientIds.split(',').map((id) => id.trim()).toList();
        }
      } else if (recipientIds is List) {
        processedRecipientIds = recipientIds;
      } else {
        processedRecipientIds = [recipientIds];
      }

      var request = http.MultipartRequest(
        'POST',
        Uri.parse('${ApiConfig.baseUrl}${ApiConfig.notificationsEndpoint}'),
      );

      request.headers.addAll(_authService.multipartHeaders);

      // Add form fields
      request.fields.addAll({
        'title': title,
        'message': message,
        'sender_id': senderId.toString(),
        'sender_name': senderName,
        'sender_type': senderType,
        'recipient_type': recipientType,
        'priority': priority,
      });

      // ✅ FIXED: Add recipient_ids as array in multipart
      for (int i = 0; i < processedRecipientIds.length; i++) {
        request.fields['recipient_ids[$i]'] = processedRecipientIds[i].toString();
      }

      print('📤 Multipart fields: ${request.fields}');
      print('📤 Recipients: $processedRecipientIds');
      print('📤 Files: ${files.length}');

      // Add files
      for (int i = 0; i < files.length; i++) {
        File file = files[i];
        String filename = basename(file.path);

        print('📎 Adding file $i: $filename');

        request.files.add(await http.MultipartFile.fromPath(
          'attachments[]', // ✅ FIXED: Use attachments[] with brackets
          file.path,
          filename: filename,
        ));
      }

      print('📤 Total files: ${request.files.length}');

      final response = await request.send().timeout(ApiConfig.requestTimeout);
      final responseBody = await response.stream.bytesToString();

      print('📥 Response: ${response.statusCode} - $responseBody');

      if (response.statusCode == 201) {
        final data = jsonDecode(responseBody);
        if (data['success'] == true) {
          return NotificationModel.fromJson(data['data']);
        } else {
          throw ApiException(data['message'] ?? 'Failed to send notification');
        }
      } else if (response.statusCode == 401) {
        throw AuthException('Authentication failed');
      } else {
        throw ApiException('HTTP ${response.statusCode}: $responseBody');
      }
    } catch (e) {
      print('❌ Error: $e');
      if (e is AuthException || e is ApiException || e is ValidationException) rethrow;
      throw NetworkException('Network error: $e');
    }
  }

  /// ✅ NEW: Send notification with attachment paths (not files)
  Future<NotificationModel> sendNotificationWithPaths({
    required String title,
    required String message,
    required dynamic recipientIds, // Can be List<int> or String
    required dynamic attachmentPaths, // Can be List<String> or String
    required dynamic attachmentNames, // Can be List<String> or String
    String recipientType = 'students',
    String priority = 'high',
    int senderId = 1,
    String senderName = 'Flutter App',
    String senderType = 'admin',
  }) async {
    if (!_authService.isAuthenticated) {
      throw AuthException('User not authenticated');
    }

    try {
      // ✅ FIXED: Process recipient_ids to ensure it's an array
      List<dynamic> processedRecipientIds;
      if (recipientIds is String) {
        if (recipientIds == 'all') {
          processedRecipientIds = ['all'];
        } else {
          processedRecipientIds = recipientIds.split(',').map((id) => id.trim()).toList();
        }
      } else if (recipientIds is List) {
        processedRecipientIds = recipientIds;
      } else {
        processedRecipientIds = [recipientIds];
      }

      // ✅ FIXED: Process attachment_paths to ensure it's an array
      List<String> processedAttachmentPaths;
      if (attachmentPaths is String) {
        processedAttachmentPaths = attachmentPaths.split(',').map((path) => path.trim()).toList();
      } else if (attachmentPaths is List) {
        processedAttachmentPaths = List<String>.from(attachmentPaths);
      } else {
        processedAttachmentPaths = [attachmentPaths.toString()];
      }

      // ✅ FIXED: Process attachment_names to ensure it's an array
      List<String> processedAttachmentNames;
      if (attachmentNames is String) {
        processedAttachmentNames = attachmentNames.split(',').map((name) => name.trim()).toList();
      } else if (attachmentNames is List) {
        processedAttachmentNames = List<String>.from(attachmentNames);
      } else {
        processedAttachmentNames = [attachmentNames.toString()];
      }

      final requestBody = {
        'title': title,
        'message': message,
        'sender_id': senderId,
        'sender_name': senderName,
        'sender_type': senderType,
        'recipient_type': recipientType,
        'recipient_ids': processedRecipientIds, // ✅ Always an array
        'priority': priority,
        'attachment_paths': processedAttachmentPaths, // ✅ Always an array
        'attachment_names': processedAttachmentNames, // ✅ Always an array
      };

      print('📤 Sending with paths: ${jsonEncode(requestBody)}');

      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}${ApiConfig.notificationsEndpoint}'),
        headers: _authService.authHeaders,
        body: jsonEncode(requestBody),
      ).timeout(ApiConfig.requestTimeout);

      print('📥 Response: ${response.statusCode} - ${response.body}');

      if (response.statusCode == 201) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          return NotificationModel.fromJson(data['data']);
        } else {
          throw ApiException(data['message'] ?? 'Failed to send notification');
        }
      } else if (response.statusCode == 401) {
        throw AuthException('Authentication failed');
      } else {
        throw ApiException('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      print('❌ Error: $e');
      if (e is AuthException || e is ApiException) rethrow;
      throw NetworkException('Network error: $e');
    }
  }

  /// Get all notifications
  Future<List<NotificationModel>> getNotifications({
    int page = 1,
    int perPage = 15,
  }) async {
    if (!_authService.isAuthenticated) {
      throw AuthException('User not authenticated');
    }

    try {
      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}${ApiConfig.notificationsEndpoint}?page=$page&per_page=$perPage'),
        headers: _authService.authHeaders,
      ).timeout(ApiConfig.requestTimeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          final List<dynamic> notificationsList = data['data'];
          return notificationsList
              .map((json) => NotificationModel.fromJson(json))
              .toList();
        } else {
          throw ApiException(data['message'] ?? 'Failed to fetch notifications');
        }
      } else if (response.statusCode == 401) {
        throw AuthException('Authentication failed');
      } else {
        throw ApiException('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      if (e is AuthException || e is ApiException) rethrow;
      throw NetworkException('Network error: $e');
    }
  }
}
```

---

## ⚠️ **7. Exception Handling**

### **flutter-app/lib/utils/exceptions.dart:**
```dart
class AppException implements Exception {
  final String message;
  AppException(this.message);

  @override
  String toString() => message;
}

class AuthException extends AppException {
  AuthException(String message) : super(message);
}

class ApiException extends AppException {
  final int? statusCode;
  ApiException(String message, [this.statusCode]) : super(message);
}

class NetworkException extends AppException {
  NetworkException(String message) : super(message);
}

class ValidationException extends AppException {
  ValidationException(String message) : super(message);
}
```

---

## 🧪 **8. Testing Examples - FIXED VERSIONS**

### **Test Multiple Recipients:**
```dart
Future<void> testMultipleRecipients() async {
  try {
    final notification = await NotificationService.instance.sendTextNotification(
      title: 'Test Multiple Recipients',
      message: 'This notification goes to multiple recipients',
      recipientIds: [1, 2, 3, 4, 5], // ✅ Multiple recipients
      recipientType: 'students',
      priority: 'high',
    );

    print('✅ Success: ${notification.id}');
    print('✅ Recipients: ${notification.recipientIds}');
  } catch (e) {
    print('❌ Error: $e');
  }
}
```

### **Test File Attachments:**
```dart
Future<void> testFileAttachments() async {
  try {
    // Pick files
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      allowMultiple: true,
      type: FileType.custom,
      allowedExtensions: ['pdf', 'doc', 'docx', 'jpg', 'png'],
    );

    if (result != null) {
      List<File> files = result.paths.map((path) => File(path!)).toList();

      final notification = await NotificationService.instance.sendNotificationWithFiles(
        title: 'Test With Attachments',
        message: 'This notification has file attachments',
        recipientIds: [1, 2, 3], // ✅ Multiple recipients
        files: files, // ✅ File attachments
        recipientType: 'students',
        priority: 'high',
      );

      print('✅ Success: ${notification.id}');
      print('✅ Attachments: ${notification.attachmentNames}');
    }
  } catch (e) {
    print('❌ Error: $e');
  }
}
```

### **Test Attachment Paths:**
```dart
Future<void> testAttachmentPaths() async {
  try {
    final notification = await NotificationService.instance.sendNotificationWithPaths(
      title: 'Test With Paths',
      message: 'This notification has predefined paths',
      recipientIds: [1, 2, 3, 4], // ✅ Multiple recipients
      attachmentPaths: [
        'notifications/document1.pdf',
        'notifications/image1.jpg'
      ],
      attachmentNames: [
        'Important Document.pdf',
        'Sample Image.jpg'
      ],
      recipientType: 'students',
      priority: 'medium',
    );

    print('✅ Success: ${notification.id}');
    print('✅ Paths: ${notification.attachmentPaths}');
  } catch (e) {
    print('❌ Error: $e');
  }
}
```

---

## 🚀 **9. Quick Setup Steps for flutter-app**

### **Step 1: Navigate to flutter-app directory:**
```bash
cd appnote-api/flutter-app
```

### **Step 2: Install dependencies:**
```bash
flutter pub get
```

### **Step 3: Generate JSON serialization:**
```bash
flutter packages pub run build_runner build
```

### **Step 4: Update API configuration:**
```dart
// In lib/config/api_config.dart
static const String baseUrl = 'http://localhost/appnote-api/public/api';
static const String storageUrl = 'http://localhost/appnote-api/public/storage';
```

### **Step 5: Test authentication:**
```dart
// Test login first
final authService = AuthService.instance;
await authService.initialize();
final success = await authService.login('admin123', 'admin123');
```

### **Step 6: Test notifications:**
```dart
// Test sending notification
await testMultipleRecipients();
await testFileAttachments();
```

---

## ✅ **10. Expected Results After Fixes**

### **✅ Multiple Recipients:**
```json
{
  "recipient_ids": [1, 2, 3, 4, 5]  // Instead of [1]
}
```

### **✅ File Attachments:**
```json
{
  "attachment_path": ["notifications/file1.pdf", "notifications/file2.jpg"],
  "attachment_name": ["Document.pdf", "Image.jpg"]
}
```

### **✅ Debug Output:**
```
📤 Sending notification: {...}
📤 Recipients: [1, 2, 3, 4, 5]
📤 Files: 2
📎 Adding file 0: document.pdf
📎 Adding file 1: image.jpg
📤 Total files: 2
📥 Response: 201 - {"success":true,...}
```

---

## 🔍 **11. Debugging Tips**

### **Check Flutter Console:**
Look for debug messages starting with:
- `📤 Sending notification:`
- `📤 Recipients:`
- `📎 Adding file:`
- `📥 Response:`

### **Check Laravel Logs:**
```bash
cd appnote-api
tail -f storage/logs/laravel.log
```

### **Verify Database:**
```sql
SELECT id, title, recipient_ids, attachment_path, attachment_name
FROM notifications
ORDER BY created_at DESC
LIMIT 5;
```

---

## 🎉 **Your Flutter app is now ready!**

**The flutter-app in your appnote-api folder is configured to:**
- ✅ Send notifications with multiple recipients
- ✅ Upload and attach files correctly
- ✅ Handle all API responses properly
- ✅ Debug issues with clear console output

**Start developing your notification features!** 🚀

