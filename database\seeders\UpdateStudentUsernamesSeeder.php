<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Student;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class UpdateStudentUsernamesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all students
        $students = Student::all();
        
        // Counter for generating sequential usernames
        $counter = 1;
        
        foreach ($students as $student) {
            // Generate username in format 'stu' + 4 digits (with leading zeros if needed)
            $username = 'stu' . str_pad($counter, 4, '0', STR_PAD_LEFT);
            
            // Update the student record
            $student->username = $username;
            
            // Set default values for new fields
            $student->phone = $student->mobile_number ?? null;
            $student->specialization = 'General';
            $student->section = 'A';
            $student->class = $student->class_name ?? 'General';
            $student->level = '1';
            $student->result = null;
            
            // Set default password (already hashed in migration, but just in case)
            if (!Hash::check('stu123', $student->password)) {
                $student->password = Hash::make('stu123');
            }
            
            $student->save();
            
            // Increment counter
            $counter++;
        }
        
        $this->command->info('Updated ' . ($counter - 1) . ' students with new username format and default values.');
    }
}
