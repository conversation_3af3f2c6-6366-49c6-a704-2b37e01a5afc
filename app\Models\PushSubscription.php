<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PushSubscription extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'user_type',
        'endpoint',
        'public_key',
        'auth_token',
        'content_encoding',
        'user_agent',
        'ip_address',
        'is_active',
        'last_used_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'is_active' => 'boolean',
        'last_used_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the user that owns the subscription (polymorphic).
     */
    public function user()
    {
        return $this->morphTo('user', 'user_type', 'user_id');
    }

    /**
     * Get the student if user_type is student.
     */
    public function student()
    {
        return $this->belongsTo(Student::class, 'user_id')->where('user_type', 'student');
    }

    /**
     * Get the employee if user_type is employee.
     */
    public function employee()
    {
        return $this->belongsTo(Employee::class, 'user_id')->where('user_type', 'employee');
    }

    /**
     * Get the admin if user_type is admin.
     */
    public function admin()
    {
        return $this->belongsTo(User::class, 'user_id')->where('user_type', 'admin');
    }

    /**
     * Scope to get active subscriptions.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get subscriptions for a specific user type.
     */
    public function scopeForUserType($query, $userType)
    {
        return $query->where('user_type', $userType);
    }

    /**
     * Scope to get subscriptions for specific user IDs.
     */
    public function scopeForUsers($query, array $userIds, $userType)
    {
        return $query->where('user_type', $userType)->whereIn('user_id', $userIds);
    }

    /**
     * Get the subscription data formatted for web push.
     */
    public function getSubscriptionData()
    {
        return [
            'endpoint' => $this->endpoint,
            'keys' => [
                'p256dh' => $this->public_key,
                'auth' => $this->auth_token,
            ],
            'contentEncoding' => $this->content_encoding ?? 'aes128gcm',
        ];
    }

    /**
     * Update the last used timestamp.
     */
    public function updateLastUsed()
    {
        $this->update(['last_used_at' => now()]);
    }

    /**
     * Deactivate the subscription.
     */
    public function deactivate()
    {
        $this->update(['is_active' => false]);
    }

    /**
     * Check if subscription is expired (not used for 30 days).
     */
    public function isExpired()
    {
        return $this->last_used_at && $this->last_used_at->diffInDays(now()) > 30;
    }
}
