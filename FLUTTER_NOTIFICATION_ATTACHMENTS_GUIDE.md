# 📱 دليل إرسال الإشعارات مع المرفقات من Flutter

## 🔧 **تم إصلاح API لدعم رفع المرفقات!**

### ✅ **التحديثات المطبقة:**
1. **دعم رفع الملفات** - يمكن الآن رفع ملفات فعلية من Flutter
2. **دعم المرفقات المتعددة** - إرسال عدة ملفات في إشعار واحد
3. **التوافق مع الطرق القديمة** - لا يزال يدعم إرسال مسارات الملفات مباشرة

---

## 📋 **طرق إرسال الإشعارات مع المرفقات:**

### 1. **الطريقة الأولى: رفع الملفات (الأفضل)**
```dart
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:path/path.dart';

Future<void> sendNotificationWithFiles(List<File> files) async {
  var request = http.MultipartRequest(
    'POST',
    Uri.parse('$baseUrl/api/notifications'),
  );

  // إضافة headers
  request.headers.addAll({
    'Authorization': 'Bearer $token',
    'Accept': 'application/json',
  });

  // إضافة البيانات الأساسية
  request.fields.addAll({
    'title': 'إشعار مع مرفقات',
    'message': 'هذا إشعار يحتوي على ملفات مرفقة',
    'sender_id': '1',
    'sender_name': 'Flutter App',
    'sender_type': 'admin',
    'recipient_type': 'students',
    'priority': 'high',
  });

  // إضافة recipient_ids كـ array
  for (int i = 0; i < recipientIds.length; i++) {
    request.fields['recipient_ids[$i]'] = recipientIds[i].toString();
  }

  // إضافة الملفات
  for (File file in files) {
    var stream = http.ByteStream(file.openRead());
    var length = await file.length();
    var filename = basename(file.path);

    var multipartFile = http.MultipartFile(
      'attachments[]', // اسم الحقل في API
      stream,
      length,
      filename: filename,
    );

    request.files.add(multipartFile);
  }

  // إرسال الطلب
  var response = await request.send();
  var responseBody = await response.stream.bytesToString();

  if (response.statusCode == 201) {
    print('✅ تم إرسال الإشعار بنجاح');
    print('Response: $responseBody');
  } else {
    print('❌ فشل في إرسال الإشعار: ${response.statusCode}');
    print('Error: $responseBody');
  }
}
```

### 2. **الطريقة الثانية: إرسال مسارات الملفات**
```dart
Future<void> sendNotificationWithPaths() async {
  final response = await http.post(
    Uri.parse('$baseUrl/api/notifications'),
    headers: {
      'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
    body: jsonEncode({
      'title': 'إشعار مع مرفقات',
      'message': 'هذا إشعار يحتوي على ملفات مرفقة',
      'sender_id': 1,
      'sender_name': 'Flutter App',
      'sender_type': 'admin',
      'recipient_type': 'students',
      'recipient_ids': [1, 2, 3],
      'priority': 'high',
      // إرسال مسارات الملفات مباشرة
      'attachment_paths': [
        'notifications/document1.pdf',
        'notifications/image1.jpg'
      ],
      'attachment_names': [
        'المستند الأول.pdf',
        'الصورة الأولى.jpg'
      ],
    }),
  );

  if (response.statusCode == 201) {
    print('✅ تم إرسال الإشعار بنجاح');
  } else {
    print('❌ فشل في إرسال الإشعار: ${response.statusCode}');
    print('Error: ${response.body}');
  }
}
```

### 3. **الطريقة الثالثة: مرفق واحد (للتوافق مع الكود القديم)**
```dart
Future<void> sendNotificationWithSingleAttachment() async {
  final response = await http.post(
    Uri.parse('$baseUrl/api/notifications'),
    headers: {
      'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
    body: jsonEncode({
      'title': 'إشعار مع مرفق واحد',
      'message': 'هذا إشعار يحتوي على مرفق واحد',
      'sender_id': 1,
      'sender_name': 'Flutter App',
      'sender_type': 'admin',
      'recipient_type': 'students',
      'recipient_ids': [1, 2, 3],
      'priority': 'medium',
      // مرفق واحد
      'attachment_path': 'notifications/document.pdf',
      'attachment_name': 'المستند.pdf',
    }),
  );

  if (response.statusCode == 201) {
    print('✅ تم إرسال الإشعار بنجاح');
  } else {
    print('❌ فشل في إرسال الإشعار: ${response.statusCode}');
    print('Error: ${response.body}');
  }
}
```

---

## 🧪 **اختبار API مع PowerShell:**

### 1. **اختبار رفع ملف واحد:**
```powershell
# إنشاء ملف تجريبي
"Test content for notification attachment" | Out-File -FilePath "test_file.txt" -Encoding UTF8

# إرسال الإشعار مع الملف
$headers = @{
    "Authorization" = "Bearer YOUR_TOKEN"
}

$form = @{
    title = "Test Notification with File"
    message = "This notification has a file attachment"
    sender_id = "1"
    sender_name = "PowerShell Test"
    sender_type = "admin"
    recipient_type = "students"
    "recipient_ids[0]" = "1"
    "recipient_ids[1]" = "2"
    priority = "high"
    "attachments" = Get-Item "test_file.txt"
}

Invoke-RestMethod -Uri "http://localhost/appnote-api/public/api/notifications" -Method POST -Headers $headers -Form $form
```

### 2. **اختبار مع مسارات مباشرة:**
```powershell
$notificationData = @{
    title = "إشعار مع مرفقات"
    message = "هذا إشعار يحتوي على مرفقات"
    sender_id = 1
    sender_name = "PowerShell Test"
    sender_type = "admin"
    recipient_type = "students"
    recipient_ids = @(1, 2, 3)
    priority = "high"
    attachment_paths = @("notifications/test1.pdf", "notifications/test2.docx")
    attachment_names = @("ملف تجريبي 1.pdf", "ملف تجريبي 2.docx")
} | ConvertTo-Json -Depth 3

$headers = @{
    "Authorization" = "Bearer YOUR_TOKEN"
    "Content-Type" = "application/json"
}

Invoke-WebRequest -Uri "http://localhost/appnote-api/public/api/notifications" -Method POST -Headers $headers -Body $notificationData
```

---

## 📊 **تنسيق البيانات المطلوب:**

### ✅ **الحقول المطلوبة:**
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `title` | string | ✅ Yes | عنوان الإشعار |
| `message` | string | ✅ Yes | محتوى الإشعار |
| `sender_id` | integer | ✅ Yes | ID المرسل |
| `sender_name` | string | ✅ Yes | اسم المرسل |
| `recipient_type` | string | ✅ Yes | نوع المستلم (students/employees/all) |
| `recipient_ids` | array | ✅ Yes | قائمة IDs المستلمين |

### ✅ **حقول المرفقات (اختيارية):**
| Field | Type | Description |
|-------|------|-------------|
| `attachments[]` | files | ملفات للرفع |
| `attachment_paths[]` | array | مسارات الملفات |
| `attachment_names[]` | array | أسماء الملفات |
| `attachment_path` | string | مسار ملف واحد (legacy) |
| `attachment_name` | string | اسم ملف واحد (legacy) |

### ✅ **حقول إضافية:**
| Field | Type | Default | Options |
|-------|------|---------|---------|
| `sender_type` | string | "admin" | admin/employee/student |
| `priority` | string | "medium" | low/medium/high |

---

## 🔍 **استكشاف الأخطاء:**

### ❌ **خطأ: "No attachments received"**
**السبب**: الملفات لم تصل للـ API
**الحل**:
```dart
// تأكد من استخدام MultipartRequest للملفات
var request = http.MultipartRequest('POST', uri);
// وليس http.post() العادي
```

### ❌ **خطأ: "File too large"**
**السبب**: حجم الملف أكبر من 10MB
**الحل**: تقليل حجم الملف أو تغيير الحد الأقصى في API

### ❌ **خطأ: "Invalid file type"**
**السبب**: نوع الملف غير مدعوم
**الحل**: التحقق من أنواع الملفات المدعومة

---

## 🚀 **النتيجة النهائية:**

الآن يمكن لتطبيق Flutter:
- ✅ **رفع ملفات فعلية** إلى Laravel API
- ✅ **إرسال مرفقات متعددة** في إشعار واحد
- ✅ **عرض المرفقات** في جميع واجهات النظام
- ✅ **تحميل المرفقات** من الواجهات

**جرب الآن إرسال إشعار مع مرفقات من Flutter!** 🎉
