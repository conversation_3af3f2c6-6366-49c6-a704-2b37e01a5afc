import 'package:flutter/material.dart';
import '../models/notification.dart';
import '../services/attachment_service.dart';
import '../services/student_service.dart';
import '../utils/constants.dart';

class StudentNotificationsScreen extends StatefulWidget {
  const StudentNotificationsScreen({super.key});

  @override
  State<StudentNotificationsScreen> createState() => _StudentNotificationsScreenState();
}

class _StudentNotificationsScreenState extends State<StudentNotificationsScreen> {
  bool _isLoading = false;
  List<AppNotification> _notifications = [];
  String _selectedFilter = 'all'; // all, read, unread

  @override
  void initState() {
    super.initState();
    _loadNotifications();
  }

  Future<void> _loadNotifications() async {
    setState(() => _isLoading = true);

    try {
      final notifications = await StudentService.getNotifications();
      setState(() {
        _notifications = notifications;
      });
    } catch (e) {
      print('Error loading notifications: $e');

      // Set empty list in case of error
      setState(() {
        _notifications = [];
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الإشعارات: $e'),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'إعادة المحاولة',
              textColor: Colors.white,
              onPressed: _loadNotifications,
            ),
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  List<AppNotification> get _filteredNotifications {
    switch (_selectedFilter) {
      case 'read':
        return _notifications.where((n) => n.isRead).toList();
      case 'unread':
        return _notifications.where((n) => !n.isRead).toList();
      default:
        return _notifications;
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;
    
    // Responsive sizing
    final horizontalPadding = isTablet ? screenWidth * 0.05 : 16.0;
    final cardPadding = isTablet ? 20.0 : 16.0;
    final spacing = isTablet ? 20.0 : 16.0;
    final fontSize = isTablet ? 16.0 : 14.0;
    final titleFontSize = isTablet ? 20.0 : 18.0;

    return Scaffold(
      backgroundColor: const Color(0xFFF5F5DC),
      appBar: AppBar(
        title: Text(
          'الإشعارات',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: isTablet ? 22 : 18,
          ),
        ),
        backgroundColor: AppColors.studentPrimary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _loadNotifications,
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
          ),
          PopupMenuButton<String>(
            onSelected: _handleFilterChange,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'all',
                child: Row(
                  children: [
                    Icon(Icons.list, color: Colors.blue),
                    SizedBox(width: 8),
                    Text('جميع الإشعارات'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'unread',
                child: Row(
                  children: [
                    Icon(Icons.mark_email_unread, color: Colors.orange),
                    SizedBox(width: 8),
                    Text('غير المقروءة'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'read',
                child: Row(
                  children: [
                    Icon(Icons.mark_email_read, color: Colors.green),
                    SizedBox(width: 8),
                    Text('المقروءة'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                color: AppColors.studentPrimary,
              ),
            )
          : RefreshIndicator(
              onRefresh: _loadNotifications,
              color: AppColors.studentPrimary,
              child: Column(
                children: [
                  // Filter Chips
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.symmetric(
                      horizontal: horizontalPadding,
                      vertical: spacing * 0.8,
                    ),
                    child: _buildFilterChips(fontSize),
                  ),
                  
                  // Statistics
                  Container(
                    margin: EdgeInsets.symmetric(horizontal: horizontalPadding),
                    padding: EdgeInsets.all(cardPadding),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withValues(alpha: 0.1),
                          spreadRadius: 1,
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: _buildStatistics(fontSize, titleFontSize),
                  ),
                  
                  SizedBox(height: spacing),
                  
                  // Notifications List
                  Expanded(
                    child: _filteredNotifications.isEmpty
                        ? _buildEmptyState(fontSize, titleFontSize)
                        : ListView.builder(
                            padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
                            itemCount: _filteredNotifications.length,
                            itemBuilder: (context, index) {
                              final notification = _filteredNotifications[index];
                              return _buildNotificationCard(
                                notification,
                                cardPadding,
                                spacing,
                                fontSize,
                                titleFontSize,
                              );
                            },
                          ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildFilterChips(double fontSize) {
    final filters = [
      {'value': 'all', 'label': 'الكل', 'icon': Icons.list},
      {'value': 'unread', 'label': 'غير مقروءة', 'icon': Icons.mark_email_unread},
      {'value': 'read', 'label': 'مقروءة', 'icon': Icons.mark_email_read},
    ];

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: filters.map((filter) {
          final isSelected = _selectedFilter == filter['value'];
          return Container(
            margin: const EdgeInsets.only(right: 8),
            child: FilterChip(
              selected: isSelected,
              label: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    filter['icon'] as IconData,
                    size: fontSize,
                    color: isSelected ? Colors.white : AppColors.studentPrimary,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    filter['label'] as String,
                    style: TextStyle(
                      fontSize: fontSize,
                      color: isSelected ? Colors.white : AppColors.studentPrimary,
                    ),
                  ),
                ],
              ),
              onSelected: (selected) {
                if (selected) {
                  _handleFilterChange(filter['value'] as String);
                }
              },
              selectedColor: AppColors.studentPrimary,
              backgroundColor: Colors.white,
              side: BorderSide(color: AppColors.studentPrimary),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildStatistics(double fontSize, double titleFontSize) {
    final totalCount = _notifications.length;
    final unreadCount = _notifications.where((n) => !n.isRead).length;
    final readCount = _notifications.where((n) => n.isRead).length;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildStatItem(
          'المجموع',
          totalCount.toString(),
          Icons.list,
          Colors.blue,
          fontSize,
          titleFontSize,
        ),
        _buildStatItem(
          'غير مقروءة',
          unreadCount.toString(),
          Icons.mark_email_unread,
          Colors.orange,
          fontSize,
          titleFontSize,
        ),
        _buildStatItem(
          'مقروءة',
          readCount.toString(),
          Icons.mark_email_read,
          Colors.green,
          fontSize,
          titleFontSize,
        ),
      ],
    );
  }

  Widget _buildStatItem(
    String label,
    String value,
    IconData icon,
    Color color,
    double fontSize,
    double titleFontSize,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: titleFontSize * 1.2),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: titleFontSize,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: fontSize * 0.9,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState(double fontSize, double titleFontSize) {
    String message;
    IconData icon;
    
    switch (_selectedFilter) {
      case 'read':
        message = 'لا توجد إشعارات مقروءة';
        icon = Icons.mark_email_read;
        break;
      case 'unread':
        message = 'لا توجد إشعارات غير مقروءة';
        icon = Icons.mark_email_unread;
        break;
      default:
        message = 'لا توجد إشعارات';
        icon = Icons.notifications_none;
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: titleFontSize * 4,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: titleFontSize,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'اسحب للأسفل للتحديث',
            style: TextStyle(
              fontSize: fontSize,
              color: Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationCard(
    AppNotification notification,
    double cardPadding,
    double spacing,
    double fontSize,
    double titleFontSize,
  ) {
    return Container(
      margin: EdgeInsets.only(bottom: spacing),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: notification.isRead
            ? null
            : Border.all(color: AppColors.studentPrimary.withValues(alpha: 0.3), width: 2),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _handleNotificationTap(notification),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: EdgeInsets.all(cardPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Read status indicator
                  Container(
                    width: 8,
                    height: 8,
                    margin: EdgeInsets.only(top: fontSize * 0.5),
                    decoration: BoxDecoration(
                      color: notification.isRead
                          ? Colors.grey.shade300
                          : AppColors.studentPrimary,
                      shape: BoxShape.circle,
                    ),
                  ),
                  SizedBox(width: cardPadding * 0.8),

                  // Content
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Title
                        Text(
                          notification.title,
                          style: TextStyle(
                            fontSize: titleFontSize,
                            fontWeight: notification.isRead
                                ? FontWeight.w600
                                : FontWeight.bold,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        SizedBox(height: cardPadding * 0.3),

                        // Message preview
                        Text(
                          notification.content,
                          style: TextStyle(
                            fontSize: fontSize,
                            color: AppColors.textSecondary,
                            height: 1.4,
                          ),
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                        ),
                        SizedBox(height: cardPadding * 0.5),

                        // Footer info
                        Row(
                          children: [
                            // Priority badge
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: _getPriorityColor(notification.priority).withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                _getPriorityLabel(notification.priority),
                                style: TextStyle(
                                  fontSize: fontSize * 0.8,
                                  color: _getPriorityColor(notification.priority),
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                            const Spacer(),

                            // Date
                            Text(
                              _formatDate(notification.createdAt),
                              style: TextStyle(
                                fontSize: fontSize * 0.8,
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),

                        // Attachments indicator
                        if (notification.hasAttachment) ...[
                          SizedBox(height: cardPadding * 0.3),
                          Row(
                            children: [
                              Icon(
                                Icons.attach_file,
                                size: fontSize,
                                color: AppColors.studentPrimary,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '${notification.attachmentCount} مرفق',
                                style: TextStyle(
                                  fontSize: fontSize * 0.8,
                                  color: AppColors.studentPrimary,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),

                  // Action menu
                  PopupMenuButton<String>(
                    onSelected: (value) => _handleNotificationAction(notification, value),
                    itemBuilder: (context) => [
                      if (!notification.isRead)
                        const PopupMenuItem(
                          value: 'mark_read',
                          child: Row(
                            children: [
                              Icon(Icons.mark_email_read, color: Colors.green),
                              SizedBox(width: 8),
                              Text('تحديد كمقروء'),
                            ],
                          ),
                        ),
                      if (notification.isRead)
                        const PopupMenuItem(
                          value: 'mark_unread',
                          child: Row(
                            children: [
                              Icon(Icons.mark_email_unread, color: Colors.orange),
                              SizedBox(width: 8),
                              Text('تحديد كغير مقروء'),
                            ],
                          ),
                        ),
                      const PopupMenuItem(
                        value: 'view_details',
                        child: Row(
                          children: [
                            Icon(Icons.visibility, color: Colors.blue),
                            SizedBox(width: 8),
                            Text('عرض التفاصيل'),
                          ],
                        ),
                      ),
                    ],
                    child: Icon(
                      Icons.more_vert,
                      color: AppColors.textSecondary,
                      size: fontSize * 1.2,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'high':
      case 'عالية':
        return Colors.red;
      case 'medium':
      case 'متوسطة':
        return Colors.orange;
      case 'low':
      case 'منخفضة':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  String _getPriorityLabel(String priority) {
    switch (priority.toLowerCase()) {
      case 'high':
        return 'عالية';
      case 'medium':
        return 'متوسطة';
      case 'low':
        return 'منخفضة';
      default:
        return priority;
    }
  }

  String _formatDate(DateTime? date) {
    if (date == null) return '';

    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        return 'منذ ${difference.inMinutes} دقيقة';
      }
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays == 1) {
      return 'أمس';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} أيام';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  void _handleFilterChange(String filter) {
    setState(() {
      _selectedFilter = filter;
    });
  }

  void _handleNotificationTap(AppNotification notification) {
    // Mark as read if not already read
    if (!notification.isRead) {
      _markAsRead(notification);
    }

    // Show notification details
    _showNotificationDetails(notification);
  }

  void _handleNotificationAction(AppNotification notification, String action) {
    switch (action) {
      case 'mark_read':
        _markAsRead(notification);
        break;
      case 'mark_unread':
        _markAsUnread(notification);
        break;
      case 'view_details':
        _showNotificationDetails(notification);
        break;
    }
  }

  Future<void> _markAsRead(AppNotification notification) async {
    try {
      await StudentService.markNotificationAsRead(notification.id);

      // Update the notification in the list
      final index = _notifications.indexWhere((n) => n.id == notification.id);
      if (index != -1) {
        setState(() {
          _notifications[index] = _notifications[index].copyWith(status: 'read');
        });
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديد الإشعار كمقروء'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث الإشعار: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _markAsUnread(AppNotification notification) async {
    try {
      await StudentService.markNotificationAsUnread(notification.id);

      // Update the notification in the list
      final index = _notifications.indexWhere((n) => n.id == notification.id);
      if (index != -1) {
        setState(() {
          _notifications[index] = _notifications[index].copyWith(status: 'unread');
        });
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديد الإشعار كغير مقروء'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث الإشعار: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showNotificationDetails(AppNotification notification) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(notification.title),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(notification.content),
              if (notification.hasAttachment) ...[
                const SizedBox(height: 16),
                const Text(
                  'المرفقات:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                ...notification.allAttachments.map((attachment) =>
                  ListTile(
                    leading: const Icon(Icons.attach_file),
                    title: Text(attachment['name'] ?? ''),
                    dense: true,
                    onTap: () async {
                      await AttachmentService.openAttachment(context, attachment);
                    },
                  ),
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }
}
