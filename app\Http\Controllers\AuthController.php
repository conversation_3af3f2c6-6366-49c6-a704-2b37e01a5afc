<?php

namespace App\Http\Controllers;

use App\Models\Student;
use App\Models\Employee;
use App\Models\User;
use App\Models\Notification;
use App\Models\NotificationRecipient;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Cache;

class AuthController extends Controller
{
    /**
     * Show the login form
     */
    public function showLoginForm()
    {
        return view('login');
    }

    /**
     * Handle the login request
     */
    public function login(Request $request)
    {
        $credentials = $request->validate([
            'username' => ['required', 'string'],
            'password' => ['required'],
        ]);

        $identifier = $credentials['username'];
        $password = $credentials['password'];
        
        // Log authentication attempt for debugging
        \Log::info('Login attempt', ['identifier' => $identifier]);

        // Try to authenticate as admin (using username, name, or email)
        $admin = User::authenticate($identifier, $password);

        if ($admin) {
            Auth::guard('web')->login($admin, $request->filled('remember'));
            $request->session()->regenerate();
            return redirect()->route('dashboard');
        }

        // Skip email check for employees since they don't have email field
        // Employees use username or phone for authentication
        
        // Try to authenticate with student username (only active students)
        $student = Student::where('username', $identifier)
                          ->where('is_active', true)
                          ->first();

        // Log student lookup result
        \Log::info('Student lookup result', [
            'found' => $student ? 'yes' : 'no',
            'active' => $student ? $student->is_active : 'n/a'
        ]);

        if ($student) {
            // Check password directly
            if (Hash::check($password, $student->password)) {
                // Log successful student password check
                \Log::info('Student password check passed');

                // Use the specific student guard
                Auth::guard('student')->login($student, $request->filled('remember'));
                $request->session()->regenerate();
                return redirect()->route('student.dashboard');
            } else {
                // Log failed password check
                \Log::info('Student password check failed');
            }
        }
        
        // Try to authenticate with employee username
        $employee = Employee::where('username', $identifier)->first();
        
        // Log employee lookup result
        \Log::info('Employee lookup result', [
            'found' => $employee ? 'yes' : 'no',
            'status' => $employee ? $employee->job_status : 'n/a'
        ]);
        
        if ($employee) {
            // Check password
            if (Hash::check($password, $employee->password)) {
                // Log successful employee password check
                \Log::info('Employee password check passed');
                
                // Use the employee guard
                Auth::guard('employee')->login($employee, $request->filled('remember'));
                $request->session()->regenerate();
                return redirect()->route('employee.dashboard');
            } else {
                // Log failed password check
                \Log::info('Employee password check failed');
            }
        }
        
        // If username authentication fails, try with phone number for backward compatibility (only active students)
        if (is_numeric($identifier)) {
            $student = Student::where('phone', $identifier)
                             ->where('is_active', true)
                             ->first();

            // Log phone lookup result
            \Log::info('Student phone lookup result', [
                'found' => $student ? 'yes' : 'no',
                'active' => $student ? $student->is_active : 'n/a'
            ]);

            if ($student && Hash::check($password, $student->password)) {
                // Log successful phone authentication
                \Log::info('Student phone authentication passed');

                // Use the specific student guard
                Auth::guard('student')->login($student, $request->filled('remember'));
                $request->session()->regenerate();
                return redirect()->route('student.dashboard');
            }
            
            // Try employee phone login
            $employee = Employee::where('phone', $identifier)->first();
            
            if ($employee && Hash::check($password, $employee->password)) {
                // Log successful employee phone authentication
                \Log::info('Employee phone authentication passed');
                
                // Use the employee guard
                Auth::guard('employee')->login($employee, $request->filled('remember'));
                $request->session()->regenerate();
                return redirect()->route('employee.dashboard');
            }
        }

        return back()->withErrors([
            'username' => 'The provided credentials do not match our records.',
        ])->onlyInput('username');
    }
    
    /**
     * Show the dashboard page
     */
    public function dashboard()
    {
        // Determine if user is logged in as a student
        $isStudent = Auth::guard('student')->check();
        $user = $isStudent ? Auth::guard('student')->user() : Auth::user();
        
        // Cache employee counts for 5 minutes to improve dashboard performance
        $employeeTotal = Cache::remember('employee_count_total', 300, function() {
            return Employee::count();
        });
        
        $employeeActive = Cache::remember('employee_count_active', 300, function() {
            return Employee::where('job_status', 'Active')->count();
        });
        
        // Cache notification counts for 5 minutes
        $notificationTotal = Cache::remember('notification_count_total', 300, function() {
            return Notification::count();
        });
        
        $notificationRecent = Cache::remember('notification_count_recent', 60, function() {
            return Notification::where('created_at', '>=', now()->subDays(7))->count();
        });
        
        // For students, filter notifications that are relevant to them
        $unreadCount = $isStudent
            ? NotificationRecipient::where('recipient_id', $user->id)
                ->where('recipient_type', 'student')
                ->whereNull('read_at')
                ->count()
            : NotificationRecipient::whereNull('read_at')->count();
        
        $stats = [
            'students' => [
                'total' => Student::count(),
                'active' => Student::where('is_active', true)->count(),
                'inactive' => Student::where('is_active', false)->count(),
            ],
            'employees' => [
                'total' => $employeeTotal,
                'active' => $employeeActive,
                'inactive' => $employeeTotal - $employeeActive
            ],
            'notifications' => [
                'total' => $notificationTotal,
                'recent' => $notificationRecent,
                'unread' => $unreadCount
            ],
            'api_requests' => DB::table('api_logs')->count() ?? 0,
            'recent_requests' => DB::table('api_logs')
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get() ?? []
        ];
        
        return view('dashboard', compact('stats', 'isStudent', 'user'));
    }

    /**
     * Handle the logout request
     */
    public function logout(Request $request)
    {
        // Check which guard the user is authenticated with and logout accordingly
        if (Auth::guard('web')->check()) {
            Auth::guard('web')->logout();
        }
        
        if (Auth::guard('student')->check()) {
            Auth::guard('student')->logout();
        }
        
        if (Auth::guard('employee')->check()) {
            Auth::guard('employee')->logout();
        }

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect('/');
    }
}
