// Simple Service Worker for Testing
console.log('🔧 Simple Service Worker loaded');

const CACHE_NAME = 'appnote-simple-v1';

// Install event
self.addEventListener('install', event => {
    console.log('🔧 Simple Service Worker: Installing...');
    
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => {
                console.log('📦 Simple Service Worker: Cache opened');
                return cache.addAll([
                    '/',
                    '/manifest.json'
                ]);
            })
            .then(() => {
                console.log('✅ Simple Service Worker: Installation complete');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('❌ Simple Service Worker: Installation failed', error);
            })
    );
});

// Activate event
self.addEventListener('activate', event => {
    console.log('🔧 Simple Service Worker: Activating...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== CACHE_NAME) {
                            console.log('🗑️ Simple Service Worker: Deleting old cache', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('✅ Simple Service Worker: Activation complete');
                return self.clients.claim();
            })
    );
});

// Fetch event
self.addEventListener('fetch', event => {
    console.log('🌐 Simple Service Worker: Fetch event for', event.request.url);
    
    // Only handle GET requests
    if (event.request.method !== 'GET') {
        return;
    }
    
    event.respondWith(
        caches.match(event.request)
            .then(response => {
                if (response) {
                    console.log('📦 Simple Service Worker: Serving from cache', event.request.url);
                    return response;
                }
                
                console.log('🌐 Simple Service Worker: Fetching from network', event.request.url);
                return fetch(event.request);
            })
            .catch(error => {
                console.error('❌ Simple Service Worker: Fetch failed', error);
                return new Response('Offline', {
                    status: 503,
                    statusText: 'Service Unavailable'
                });
            })
    );
});

// Message handling
self.addEventListener('message', event => {
    console.log('📨 Simple Service Worker: Message received', event.data);
    
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
});
