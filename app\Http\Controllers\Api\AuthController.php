<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Student;
use App\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use <PERSON><PERSON>\JWTAuth\Facades\JWTAuth;
use Ty<PERSON>\JWTAuth\Exceptions\JWTException;

class AuthController extends Controller
{
    /**
     * Create a new AuthController instance.
     */
    public function __construct()
    {
        $this->middleware('auth:api', ['except' => ['adminLogin', 'studentLogin', 'employeeLogin']]);
    }

    /**
     * Admin login
     */
    public function adminLogin(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'username' => 'required|string',
            'password' => 'required|string|min:3',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        // Use the new authenticate method that supports username, name, or email
        $user = User::authenticate($request->username, $request->password);

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid credentials'
            ], 401);
        }

        try {
            if (!$token = JWTAuth::fromUser($user)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Could not create token'
                ], 500);
            }
        } catch (JWTException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Could not create token'
            ], 500);
        }

        return response()->json([
            'success' => true,
            'message' => 'Admin login successful',
            'data' => [
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                ],
                'token' => $token,
                'token_type' => 'bearer',
                'expires_in' => JWTAuth::factory()->getTTL() * 60
            ]
        ]);
    }

    /**
     * Student login
     */
    public function studentLogin(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'identifier' => 'required|string',
            'password' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        // Try to authenticate by username first, then by phone
        $student = Student::authenticate($request->identifier, $request->password);

        if (!$student) {
            $student = Student::authenticateByPhone($request->identifier, $request->password);
        }

        if (!$student) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid credentials'
            ], 401);
        }

        try {
            if (!$token = JWTAuth::fromUser($student)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Could not create token'
                ], 500);
            }
        } catch (JWTException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Could not create token'
            ], 500);
        }

        return response()->json([
            'success' => true,
            'message' => 'Student login successful',
            'data' => [
                'student' => [
                    'id' => $student->id,
                    'username' => $student->username,
                    'full_name' => $student->full_name,
                    'phone' => $student->phone,
                    'class' => $student->class,
                    'nationality' => $student->nationality,
                    'specialization' => $student->specialization,
                    'section' => $student->section,
                    'level' => $student->level,
                    'result' => $student->result,
                    'is_active' => $student->is_active,
                ],
                'token' => $token,
                'token_type' => 'bearer',
                'expires_in' => JWTAuth::factory()->getTTL() * 60
            ]
        ]);
    }

    /**
     * Employee login
     */
    public function employeeLogin(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'identifier' => 'required|string',
            'password' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $employee = Employee::authenticate($request->identifier, $request->password);

        if (!$employee) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid credentials'
            ], 401);
        }

        try {
            if (!$token = JWTAuth::fromUser($employee)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Could not create token'
                ], 500);
            }
        } catch (JWTException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Could not create token'
            ], 500);
        }

        return response()->json([
            'success' => true,
            'message' => 'Employee login successful',
            'data' => [
                'employee' => [
                    'id' => $employee->id,
                    'username' => $employee->username,
                    'full_name' => $employee->full_name,
                    'phone' => $employee->phone,
                    'contract_type' => $employee->contract_type,
                    'employee_type' => $employee->employee_type,
                    'job_status' => $employee->job_status,
                    'automatic_number' => $employee->automatic_number,
                    'financial_number' => $employee->financial_number,
                    'state_cooperative_number' => $employee->state_cooperative_number,
                    'bank_account_number' => $employee->bank_account_number,
                ],
                'token' => $token,
                'token_type' => 'bearer',
                'expires_in' => JWTAuth::factory()->getTTL() * 60
            ]
        ]);
    }

    /**
     * Logout
     */
    public function logout()
    {
        try {
            JWTAuth::invalidate(JWTAuth::getToken());

            return response()->json([
                'success' => true,
                'message' => 'Successfully logged out'
            ]);
        } catch (JWTException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to logout'
            ], 500);
        }
    }
}
