@extends('layouts.app')

@section('content')
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 style="color: var(--costa-del-sol);">Notification Details</h2>
        <div>
            <a href="{{ route('notifications.index') }}" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left"></i> Back to Notifications
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header" style="background-color: var(--thistle-green);">
                    <h5 style="color: var(--costa-del-sol);">Notification Content</h5>
                </div>
                <div class="card-body">
                    <h3>{{ $notification->title }}</h3>
                    <div class="d-flex justify-content-between align-items-center my-3">
                        <span class="badge bg-{{ $notification->priority === 'high' ? 'danger' : ($notification->priority === 'medium' ? 'warning' : 'info') }} px-3 py-2">
                            {{ ucfirst($notification->priority) }} Priority
                        </span>
                        <small class="text-muted">Sent: {{ $notification->created_at->format('Y-m-d H:i') }}</small>
                    </div>
                    <hr>
                    <div class="notification-message p-3 bg-light rounded">
                        {{ $notification->message }}
                    </div>
                    
                    @if($notification->attachment_path)
                    <div class="mt-4">
                        <h6>Attachments:</h6>
                        <div>
                            @php
                                // Since we added cast to array in model, these should be arrays now
                                $paths = $notification->attachment_path ?? [];
                                $names = $notification->attachment_name ?? [];

                                // Ensure they are arrays (fallback for old data)
                                if (!is_array($paths)) {
                                    $paths = [$paths];
                                }
                                if (!is_array($names)) {
                                    $names = [$names];
                                }
                            @endphp
                            
                            @for($i = 0; $i < count($paths); $i++)
                                @php
                                    $path = $paths[$i] ?? '';
                                    $name = $names[$i] ?? 'Attachment ' . ($i + 1);
                                    
                                    // Handle path formatting for proper URL generation
                                    if (strpos($path, 'public/') === 0) {
                                        $path = substr($path, 7); // Remove 'public/' prefix if present
                                    }
                                    
                                    // Create direct download URL
                                    $downloadUrl = asset('storage/' . $path);
                                    error_log('Download URL for ' . $name . ': ' . $downloadUrl);
                                @endphp
                                <div class="mb-2">
                                    <a href="{{ $downloadUrl }}" class="btn btn-sm btn-outline-primary" download="{{ $name }}" target="_blank">
                                        <i class="fas fa-download"></i> {{ $name }}
                                    </a>
                                    <!-- Direct link as fallback -->
                                    <a href="/storage/{{ $path }}" class="btn btn-sm btn-light ms-2" target="_blank">
                                        <i class="fas fa-external-link-alt"></i> View
                                    </a>
                                </div>
                            @endfor
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header" style="background-color: var(--coral-reef);">
                    <h5 style="color: var(--costa-del-sol);">Notification Info</h5>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between">
                            <span>Status:</span>
                            <span class="badge bg-{{ $notification->status === 'sent' ? 'success' : ($notification->status === 'draft' ? 'secondary' : 'danger') }}">
                                {{ ucfirst($notification->status) }}
                            </span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            <span>Sender:</span>
                            <span>{{ $notification->sender_name }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            <span>Recipient Group:</span>
                            <span>{{ ucfirst(str_replace('_', ' ', $notification->recipient_type)) }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between">
                            <span>Recipients Count:</span>
                            @php
                                $recipientIds = $notification->recipient_ids ?? [];
                                if (!is_array($recipientIds)) {
                                    $recipientIds = is_string($recipientIds) ? json_decode($recipientIds, true) ?? [] : [];
                                }
                            @endphp
                            <span class="badge bg-primary">{{ count($recipientIds) }}</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recipients List -->
    <div class="card mt-3">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0" style="color: var(--costa-del-sol);">Recipients</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-sm table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Type</th>
                            <th>Name</th>
                            <th>Status</th>
                            <th>Read At</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($recipients as $recipient)
                            <tr>
                                <td>{{ $recipient->recipient_id }}</td>
                                <td>{{ ucfirst($recipient->recipient_type) }}</td>
                                <td>
                                    @if($recipient->recipient_type == 'student')
                                        {{ \App\Models\Student::find($recipient->recipient_id)->full_name ?? 'Unknown Student' }}
                                    @elseif($recipient->recipient_type == 'employee')
                                        {{ \App\Models\Employee::find($recipient->recipient_id)->full_name ?? 'Unknown Employee' }}
                                    @else
                                        Unknown
                                    @endif
                                </td>
                                <td>
                                    @if($recipient->read_at)
                                        <span class="badge bg-success">Read</span>
                                    @else
                                        <span class="badge bg-secondary">Unread</span>
                                    @endif
                                </td>
                                <td>{{ $recipient->read_at ? $recipient->read_at->format('Y-m-d H:i') : 'Not read yet' }}</td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="5" class="text-center">No recipients found</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
            <div class="d-flex justify-content-center mt-4">
                {{ $recipients->links('pagination::bootstrap-5') }}
            </div>
        </div>
    </div>
</div>
@endsection
