@php
use Illuminate\Support\Facades\Auth;
@endphp
@extends('layouts.app')

@section('styles')
<style>
    .notifications-table {
        border-collapse: separate;
        border-spacing: 0;
        width: 100%;
        border: none;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }
    
    .notifications-table th {
        background-color: var(--thistle-green);
        color: var(--costa-del-sol);
        font-weight: 600;
        padding: 15px 20px;
        text-align: left;
        border: none;
    }
    
    .notifications-table td {
        padding: 12px 20px;
        vertical-align: middle;
        border-top: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .notifications-table tr {
        background-color: #fff;
        transition: background-color 0.2s ease;
    }
    
    .notifications-table tr:hover {
        background-color: rgba(242, 242, 242, 0.5);
    }
    
    .notifications-table tr.unread {
        background-color: rgba(230, 235, 214, 0.2);
    }
    
    .priority-badge {
        padding: 6px 12px;
        border-radius: 30px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .priority-high {
        background-color: rgba(220, 53, 69, 0.15);
        color: #dc3545;
    }
    
    .priority-medium {
        background-color: rgba(255, 193, 7, 0.15);
        color: #856404;
    }
    
    .priority-low {
        background-color: rgba(40, 167, 69, 0.15);
        color: #28a745;
    }
    
    .status-badge {
        padding: 6px 12px;
        border-radius: 30px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .status-sent {
        background-color: rgba(40, 167, 69, 0.15);
        color: #28a745;
        border: 1px solid rgba(40, 167, 69, 0.3);
    }
    
    .status-read {
        background-color: rgba(108, 117, 125, 0.15);
        color: #6c757d;
        border: 1px solid rgba(108, 117, 125, 0.3);
    }
    
    .status-unread {
        background-color: rgba(255, 193, 7, 0.15);
        color: #856404;
        border: 1px solid rgba(255, 193, 7, 0.3);
    }
    
    .view-btn {
        background-color: #17a2b8;
        color: white;
        border: none;
        border-radius: 30px;
        padding: 6px 16px;
        font-size: 0.85rem;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-block;
        cursor: pointer;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    
    .view-btn:hover {
        background-color: #138496;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        color: white;
    }
    
    .filter-btn {
        border-radius: 20px;
        background-color: white;
        border: 1px solid #e9e9e9;
        color: var(--mid-gray);
        margin-right: 0.5rem;
        transition: background-color 0.2s ease, color 0.2s ease;
    }
    
    .filter-btn:hover, .filter-btn.active {
        background-color: var(--costa-del-sol);
        color: white;
        border-color: var(--costa-del-sol);
    }
    
    .modal-content {
        border-radius: 15px;
        border: none;
    }
    
    .modal-header {
        border-bottom: 1px solid #f0f0f0;
        padding: 1.5rem;
    }
    
    .modal-footer {
        border-top: 1px solid #f0f0f0;
        padding: 1.5rem;
    }
    
    .modal-body {
        padding: 1.5rem;
    }
    
    .modal-title {
        color: var(--costa-del-sol);
        font-weight: 600;
    }
    
    .mark-read-btn {
        background-color: var(--costa-del-sol);
        color: white;
        border-radius: 20px;
    }
    
    .mark-read-btn:hover {
        background-color: var(--locust);
        color: white;
    }
    
    .primary-btn {
        background-color: var(--costa-del-sol);
        color: white;
        border-radius: 20px;
        padding: 0.5rem 1.5rem;
        text-decoration: none;
        transition: background-color 0.2s ease;
        border: none;
    }
    
    /* Attachment section styling */
    .attachment-section {
        border: 1px solid #eaeaea;
        border-radius: 8px;
        overflow: hidden;
        margin-top: 1.5rem;
        background-color: #f9f9f9;
    }
    
    .attachment-header {
        background-color: #f0f0f0;
        padding: 0.8rem 1rem;
        border-bottom: 1px solid #eaeaea;
    }
    
    .attachment-container {
        padding: 0.5rem 0;
    }
    
    .attachment-item {
        padding: 0.5rem 1rem;
        transition: background-color 0.2s;
        border-bottom: 1px solid #eee;
    }
    
    .attachment-item:last-child {
        border-bottom: none;
    }
    
    .attachment-item:hover {
        background-color: #f0f0f0;
    }
    
    .download-attachment-btn {
        display: flex;
        align-items: center;
        text-decoration: none;
        color: #333;
        padding: 0.5rem;
    }
    
    .attachment-icon {
        background-color: #e9ecef;
        color: var(--costa-del-sol);
        width: 40px;
        height: 40px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
    }
    
    .attachment-icon i {
        font-size: 1.25rem;
    }
    
    .attachment-details {
        display: flex;
        flex-direction: column;
    }
    
    .attachment-name {
        font-weight: 500;
        max-width: 220px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    .attachment-info {
        color: #6c757d;
        font-size: 0.75rem;
    }
    
    /* Hover effect for primary-btn */
    .primary-btn:hover {
        background-color: var(--locust);
        color: white;
    }
    
    /* Special hover effect for attachment buttons */
    .download-attachment-btn:hover .attachment-icon {
        background-color: #d4d8dc;
    }
    
    .download-attachment-btn:hover .attachment-name {
        color: var(--costa-del-sol);
    }
    
    .secondary-btn {
        background-color: white;
        color: var(--costa-del-sol);
        border-radius: 20px;
        border: 1px solid var(--costa-del-sol);
        padding: 0.5rem 1.5rem;
        text-decoration: none;
        transition: background-color 0.2s ease, color 0.2s ease;
        font-size: 0.9rem;
    }

    .secondary-btn:hover {
        background-color: var(--costa-del-sol);
        color: white;
    }
    
    .attachment-badge {
        font-size: 0.75rem;
        background-color: rgba(125, 145, 120, 0.1);
        color: var(--costa-del-sol);
        border-radius: 20px;
        padding: 0.25rem 0.5rem;
    }
    
    .pagination {
        justify-content: center;
    }
    
    .page-item.active .page-link {
        background-color: var(--costa-del-sol);
        border-color: var(--costa-del-sol);
    }
    
    .page-link {
        color: var(--costa-del-sol);
    }
    
    .download-attachment-btn {
        display: inline-flex;
        align-items: center;
        background-color: rgba(125, 145, 120, 0.1);
        color: var(--costa-del-sol);
        border-radius: 20px;
        padding: 0.5rem 1rem;
        margin-bottom: 0.5rem;
        text-decoration: none;
        transition: background-color 0.2s ease, color 0.2s ease;
    }
    
    .download-attachment-btn i {
        margin-right: 0.5rem;
    }
    
    .download-attachment-btn:hover {
        background-color: var(--costa-del-sol);
        color: white;
    }
    
    .attachment-name {
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: inline-block;
    }
    
    /* Responsive adjustments */
    @media (max-width: 1200px) {
        .notifications-table th,
        .notifications-table td {
            padding: 10px 15px;
            font-size: 0.9rem;
        }
    }

    @media (max-width: 992px) {
        .notifications-table th,
        .notifications-table td {
            padding: 8px 12px;
            font-size: 0.85rem;
        }

        .priority-badge,
        .status-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
        }

        .view-btn {
            font-size: 0.8rem;
            padding: 4px 12px;
        }
    }

    @media (max-width: 768px) {
        .container-fluid.main-content {
            padding-left: 1rem !important;
            padding-right: 1rem !important;
        }

        /* Header responsive */
        .d-flex.justify-content-between.align-items-center {
            flex-direction: column;
            align-items: flex-start !important;
            gap: 1rem;
        }

        .d-flex.justify-content-between.align-items-center > div:last-child {
            align-self: flex-end;
        }

        /* Make secondary button smaller on mobile */
        .secondary-btn {
            padding: 0.4rem 1rem !important;
            font-size: 0.8rem !important;
            border-radius: 15px !important;
        }

        .secondary-btn i {
            font-size: 0.75rem;
        }

        /* Filter section responsive */
        .d-flex.flex-wrap {
            flex-direction: column !important;
            gap: 1rem;
        }

        .filter-btn {
            margin-bottom: 0.5rem;
            margin-right: 0.25rem;
            font-size: 0.8rem;
            padding: 0.4rem 0.8rem;
        }

        /* Table responsive - hide less important columns */
        .notifications-table th:nth-child(1), /* ID */
        .notifications-table td:nth-child(1) {
            display: none;
        }

        .notifications-table th:nth-child(4), /* Recipient Group */
        .notifications-table td:nth-child(4) {
            display: none;
        }

        .notifications-table th,
        .notifications-table td {
            padding: 8px 6px;
            font-size: 0.8rem;
        }

        .notifications-table th:nth-child(2), /* Title */
        .notifications-table td:nth-child(2) {
            max-width: 120px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .notifications-table th:nth-child(5), /* Date */
        .notifications-table td:nth-child(5) {
            font-size: 0.7rem;
        }

        .priority-badge,
        .status-badge {
            font-size: 0.7rem;
            padding: 3px 6px;
        }

        .view-btn {
            font-size: 0.7rem;
            padding: 3px 8px;
        }

        .mark-read-btn {
            font-size: 0.7rem;
            padding: 3px 8px;
            margin-top: 2px;
        }

        .attachment-name {
            max-width: 150px;
        }

        /* Modal responsive */
        .modal-dialog {
            margin: 0.5rem;
        }

        .modal-header,
        .modal-body,
        .modal-footer {
            padding: 1rem;
        }
    }

    @media (max-width: 576px) {
        .container-fluid.main-content {
            padding-left: 0.5rem !important;
            padding-right: 0.5rem !important;
        }

        /* Further hide columns on very small screens */
        .notifications-table th:nth-child(3), /* Priority */
        .notifications-table td:nth-child(3) {
            display: none;
        }

        .notifications-table th:nth-child(5), /* Date */
        .notifications-table td:nth-child(5) {
            display: none;
        }

        /* Make remaining columns wider */
        .notifications-table th:nth-child(2), /* Title */
        .notifications-table td:nth-child(2) {
            max-width: 150px;
        }

        .notifications-table th:nth-child(6), /* Status */
        .notifications-table td:nth-child(6) {
            width: 80px;
        }

        .notifications-table th:nth-child(7), /* Actions */
        .notifications-table td:nth-child(7) {
            width: 60px;
        }

        /* Stack action buttons vertically */
        .notifications-table td:nth-child(7) {
            white-space: normal;
        }

        .notifications-table td:nth-child(7) .view-btn,
        .notifications-table td:nth-child(7) .mark-read-btn {
            display: block;
            width: 100%;
            margin-bottom: 2px;
            text-align: center;
        }

        /* Filter buttons stack better */
        .filter-btn {
            display: inline-block;
            width: auto;
            margin: 0.2rem 0.2rem 0.2rem 0;
        }

        /* Page title smaller */
        h2 {
            font-size: 1.5rem !important;
        }

        /* Even smaller button for very small screens */
        .secondary-btn {
            padding: 0.3rem 0.8rem !important;
            font-size: 0.75rem !important;
            border-radius: 12px !important;
        }

        .secondary-btn i {
            font-size: 0.7rem;
        }

        /* Pagination responsive */
        .pagination {
            flex-wrap: wrap;
            justify-content: center;
        }

        .page-item {
            margin: 0.1rem;
        }

        .page-link {
            padding: 0.3rem 0.6rem;
            font-size: 0.8rem;
        }
    }

    /* Card view for mobile - alternative layout */
    @media (max-width: 768px) {
        .notifications-table-wrapper {
            display: none !important;
        }

        .notifications-card-view {
            display: block !important;
            background: transparent;
        }

        .notification-card {
            background: #ffffff !important;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            margin-bottom: 1rem;
            padding: 1.25rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid var(--costa-del-sol);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            position: relative;
            width: 100%;
        }

        .notification-card.unread {
            background-color: rgba(230, 235, 214, 0.3) !important;
            border-left-color: var(--locust);
            border-left-width: 5px;
        }

        .notification-card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 0.75rem;
        }

        .notification-card-title {
            font-weight: 600;
            color: var(--costa-del-sol) !important;
            margin-bottom: 0.25rem;
            flex: 1;
            margin-right: 0.75rem;
            font-size: 1rem;
            line-height: 1.4;
        }

        .notification-card-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 0.75rem;
            margin-bottom: 1rem;
            font-size: 0.85rem;
            color: var(--mid-gray);
        }

        .notification-card-actions {
            display: flex;
            gap: 0.75rem;
            flex-wrap: wrap;
        }

        .notification-card-actions .view-btn,
        .notification-card-actions .mark-read-btn {
            flex: 1;
            text-align: center;
            min-width: 100px;
            padding: 0.5rem 1rem;
            font-size: 0.85rem;
        }
    }

    /* Further refinements for very small screens */
    @media (max-width: 480px) {
        .notification-card {
            padding: 1rem;
            margin-bottom: 0.75rem;
        }

        .notification-card-title {
            font-size: 0.95rem;
        }

        .notification-card-meta {
            font-size: 0.8rem;
            gap: 0.5rem;
        }

        .notification-card-actions {
            gap: 0.5rem;
        }

        .notification-card-actions .view-btn,
        .notification-card-actions .mark-read-btn {
            min-width: 80px;
            padding: 0.4rem 0.8rem;
            font-size: 0.8rem;
        }
    }

    /* Default display settings */
    .notifications-table-wrapper {
        display: block;
    }

    .notifications-card-view {
        display: none;
    }

    /* Ensure proper display on larger screens */
    @media (min-width: 769px) {
        .notifications-table-wrapper {
            display: block !important;
        }

        .notifications-card-view {
            display: none !important;
        }
    }

    /* Additional responsive improvements */
    @media (max-width: 768px) {
        .card {
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        }

        .filter-btn {
            white-space: nowrap;
        }

        .gap-2 {
            gap: 0.25rem !important;
        }

        .h3 {
            font-size: 1.4rem;
        }
    }

    @media (max-width: 576px) {
        .gap-2 {
            gap: 0.2rem !important;
        }

        .filter-btn {
            font-size: 0.75rem;
            padding: 0.35rem 0.7rem;
        }

        .filter-btn i {
            font-size: 0.7rem;
        }

        .h3 {
            font-size: 1.25rem;
        }

        .small {
            font-size: 0.8rem;
        }
    }

    /* Smooth transitions for responsive changes */
    .notifications-table,
    .notification-card,
    .filter-btn {
        transition: all 0.3s ease;
    }

    /* Improved focus states for accessibility */
    .filter-btn:focus,
    .view-btn:focus,
    .mark-read-btn:focus {
        outline: 2px solid var(--costa-del-sol);
        outline-offset: 2px;
    }

    /* Better hover states on touch devices */
    @media (hover: hover) {
        .notification-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
    }

    /* Force mobile card view visibility for debugging */
    @media (max-width: 768px) {
        .notifications-card-view {
            min-height: 200px;
            background: rgba(125, 145, 120, 0.05) !important;
            border: 2px dashed rgba(125, 145, 120, 0.3);
            border-radius: 8px;
            padding: 1rem;
        }

        .notification-card {
            border: 2px solid rgba(125, 145, 120, 0.2) !important;
            min-height: 120px;
        }

        /* Ensure text is visible */
        .notification-card * {
            color: inherit !important;
        }

        .notification-card-title {
            color: var(--costa-del-sol) !important;
            font-weight: 700 !important;
        }
    }
</style>
@endsection

@section('content')
<div class="container-fluid main-content px-4">
    <div class="row">
        <div class="col-12">
            <!-- Page Header -->
            <div class="d-flex justify-content-between align-items-start align-items-md-center mb-4 mt-2 flex-column flex-md-row gap-3 gap-md-0">
                <div class="flex-grow-1">
                    <h2 class="mb-1 h3 h2-md" style="color: var(--costa-del-sol); font-weight: 700;">
                        <i class="fas fa-bell me-2"></i> Notifications
                    </h2>
                    <p class="mb-0 text-muted small">
                        You have <span class="fw-semibold">{{ $notifications->total() }}</span> notifications
                        @if($stats['notifications']['unread'] ?? 0 > 0)
                            (<span class="fw-semibold text-warning">{{ $stats['notifications']['unread'] ?? 0 }} unread</span>)
                        @endif
                    </p>
                </div>
                <div class="align-self-end align-self-md-auto">
                    <a href="{{ route('employee.dashboard') }}" class="secondary-btn">
                        <i class="fas fa-arrow-left me-1"></i>
                        <span class="d-none d-sm-inline">Back to </span>Dashboard
                    </a>
                </div>
            </div>
            
            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 col-12 mb-3 mb-md-0">
                            <label class="mb-2 fw-semibold" style="color: var(--mid-gray);">
                                <i class="fas fa-filter me-1"></i> Status
                            </label>
                            <div class="d-flex flex-wrap gap-2">
                                <a href="{{ request()->url() }}"
                                   class="btn btn-sm filter-btn {{ !request('status') ? 'active' : '' }}">
                                   <i class="fas fa-list me-1"></i> All
                                </a>
                                <a href="{{ request()->url() }}?status=unread{{ request('date') ? '&date='.request('date') : '' }}"
                                   class="btn btn-sm filter-btn {{ request('status') == 'unread' ? 'active' : '' }}">
                                   <i class="fas fa-circle me-1" style="font-size: 8px;"></i> Unread
                                </a>
                                <a href="{{ request()->url() }}?status=read{{ request('date') ? '&date='.request('date') : '' }}"
                                   class="btn btn-sm filter-btn {{ request('status') == 'read' ? 'active' : '' }}">
                                   <i class="fas fa-check-circle me-1"></i> Read
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6 col-12">
                            <label class="mb-2 fw-semibold" style="color: var(--mid-gray);">
                                <i class="fas fa-calendar me-1"></i> Date
                            </label>
                            <div class="d-flex flex-wrap gap-2">
                                <a href="{{ request()->url() }}{{ request('status') ? '?status='.request('status') : '' }}"
                                   class="btn btn-sm filter-btn {{ !request('date') ? 'active' : '' }}">
                                   <i class="fas fa-clock me-1"></i> All Time
                                </a>
                                <a href="{{ request()->url() }}?{{ request('status') ? 'status='.request('status').'&' : '' }}date=today"
                                   class="btn btn-sm filter-btn {{ request('date') == 'today' ? 'active' : '' }}">
                                   <i class="fas fa-calendar-day me-1"></i> Today
                                </a>
                                <a href="{{ request()->url() }}?{{ request('status') ? 'status='.request('status').'&' : '' }}date=week"
                                   class="btn btn-sm filter-btn {{ request('date') == 'week' ? 'active' : '' }}">
                                   <i class="fas fa-calendar-week me-1"></i> This Week
                                </a>
                                <a href="{{ request()->url() }}?{{ request('status') ? 'status='.request('status').'&' : '' }}date=month"
                                   class="btn btn-sm filter-btn {{ request('date') == 'month' ? 'active' : '' }}">
                                   <i class="fas fa-calendar-alt me-1"></i> This Month
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Notifications List -->
            <div class="row">
                <div class="col-12">
                    @if($notifications->isEmpty())
                        <div class="text-center p-5">
                            <i class="far fa-bell-slash fa-4x mb-3" style="color: var(--mid-gray);"></i>
                            <h4>No Notifications Found</h4>
                            <p>You don't have any notifications matching the selected filters.</p>
                        </div>
                    @else
                        <!-- Table View (Desktop/Tablet) -->
                        <div class="card notifications-table-wrapper">
                            <div class="card-body p-0">
                                <table class="notifications-table">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Title</th>
                                            <th>Priority</th>
                                            <th>Recipient Group</th>
                                            <th>Date</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($notifications as $recipient)
                                            @php
                                                $notification = $recipient->notification;
                                                $isUnread = is_null($recipient->read_at);
                                                $isHighlighted = isset($highlightedNotificationId) && $notification->id == $highlightedNotificationId;
                                                $priority = $notification->priority ?? 'Medium';

                                                // Check if notification has attachments
                                                $hasAttachments = !empty($notification->attachments) && $notification->attachments !== '[]' && $notification->attachments !== 'null';
                                            @endphp

                                            <tr id="notification-{{ $notification->id }}" class="{{ $isUnread ? 'unread' : '' }} {{ $isHighlighted ? 'table-info' : '' }}">
                                                <td>{{ $notification->id }}</td>
                                                <td>{{ $notification->title }}</td>
                                                <td>
                                                    <span class="priority-badge priority-{{ strtolower($priority) }}">
                                                        {{ ucfirst($priority) }}
                                                    </span>
                                                </td>
                                                <td>{{ $notification->recipient_group ?? 'All employees' }}</td>
                                                <td>{{ $notification->created_at->format('Y-m-d H:i') }}</td>
                                                <td>
                                                    @if($isUnread)
                                                        <span class="status-badge status-unread">
                                                            <i class="fas fa-circle me-1" style="font-size: 8px;"></i> Unread
                                                        </span>
                                                    @else
                                                        <span class="status-badge status-read">
                                                            <i class="fas fa-check-circle me-1"></i> Read
                                                        </span>
                                                    @endif
                                                </td>
                                                <td>
                                                    <button type="button" class="view-btn" data-bs-toggle="modal" data-bs-target="#notificationModal{{ $notification->id }}">
                                                        <i class="fas fa-eye me-1"></i> View
                                                    </button>
                                                    @if($isUnread)
                                                        <form action="{{ route('employee.notifications.mark-read', $notification->id) }}" method="POST" class="d-inline">
                                                            @csrf
                                                            <button type="submit" class="mark-read-btn">
                                                                <i class="fas fa-check-circle me-1"></i> Mark Read
                                                            </button>
                                                        </form>
                                                    @endif
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>

                                <!-- Pagination -->
                                <div class="d-flex justify-content-center p-3">
                                    {{ $notifications->links() }}
                                </div>
                            </div>
                        </div>

                        <!-- Card View (Mobile) -->
                        <div class="notifications-card-view">
                            <div class="d-block d-md-none mb-3 p-2 text-center" style="background-color: var(--thistle-green); color: var(--costa-del-sol); border-radius: 8px; font-size: 0.9rem;">
                                <i class="fas fa-mobile-alt me-2"></i>Mobile View - {{ $notifications->count() }} notifications
                            </div>
                            @foreach($notifications as $recipient)
                                @php
                                    $notification = $recipient->notification;
                                    $isUnread = is_null($recipient->read_at);
                                    $priority = $notification->priority ?? 'Medium';

                                    // Check if notification has attachments
                                    $hasAttachments = !empty($notification->attachments) && $notification->attachments !== '[]' && $notification->attachments !== 'null';
                                @endphp

                                <div class="notification-card {{ $isUnread ? 'unread' : '' }}" id="notification-card-{{ $notification->id }}">
                                    <div class="notification-card-header">
                                        <div class="notification-card-title">{{ $notification->title }}</div>
                                        @if($isUnread)
                                            <span class="status-badge status-unread">
                                                <i class="fas fa-circle me-1" style="font-size: 8px;"></i> Unread
                                            </span>
                                        @else
                                            <span class="status-badge status-read">
                                                <i class="fas fa-check-circle me-1"></i> Read
                                            </span>
                                        @endif
                                    </div>

                                    <div class="notification-card-meta">
                                        <span class="priority-badge priority-{{ strtolower($priority) }}">
                                            {{ ucfirst($priority) }}
                                        </span>
                                        <span>{{ $notification->created_at->format('M d, Y H:i') }}</span>
                                        @if($hasAttachments)
                                            <span class="attachment-badge">
                                                <i class="fas fa-paperclip me-1"></i> Attachment
                                            </span>
                                        @endif
                                    </div>

                                    <div class="notification-card-actions">
                                        <button type="button" class="view-btn" data-bs-toggle="modal" data-bs-target="#notificationModal{{ $notification->id }}">
                                            <i class="fas fa-eye me-1"></i> View
                                        </button>
                                        @if($isUnread)
                                            <form action="{{ route('employee.notifications.mark-read', $notification->id) }}" method="POST" class="d-inline">
                                                @csrf
                                                <button type="submit" class="mark-read-btn">
                                                    <i class="fas fa-check-circle me-1"></i> Mark Read
                                                </button>
                                            </form>
                                        @endif
                                    </div>
                                </div>
                            @endforeach

                            <!-- Pagination for card view -->
                            <div class="d-flex justify-content-center p-3">
                                {{ $notifications->links() }}
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Notification Detail Modals -->
@foreach($notifications as $recipient)
@php
    $notification = $recipient->notification;
    $isUnread = is_null($recipient->read_at);
    
    // Check if notification has attachments
    $hasAttachments = !empty($notification->attachments) && $notification->attachments !== '[]' && $notification->attachments !== 'null';
@endphp

<!-- Modal for notification details -->
@include('employee.partials.notification_modal', [
    'notification' => $notification, 
    'isUnread' => $isUnread
])
@endforeach
@endsection

<script>
    // Handle notification click
    document.addEventListener('DOMContentLoaded', function() {
        // Process Unicode filenames
        document.querySelectorAll('.attachment-name').forEach(function(element) {
            const filename = element.getAttribute('data-filename');
            if (filename) {
                element.textContent = decodeUnicodeFilename(filename);
            }
        });
    });
    
    function decodeUnicodeFilename(filename) {
        if (!filename) return filename;
        if (filename.includes('\\u')) {
            try {
                // Try to parse as JSON string if it looks like a JSON string
                const jsonString = filename.startsWith('"') ? filename : '"' + filename.replace(/"/g, '\\"') + '"';
                const decodedName = JSON.parse(jsonString);
                if (decodedName) return decodedName;
            } catch (e) {
                console.log('First decode attempt failed:', e);
            }
            
            try {
                // Manually replace Unicode escape sequences
                const decodedName = filename.replace(/\\u([0-9a-f]{4})/gi, function(match, hex) {
                    return String.fromCharCode(parseInt(hex, 16));
                });
                return decodedName;
            } catch (e) {
                console.log('Second decode attempt failed:', e);
                return filename; // Return original if all decoding fails
            }
        }
        return filename;
    }
</script>
