# 📱 Notification API Documentation for Flutter App

## 🔗 **Base URL**
```
http://localhost/appnote-api/public/api
```

## 🔐 **Authentication**
All endpoints require Bearer token authentication:
```dart
headers: {
  'Authorization': 'Bearer $token',
  'Content-Type': 'application/json',
  'Accept': 'application/json',
}
```

---

## 📋 **API Endpoints**

### 1. **Send Notification** 📤
**POST** `/notifications`

Send a notification to specified recipients with optional file attachments.

#### **Request Body (JSON):**
```json
{
  "title": "Notification Title",
  "message": "Notification content/message",
  "sender_id": 1,
  "sender_name": "Sender Name",
  "sender_type": "admin",
  "recipient_type": "students",
  "recipient_ids": [1, 2, 3],
  "priority": "high",
  "attachment_paths": ["path1.pdf", "path2.docx"],
  "attachment_names": ["File 1.pdf", "File 2.docx"]
}
```

#### **Flutter Implementation:**
```dart
Future<Map<String, dynamic>> sendNotification({
  required String title,
  required String message,
  required int senderId,
  required String senderName,
  required String recipientType,
  required List<int> recipientIds,
  String senderType = 'admin',
  String priority = 'medium',
  List<String>? attachmentPaths,
  List<String>? attachmentNames,
}) async {
  final response = await http.post(
    Uri.parse('$baseUrl/notifications'),
    headers: {
      'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
    body: jsonEncode({
      'title': title,
      'message': message,
      'sender_id': senderId,
      'sender_name': senderName,
      'sender_type': senderType,
      'recipient_type': recipientType,
      'recipient_ids': recipientIds,
      'priority': priority,
      if (attachmentPaths != null) 'attachment_paths': attachmentPaths,
      if (attachmentNames != null) 'attachment_names': attachmentNames,
    }),
  );

  if (response.statusCode == 201) {
    return jsonDecode(response.body);
  } else {
    throw Exception('Failed to send notification: ${response.body}');
  }
}
```

#### **Success Response (201):**
```json
{
  "success": true,
  "message": "Notification sent successfully",
  "data": {
    "id": 36,
    "title": "Notification Title",
    "message": "Notification content",
    "sender_type": "admin",
    "sender_id": 1,
    "sender_name": "Sender Name",
    "recipient_type": "students",
    "recipient_ids": [1, 2, 3],
    "attachment_path": ["path1.pdf", "path2.docx"],
    "attachment_name": ["File 1.pdf", "File 2.docx"],
    "priority": "high",
    "status": "sent",
    "created_at": "2025-06-16T19:24:59.000000Z",
    "updated_at": "2025-06-16T19:24:59.000000Z"
  }
}
```

---

### 2. **Send Notification with File Upload** 📎
**POST** `/notifications` (Multipart Form Data)

Upload actual files with the notification.

#### **Flutter Implementation:**
```dart
Future<Map<String, dynamic>> sendNotificationWithFiles({
  required String title,
  required String message,
  required int senderId,
  required String senderName,
  required String recipientType,
  required List<int> recipientIds,
  required List<File> attachments,
  String senderType = 'admin',
  String priority = 'medium',
}) async {
  var request = http.MultipartRequest(
    'POST',
    Uri.parse('$baseUrl/notifications'),
  );

  // Add headers
  request.headers.addAll({
    'Authorization': 'Bearer $token',
    'Accept': 'application/json',
  });

  // Add form fields
  request.fields.addAll({
    'title': title,
    'message': message,
    'sender_id': senderId.toString(),
    'sender_name': senderName,
    'sender_type': senderType,
    'recipient_type': recipientType,
    'priority': priority,
  });

  // Add recipient_ids as array
  for (int i = 0; i < recipientIds.length; i++) {
    request.fields['recipient_ids[$i]'] = recipientIds[i].toString();
  }

  // Add files
  for (File file in attachments) {
    request.files.add(await http.MultipartFile.fromPath(
      'attachments[]',
      file.path,
      filename: basename(file.path),
    ));
  }

  var response = await request.send();
  var responseBody = await response.stream.bytesToString();

  if (response.statusCode == 201) {
    return jsonDecode(responseBody);
  } else {
    throw Exception('Failed to upload files: $responseBody');
  }
}
```

---

### 3. **Get All Notifications** 📋
**GET** `/notifications`

Retrieve all notifications with pagination support.

#### **Flutter Implementation:**
```dart
Future<List<Map<String, dynamic>>> getAllNotifications({
  int page = 1,
  int perPage = 15,
}) async {
  final response = await http.get(
    Uri.parse('$baseUrl/notifications?page=$page&per_page=$perPage'),
    headers: {
      'Authorization': 'Bearer $token',
      'Accept': 'application/json',
    },
  );

  if (response.statusCode == 200) {
    final data = jsonDecode(response.body);
    return List<Map<String, dynamic>>.from(data['data']);
  } else {
    throw Exception('Failed to fetch notifications: ${response.body}');
  }
}
```

#### **Success Response (200):**
```json
{
  "success": true,
  "message": "Notifications retrieved successfully",
  "data": [
    {
      "id": 36,
      "title": "Notification Title",
      "message": "Notification content",
      "sender_name": "Sender Name",
      "recipient_type": "students",
      "attachment_path": ["path1.pdf"],
      "attachment_name": ["File 1.pdf"],
      "priority": "high",
      "status": "sent",
      "created_at": "2025-06-16T19:24:59.000000Z"
    }
  ]
}
```

---

### 4. **Get Single Notification** 🔍
**GET** `/notifications/{id}`

Retrieve details of a specific notification.

#### **Flutter Implementation:**
```dart
Future<Map<String, dynamic>> getNotification(int id) async {
  final response = await http.get(
    Uri.parse('$baseUrl/notifications/$id'),
    headers: {
      'Authorization': 'Bearer $token',
      'Accept': 'application/json',
    },
  );

  if (response.statusCode == 200) {
    return jsonDecode(response.body);
  } else {
    throw Exception('Failed to fetch notification: ${response.body}');
  }
}
```

---

### 5. **Delete Notification** 🗑️
**DELETE** `/notifications/{id}`

Delete a specific notification.

#### **Flutter Implementation:**
```dart
Future<bool> deleteNotification(int id) async {
  final response = await http.delete(
    Uri.parse('$baseUrl/notifications/$id'),
    headers: {
      'Authorization': 'Bearer $token',
      'Accept': 'application/json',
    },
  );

  return response.statusCode == 200;
}
```

---

### 6. **Bulk Delete Notifications** 🗑️📦
**DELETE** `/notifications/bulk-delete`

Delete multiple notifications at once.

#### **Request Body:**
```json
{
  "notification_ids": [1, 2, 3, 4]
}
```

#### **Flutter Implementation:**
```dart
Future<Map<String, dynamic>> bulkDeleteNotifications(List<int> ids) async {
  final response = await http.delete(
    Uri.parse('$baseUrl/notifications/bulk-delete'),
    headers: {
      'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
    body: jsonEncode({
      'notification_ids': ids,
    }),
  );

  if (response.statusCode == 200) {
    return jsonDecode(response.body);
  } else {
    throw Exception('Failed to bulk delete: ${response.body}');
  }
}
```

---

### 7. **Mark Notification as Read** ✅
**POST** `/notifications/{id}/mark-read`

Mark a notification as read for the current user.

#### **Flutter Implementation:**
```dart
Future<bool> markNotificationAsRead(int id) async {
  final response = await http.post(
    Uri.parse('$baseUrl/notifications/$id/mark-read'),
    headers: {
      'Authorization': 'Bearer $token',
      'Accept': 'application/json',
    },
  );

  return response.statusCode == 200;
}
```

---

## 📊 **Data Models**

### **Notification Model:**
```dart
class NotificationModel {
  final int id;
  final String title;
  final String message;
  final String senderName;
  final String recipientType;
  final List<String>? attachmentPaths;
  final List<String>? attachmentNames;
  final String priority;
  final String status;
  final DateTime createdAt;

  NotificationModel({
    required this.id,
    required this.title,
    required this.message,
    required this.senderName,
    required this.recipientType,
    this.attachmentPaths,
    this.attachmentNames,
    required this.priority,
    required this.status,
    required this.createdAt,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'],
      title: json['title'],
      message: json['message'],
      senderName: json['sender_name'],
      recipientType: json['recipient_type'],
      attachmentPaths: json['attachment_path'] != null 
          ? List<String>.from(json['attachment_path'])
          : null,
      attachmentNames: json['attachment_name'] != null
          ? List<String>.from(json['attachment_name'])
          : null,
      priority: json['priority'],
      status: json['status'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'sender_name': senderName,
      'recipient_type': recipientType,
      'attachment_path': attachmentPaths,
      'attachment_name': attachmentNames,
      'priority': priority,
      'status': status,
      'created_at': createdAt.toIso8601String(),
    };
  }

  bool get hasAttachments =>
      attachmentPaths != null && attachmentPaths!.isNotEmpty;
}
```

---

## 🎯 **Field Validation**

### **Required Fields:**
| Field | Type | Description |
|-------|------|-------------|
| `title` | String (max: 255) | Notification title |
| `message` | String | Notification content |
| `sender_id` | Integer | ID of sender |
| `sender_name` | String (max: 255) | Name of sender |
| `recipient_type` | String | Type: students/employees/all |
| `recipient_ids` | Array[Integer] | List of recipient IDs |

### **Optional Fields:**
| Field | Type | Default | Options |
|-------|------|---------|---------|
| `sender_type` | String | "admin" | admin/employee/student |
| `priority` | String | "medium" | low/medium/high |
| `attachment_paths` | Array[String] | null | File paths |
| `attachment_names` | Array[String] | null | Display names |
| `attachments[]` | Files | null | Actual files (multipart) |

### **File Upload Limits:**
- **Max file size:** 10MB per file
- **Supported formats:** PDF, DOC, DOCX, XLS, XLSX, JPG, PNG, TXT, ZIP
- **Max files:** No limit (but consider performance)

---

## ⚠️ **Error Handling**

### **Common Error Responses:**

#### **Validation Error (422):**
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "title": ["The title field is required."],
    "recipient_ids": ["The recipient ids field is required."]
  }
}
```

#### **Authentication Error (401):**
```json
{
  "success": false,
  "message": "Unauthenticated"
}
```

#### **Server Error (500):**
```json
{
  "success": false,
  "message": "Error sending notification: Internal server error",
  "data": null
}
```

### **Flutter Error Handling:**
```dart
try {
  final result = await sendNotification(/* parameters */);
  // Handle success
} on Exception catch (e) {
  if (e.toString().contains('422')) {
    // Handle validation errors
  } else if (e.toString().contains('401')) {
    // Handle authentication errors
  } else {
    // Handle other errors
  }
}
```

---

## 🧪 **Complete Flutter Service Class**

```dart
import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:path/path.dart';

class NotificationService {
  final String baseUrl;
  final String token;

  NotificationService({required this.baseUrl, required this.token});

  Map<String, String> get _headers => {
    'Authorization': 'Bearer $token',
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  // Send simple notification
  Future<NotificationModel> sendSimpleNotification({
    required String title,
    required String message,
    required List<int> recipientIds,
    String recipientType = 'students',
    String priority = 'medium',
  }) async {
    final result = await sendNotification(
      title: title,
      message: message,
      senderId: 1,
      senderName: 'Flutter App',
      recipientType: recipientType,
      recipientIds: recipientIds,
      priority: priority,
    );

    return NotificationModel.fromJson(result['data']);
  }

  // Send notification with attachments
  Future<NotificationModel> sendNotificationWithAttachments({
    required String title,
    required String message,
    required List<int> recipientIds,
    required List<File> files,
    String recipientType = 'students',
    String priority = 'high',
  }) async {
    final result = await sendNotificationWithFiles(
      title: title,
      message: message,
      senderId: 1,
      senderName: 'Flutter App',
      recipientType: recipientType,
      recipientIds: recipientIds,
      attachments: files,
      priority: priority,
    );

    return NotificationModel.fromJson(result['data']);
  }

  // Get all notifications
  Future<List<NotificationModel>> getNotifications() async {
    final notifications = await getAllNotifications();
    return notifications.map((n) => NotificationModel.fromJson(n)).toList();
  }
}
```

---

## 🚀 **Quick Start Example**

```dart
void main() async {
  // Initialize service
  final notificationService = NotificationService(
    baseUrl: 'http://localhost/appnote-api/public/api',
    token: 'your_bearer_token_here',
  );

  try {
    // Send simple notification
    await notificationService.sendSimpleNotification(
      title: 'Welcome Message',
      message: 'Welcome to our app!',
      recipientIds: [1, 2, 3],
      priority: 'high',
    );
    print('✅ Simple notification sent successfully');

    // Send notification with files
    final files = [File('path/to/document.pdf')];
    await notificationService.sendNotificationWithAttachments(
      title: 'Documents Shared',
      message: 'Please review the attached documents.',
      recipientIds: [1, 2, 3],
      files: files,
    );
    print('✅ Notification with attachments sent successfully');

    // Get all notifications
    final notifications = await notificationService.getNotifications();
    print('📋 Retrieved ${notifications.length} notifications');

  } catch (e) {
    print('❌ Error: $e');
  }
}
```

---

## 📱 **Flutter Widget Example**

```dart
class NotificationListWidget extends StatefulWidget {
  @override
  _NotificationListWidgetState createState() => _NotificationListWidgetState();
}

class _NotificationListWidgetState extends State<NotificationListWidget> {
  List<NotificationModel> _notifications = [];
  bool _loading = true;

  @override
  void initState() {
    super.initState();
    _loadNotifications();
  }

  Future<void> _loadNotifications() async {
    try {
      final notificationService = NotificationService(
        baseUrl: 'http://localhost/appnote-api/public/api',
        token: 'your-token',
      );

      final notifications = await notificationService.getNotifications();
      setState(() {
        _notifications = notifications;
        _loading = false;
      });
    } catch (e) {
      setState(() => _loading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading notifications: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_loading) {
      return Center(child: CircularProgressIndicator());
    }

    return ListView.builder(
      itemCount: _notifications.length,
      itemBuilder: (context, index) {
        final notification = _notifications[index];
        return Card(
          child: ListTile(
            title: Text(notification.title),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(notification.message),
                if (notification.hasAttachments)
                  Wrap(
                    children: notification.attachmentNames!
                        .map((name) => Chip(label: Text(name)))
                        .toList(),
                  ),
              ],
            ),
            trailing: Chip(
              label: Text(notification.priority),
              backgroundColor: notification.priority == 'high'
                  ? Colors.red
                  : notification.priority == 'medium'
                      ? Colors.orange
                      : Colors.green,
            ),
          ),
        );
      },
    );
  }
}
```

---

## ✅ **API Status**
- ✅ **Send Notifications** - Working
- ✅ **File Upload** - Working
- ✅ **Multiple Attachments** - Working
- ✅ **Get Notifications** - Working
- ✅ **Delete Notifications** - Working
- ✅ **Mark as Read** - Working
- ✅ **Bulk Operations** - Working

**Ready for Flutter integration!** 🎉
```
