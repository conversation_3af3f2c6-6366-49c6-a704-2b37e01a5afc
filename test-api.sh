#!/bin/bash

# AppNote API Testing Script
# Bash script using curl to test API endpoints

BASE_URL="http://localhost/appnote-api/public/api"

echo "=== AppNote API Testing ==="

# Test 1: Health Check
echo -e "\n1. Testing Health Check..."
HEALTH_RESPONSE=$(curl -s -X GET "$BASE_URL/health" -H "Content-Type: application/json")
if [[ $? -eq 0 ]]; then
    echo "✅ Health Check: $(echo $HEALTH_RESPONSE | jq -r '.message')"
else
    echo "❌ Health Check Failed"
fi

# Test 2: Admin Login
echo -e "\n2. Testing Admin Login..."
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/admin/login" \
    -H "Content-Type: application/json" \
    -d '{"username":"admin123","password":"admin123"}')

if [[ $? -eq 0 ]]; then
    SUCCESS=$(echo $LOGIN_RESPONSE | jq -r '.success')
    if [[ "$SUCCESS" == "true" ]]; then
        echo "✅ Admin Login: $(echo $LOGIN_RESPONSE | jq -r '.message')"
        echo "   User: $(echo $LOGIN_RESPONSE | jq -r '.data.user.name') ($(echo $LOGIN_RESPONSE | jq -r '.data.user.email'))"
        
        # Extract token for further tests
        AUTH_TOKEN=$(echo $LOGIN_RESPONSE | jq -r '.data.token')
        echo "   Token: ${AUTH_TOKEN:0:50}..."
    else
        echo "❌ Admin Login Failed: $(echo $LOGIN_RESPONSE | jq -r '.message')"
    fi
else
    echo "❌ Admin Login Failed: Network error"
fi

# Test 3: Test Database Connection
echo -e "\n3. Testing Database Connection..."
DB_RESPONSE=$(curl -s -X GET "$BASE_URL/test-db" -H "Content-Type: application/json")
if [[ $? -eq 0 ]]; then
    echo "✅ Database: $(echo $DB_RESPONSE | jq -r '.message')"
else
    echo "❌ Database Test Failed"
fi

# Test 4: Test Protected Endpoint (if we have a token)
if [[ -n "$AUTH_TOKEN" && "$AUTH_TOKEN" != "null" ]]; then
    echo -e "\n4. Testing Protected Endpoint (Students List)..."
    STUDENTS_RESPONSE=$(curl -s -X GET "$BASE_URL/students" \
        -H "Authorization: Bearer $AUTH_TOKEN" \
        -H "Content-Type: application/json")
    
    if [[ $? -eq 0 ]]; then
        SUCCESS=$(echo $STUDENTS_RESPONSE | jq -r '.success')
        if [[ "$SUCCESS" == "true" ]]; then
            COUNT=$(echo $STUDENTS_RESPONSE | jq -r '.data | length')
            echo "✅ Students Endpoint: Retrieved $COUNT students"
        else
            echo "❌ Students Endpoint Failed: $(echo $STUDENTS_RESPONSE | jq -r '.message')"
        fi
    else
        echo "❌ Students Endpoint Failed: Network error"
    fi
fi

# Test 5: API Documentation
echo -e "\n5. Testing API Documentation..."
DOCS_RESPONSE=$(curl -s -X GET "$BASE_URL/docs" -H "Content-Type: application/json")
if [[ $? -eq 0 ]]; then
    echo "✅ API Docs: $(echo $DOCS_RESPONSE | jq -r '.message')"
else
    echo "❌ API Docs Failed"
fi

echo -e "\n=== Testing Complete ==="
