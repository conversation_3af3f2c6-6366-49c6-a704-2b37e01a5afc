<?php

namespace App\Http\Controllers;

use App\Models\Student;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Spreadsheet;

class StudentController extends Controller
{
    /**
     * Get unique non-empty values for a specific field
     */
    private function getUniqueValues($field)
    {
        return DB::table('students')
            ->select($field)
            ->whereNotNull($field)
            ->where($field, '!=', '')
            ->distinct()
            ->orderBy($field)
            ->pluck($field)
            ->toArray();
    }
    /**
     * Display a listing of the students.
     */
    public function index(Request $request)
    {
        $query = Student::query();
        
        // Search functionality
        if ($request->has('search') && $request->search != '') {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('full_name', 'like', "%{$search}%")
                  ->orWhere('username', 'like', "%{$search}%")
                  ->orWhere('class', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('nationality', 'like', "%{$search}%")
                  ->orWhere('specialization', 'like', "%{$search}%")
                  ->orWhere('section', 'like', "%{$search}%")
                  ->orWhere('level', 'like', "%{$search}%")
                  ->orWhere('result', 'like', "%{$search}%");
            });
        }
        
        // Get paginated results
        $students = $query->orderBy('id', 'desc')->paginate(15);
        
        // Get unique values for dropdowns
        $nationalities = $this->getUniqueValues('nationality');
        $specializations = $this->getUniqueValues('specialization');
        $sections = $this->getUniqueValues('section');
        $classes = $this->getUniqueValues('class');
        $levels = $this->getUniqueValues('level');
        $results = $this->getUniqueValues('result');
        
        return view('students.index', compact('students', 'nationalities', 'specializations', 'sections', 'classes', 'levels', 'results'));
    }

    /**
     * Store a newly created student in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'username' => 'required|string|max:50|unique:students',
            'full_name' => 'required|string|max:255',
            'nationality' => 'nullable|string|max:100',
            'phone' => 'nullable|string|max:50',
            'specialization' => 'nullable|string|max:100',
            'section' => 'nullable|string|max:50',
            'class' => 'nullable|string|max:100',
            'level' => 'nullable|string|max:50',
            'result' => 'nullable|string|max:50',
            'password' => 'required|string|min:5',
        ]);

        if ($validator->fails()) {
            return back()
                ->withErrors($validator)
                ->withInput();
        }

        $student = new Student();
        $student->username = $request->username;
        $student->full_name = $request->full_name;
        $student->nationality = $request->nationality;
        $student->phone = $request->phone;
        $student->specialization = $request->specialization;
        $student->section = $request->section;
        $student->class = $request->class;
        $student->level = $request->level;
        $student->result = $request->result;
        $student->password = Hash::make($request->password);
        $student->is_active = $request->has('is_active') ? true : true; // Make new students active by default
        $student->save();

        return redirect()->route('students.index')->with('success', 'Student added successfully.');
    }

    /**
     * Update the specified student in storage.
     */
    public function update(Request $request, Student $student)
    {
        $validator = Validator::make($request->all(), [
            // Username is not included in validation as it should not be editable
            'full_name' => 'required|string|max:255',
            'nationality' => 'nullable|string|max:100',
            'phone' => 'nullable|string|max:50',
            'specialization' => 'nullable|string|max:100',
            'section' => 'nullable|string|max:50',
            'class' => 'nullable|string|max:100',
            'level' => 'nullable|string|max:50',
            'result' => 'nullable|string|max:50',
            'password' => 'nullable|string|min:5',
        ]);

        if ($validator->fails()) {
            return back()
                ->withErrors($validator)
                ->withInput();
        }

        // Username should not be updated for security reasons
        // $student->username = $request->username; // Commented out - username is not editable
        $student->full_name = $request->full_name;
        $student->nationality = $request->nationality;
        $student->phone = $request->phone;
        $student->specialization = $request->specialization;
        $student->section = $request->section;
        $student->class = $request->class;
        $student->level = $request->level;
        $student->result = $request->result;
        
        if ($request->filled('password')) {
            $student->password = Hash::make($request->password);
        }
        
        $student->is_active = $request->has('is_active');
        $student->save();

        return redirect()->route('students.index')->with('success', 'Student updated successfully.');
    }

    /**
     * Remove the specified student from storage.
     */
    public function destroy(Student $student)
    {
        $student->delete();
        return redirect()->route('students.index')->with('success', 'Student deleted successfully.');
    }
    
    /**
     * Import students from Excel file
     */
    public function import(Request $request)
    {
        // Increase execution time limit for large imports
        ini_set('max_execution_time', 600); // 10 minutes
        
        $request->validate([
            'excel_file' => 'required|file|mimes:xlsx,xls,csv,txt|mimetypes:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel,text/csv,text/plain,application/csv|max:20480',
            'has_header' => 'nullable',
            'import_id' => 'nullable|string',
        ]);
        
        // Check if this is an AJAX request
        $isAjax = $request->ajax() || $request->header('X-Requested-With') === 'XMLHttpRequest';
        $importId = $request->input('import_id', 'import_' . time());

        \Log::info('📊 Student import started', [
            'user_id' => auth()->id(),
            'is_ajax' => $isAjax,
            'import_id' => $importId,
            'file_name' => $request->file('excel_file') ? $request->file('excel_file')->getClientOriginalName() : 'No file',
            'has_header' => $request->has('has_header'),
        ]);
        
        try {
            $file = $request->file('excel_file');
            
            // Store the file temporarily
            $filePath = $file->storeAs('temp_imports', $importId . '.' . $file->getClientOriginalExtension());
            
            if ($isAjax) {
                // For AJAX requests, start the import process in the background and return immediately
                session([$importId => [
                    'status' => 'uploading',
                    'progress' => 0,
                    'message' => 'File uploaded. Starting import...',
                    'total_rows' => 0,
                    'processed_rows' => 0,
                    'success_count' => 0,
                    'error_count' => 0,
                    'completed' => false,
                ]]);
                
                // Start background processing (in a real app, you might use a queue job)
                // For now, we'll simulate this with a session-based approach
                $this->processImportInBackground($importId, $filePath, $request->has('has_header'));
                
                return response()->json([
                    'success' => true,
                    'message' => 'Import started',
                    'import_id' => $importId
                ]);
            }
            
            // For non-AJAX requests, process synchronously (fallback method)
            $fullPath = storage_path('app/' . $filePath);
            $extension = strtolower(pathinfo($fullPath, PATHINFO_EXTENSION));

            if ($extension === 'csv') {
                // Handle CSV files
                \Log::info('📄 Processing CSV file');
                $rows = [];
                if (($handle = fopen($fullPath, "r")) !== FALSE) {
                    while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
                        $rows[] = $data;
                    }
                    fclose($handle);
                }
            } else {
                // Handle Excel files
                \Log::info('📊 Processing Excel file');

                // Check if ZipArchive is available
                if (!class_exists('ZipArchive')) {
                    \Log::error('❌ ZipArchive not available for Excel files');
                    throw new \Exception('ZipArchive extension is required for Excel files. Please convert your file to CSV format and try again.');
                }

                try {
                    $reader = IOFactory::createReaderForFile($fullPath);
                    $reader->setReadDataOnly(true);
                    $spreadsheet = $reader->load($fullPath);
                    $worksheet = $spreadsheet->getActiveSheet();
                    $rows = $worksheet->toArray();
                } catch (\Exception $e) {
                    \Log::error('❌ Failed to read Excel file', [
                        'error' => $e->getMessage(),
                        'file' => $fullPath,
                    ]);
                    throw new \Exception('Failed to read Excel file: ' . $e->getMessage() . '. Please try converting to CSV format.');
                }
            }
            
            // Skip header row if indicated
            if ($request->has('has_header')) {
                array_shift($rows);
            }
            
            list($successCount, $errorCount) = $this->processStudentRows($rows);
            
            // Clean up the temp file
            if (file_exists(storage_path('app/' . $filePath))) {
                unlink(storage_path('app/' . $filePath));
            }
            
            if ($errorCount > 0) {
                return redirect()->route('students.index')
                    ->with('success', "$successCount students imported successfully.")
                    ->with('error', "$errorCount students could not be imported due to errors.");
            }
            
            return redirect()->route('students.index')
                ->with('success', "$successCount students imported successfully.");
        } catch (\Exception $e) {
            // Clean up session data in case of error
            if ($isAjax && session()->has($importId)) {
                session()->forget($importId);
            }
            
            if ($isAjax) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error importing file: ' . $e->getMessage()
                ]);
            }
            
            return redirect()->route('students.index')
                ->with('error', 'Error importing file: ' . $e->getMessage());
        }
    }
    
    /**
     * Process the import in background (simulated)
     */
    private function processImportInBackground($importId, $filePath, $hasHeader)
    {
        try {
            // In a real application, this would be a queue job
            // For now, we'll simulate the background processing
            
            $fullPath = storage_path('app/' . $filePath);
            $extension = strtolower(pathinfo($fullPath, PATHINFO_EXTENSION));

            if ($extension === 'csv') {
                // Handle CSV files
                $rows = [];
                if (($handle = fopen($fullPath, "r")) !== FALSE) {
                    while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
                        $rows[] = $data;
                    }
                    fclose($handle);
                }
            } else {
                // Handle Excel files
                if (!class_exists('ZipArchive')) {
                    throw new \Exception('ZipArchive extension is required for Excel files. Please convert your file to CSV format and try again.');
                }

                $reader = IOFactory::createReaderForFile($fullPath);
                $reader->setReadDataOnly(true);
                $spreadsheet = $reader->load($fullPath);
                $worksheet = $spreadsheet->getActiveSheet();
                $rows = $worksheet->toArray();
            }

            \Log::info('📊 File read successfully', [
                'total_rows' => count($rows),
                'has_header' => $hasHeader,
                'first_row' => $rows[0] ?? 'No data',
            ]);

            // Skip header row if indicated
            if ($hasHeader) {
                $headerRow = array_shift($rows);
                \Log::info('📋 Header row removed', ['header' => $headerRow]);
            }

            // Update session with total rows
            $totalRows = count($rows);

            \Log::info('🔄 Starting to process rows', ['total_rows' => $totalRows]);
            session([$importId => array_merge(session($importId, []), [
                'status' => 'processing',
                'total_rows' => $totalRows,
                'message' => 'Processing ' . $totalRows . ' rows...'
            ])]);
            
            $successCount = 0;
            $errorCount = 0;
            
            // Process rows with progress updates
            foreach ($rows as $index => $row) {
                if (empty($row[0])) continue; // Skip empty rows

                // Check if data is semicolon-separated in first column
                if (count($row) == 1 && strpos($row[0], ';') !== false) {
                    $row = explode(';', $row[0]);
                    \Log::info('🔧 Fixed semicolon-separated data', [
                        'original_count' => 1,
                        'new_count' => count($row),
                        'first_field' => $row[0] ?? 'N/A'
                    ]);
                }

                try {
                    \Log::info('👤 Processing student row', [
                        'row_index' => $index,
                        'username' => $row[0] ?? 'N/A',
                        'full_name' => $row[1] ?? 'N/A',
                        'row_count' => count($row),
                        'row_data' => $row
                    ]);

                    // Check if username already exists
                    $existingStudent = Student::where('username', $row[0])->first();
                    if ($existingStudent) {
                        \Log::warning('⚠️ Username already exists', ['username' => $row[0]]);
                        $errorCount++;
                        continue;
                    }

                    $student = new Student();
                    $student->username = $row[0]; // username
                    $student->full_name = $row[1]; // full_name
                    $student->nationality = $row[2] ?? null; // nationality
                    $student->phone = $row[3] ?? null; // phone
                    $student->specialization = $row[4] ?? null; // specialization
                    $student->section = $row[5] ?? null; // section
                    $student->class = $row[6] ?? null; // class
                    $student->level = $row[7] ?? null; // level
                    $student->result = $row[8] ?? null; // result
                    $student->password = Hash::make($row[9] ?? 'stu123'); // password
                    $student->is_active = isset($row[10]) ? (bool)$row[10] : true; // is_active

                    \Log::info('💾 Attempting to save student', [
                        'username' => $student->username,
                        'full_name' => $student->full_name,
                    ]);

                    $student->save();

                    \Log::info('✅ Student saved successfully', [
                        'id' => $student->id,
                        'username' => $student->username,
                    ]);

                    $successCount++;
                } catch (\Exception $e) {
                    \Log::error('❌ Failed to save student', [
                        'error' => $e->getMessage(),
                        'username' => $row[0] ?? 'N/A',
                        'row_data' => $row,
                    ]);
                    $errorCount++;
                }
                
                // Update progress every 10 rows or at specific percentages
                if ($index % 10 === 0 || $index === count($rows) - 1) {
                    $processedRows = $index + 1;
                    $progress = ($totalRows > 0) ? round(($processedRows / $totalRows) * 100) : 0;
                    
                    session([$importId => array_merge(session($importId, []), [
                        'progress' => $progress,
                        'processed_rows' => $processedRows,
                        'success_count' => $successCount,
                        'error_count' => $errorCount,
                        'message' => "Processing: $processedRows of $totalRows rows ($progress%)"
                    ])]);
                }
            }
            
            // Mark as completed
            session([$importId => array_merge(session($importId, []), [
                'status' => 'completed',
                'progress' => 100,
                'processed_rows' => $totalRows,
                'success_count' => $successCount,
                'error_count' => $errorCount,
                'message' => "Import completed: $successCount successful, $errorCount failed",
                'completed' => true
            ])]);
            
            // Clean up the temp file
            if (file_exists(storage_path('app/' . $filePath))) {
                unlink(storage_path('app/' . $filePath));
            }
            
        } catch (\Exception $e) {
            // Update session with error
            session([$importId => array_merge(session($importId, []), [
                'status' => 'error',
                'message' => 'Error: ' . $e->getMessage(),
                'completed' => true
            ])]);
            
            // Clean up the temp file
            if (file_exists(storage_path('app/' . $filePath))) {
                unlink(storage_path('app/' . $filePath));
            }
        }
    }
    
    /**
     * Check the progress of an import
     */
    public function importProgress($importId)
    {
        if (!session()->has($importId)) {
            return response()->json([
                'error' => 'Import session not found'
            ]);
        }
        
        $importData = session($importId);
        
        return response()->json([
            'status' => $importData['status'] ?? 'unknown',
            'progress' => $importData['progress'] ?? 0,
            'message' => $importData['message'] ?? 'Processing...',
            'processed_rows' => $importData['processed_rows'] ?? 0,
            'total_rows' => $importData['total_rows'] ?? 0,
            'success_count' => $importData['success_count'] ?? 0,
            'error_count' => $importData['error_count'] ?? 0,
            'completed' => $importData['completed'] ?? false
        ]);
    }
    
    /**
     * Process a batch of student rows from Excel import
     * 
     * @param array $rows
     * @return array [successCount, errorCount]
     */
    private function processStudentRows($rows)
    {
        \Log::info('🔄 processStudentRows called', [
            'total_rows' => count($rows),
            'first_row' => $rows[0] ?? 'No data'
        ]);

        $successCount = 0;
        $errorCount = 0;

        // Use database transactions for better performance
        DB::beginTransaction();
        
        try {
            foreach ($rows as $row) {
                if (empty($row[0])) continue; // Skip empty rows

                // Check if data is semicolon-separated in first column
                if (count($row) == 1 && strpos($row[0], ';') !== false) {
                    $row = explode(';', $row[0]);
                    \Log::info('🔧 Fixed semicolon-separated data in processStudentRows', [
                        'original_count' => 1,
                        'new_count' => count($row),
                        'first_field' => $row[0] ?? 'N/A'
                    ]);
                }

                try {
                    \Log::info('👤 Processing student in processStudentRows', [
                        'username' => $row[0] ?? 'N/A',
                        'full_name' => $row[1] ?? 'N/A',
                        'row_count' => count($row),
                        'row_data' => $row
                    ]);

                    // Check if username already exists
                    $existingStudent = Student::where('username', $row[0])->first();
                    if ($existingStudent) {
                        \Log::warning('⚠️ Username already exists in processStudentRows', ['username' => $row[0]]);
                        $errorCount++;
                        continue;
                    }

                    $student = new Student();
                    $student->username = $row[0]; // username
                    $student->full_name = $row[1]; // full_name
                    $student->nationality = $row[2] ?? null; // nationality
                    $student->phone = $row[3] ?? null; // phone
                    $student->specialization = $row[4] ?? null; // specialization
                    $student->section = $row[5] ?? null; // section
                    $student->class = $row[6] ?? null; // class
                    $student->level = $row[7] ?? null; // level
                    $student->result = $row[8] ?? null; // result
                    $student->password = Hash::make($row[9] ?? 'stu123'); // password
                    $student->is_active = isset($row[10]) ? (bool)$row[10] : true; // is_active
                    \Log::info('💾 Attempting to save student in processStudentRows', [
                        'username' => $student->username,
                        'full_name' => $student->full_name,
                    ]);

                    $student->save();

                    \Log::info('✅ Student saved successfully in processStudentRows', [
                        'id' => $student->id,
                        'username' => $student->username,
                    ]);

                    $successCount++;

                    // Free memory periodically
                    if ($successCount % 100 === 0) {
                        gc_collect_cycles();
                    }
                } catch (\Exception $e) {
                    \Log::error('❌ Failed to save student in processStudentRows', [
                        'error' => $e->getMessage(),
                        'username' => $row[0] ?? 'N/A',
                        'row_data' => $row,
                    ]);
                    $errorCount++;
                }
            }
            
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
        
        return [$successCount, $errorCount];
    }
    
    /**
     * Export a template for importing students
     */
    public function exportTemplate()
    {
        // Set maximum execution time to prevent timeout
        ini_set('max_execution_time', 300); // 5 minutes
        
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        
        // Add headers
        $headers = [
            'A1' => 'username',
            'B1' => 'full_name',
            'C1' => 'nationality',
            'D1' => 'phone',
            'E1' => 'specialization',
            'F1' => 'section',
            'G1' => 'class',
            'H1' => 'level',
            'I1' => 'result',
            'J1' => 'password',
            'K1' => 'is_active'
        ];
        
        foreach ($headers as $cell => $value) {
            $sheet->setCellValue($cell, $value);
            // Make headers bold
            $sheet->getStyle($cell)->getFont()->setBold(true);
        }
        
        // Add example data
        $exampleData = [
            'A2' => 'stu0001',
            'B2' => 'John Doe',
            'C2' => 'Lebanese',
            'D2' => '71123456',
            'E2' => 'Science',
            'F2' => 'A',
            'G2' => 'Grade 10',
            'H2' => '2',
            'I2' => 'Pass',
            'J2' => 'stu123',
            'K2' => '1'
        ];
        
        foreach ($exampleData as $cell => $value) {
            $sheet->setCellValue($cell, $value);
        }
        
        // Set fixed column widths instead of auto-size to improve performance
        $columnWidths = [
            'A' => 12, // username
            'B' => 20, // full_name
            'C' => 15, // nationality
            'D' => 15, // phone
            'E' => 15, // specialization
            'F' => 10, // section
            'G' => 12, // class
            'H' => 8,  // level
            'I' => 10, // result
            'J' => 12, // password
            'K' => 10  // is_active
        ];
        
        foreach ($columnWidths as $col => $width) {
            $sheet->getColumnDimension($col)->setWidth($width);
        }
        
        // Create the file in memory
        $writer = new Xlsx($spreadsheet);
        $tempFile = tempnam(sys_get_temp_dir(), 'student_template_');
        $writer->save($tempFile);
        
        // Send the file as a download
        $filename = 'students_import_template.xlsx';
        return response()->download($tempFile, $filename, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'max-age=0',
        ])->deleteFileAfterSend(true);
    }
    
    /**
     * Handle bulk actions on students
     */
    public function bulkAction(Request $request)
    {
        $action = $request->input('action');
        $ids = $request->input('ids', []);
        
        // Skip the empty IDs check for delete-all action
        if (empty($ids) && $action !== 'delete-all') {
            return redirect()->route('students.index')
                ->with('error', 'No students selected for bulk action');
        }
        
        $count = count($ids);
        $message = '';
        
        switch ($action) {
            case 'activate':
                Student::whereIn('id', $ids)->update(['is_active' => 1]);
                $message = "{$count} students have been activated successfully";
                break;
                
            case 'deactivate':
                Student::whereIn('id', $ids)->update(['is_active' => 0]);
                $message = "{$count} students have been deactivated successfully";
                break;
                
            case 'delete':
                Student::whereIn('id', $ids)->delete();
                $message = "{$count} students have been deleted successfully";
                break;
                
            case 'delete-all':
                // For delete-all, we don't use the selected IDs
                $totalDeleted = Student::count();
                Student::truncate(); // This deletes all records and resets auto-increment
                $message = "All {$totalDeleted} students have been deleted successfully";
                break;
                
            default:
                return redirect()->route('students.index')
                    ->with('error', 'Invalid bulk action');
        }
        
        return redirect()->route('students.index')
            ->with('success', $message);
    }
}
