<?php

namespace App\Http\Controllers;

use App\Models\Employee;
use App\Models\Notification;
use App\Models\NotificationRecipient;
use App\Models\Student;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    /**
     * Bulk delete notifications
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function bulkDelete(Request $request)
    {
        $request->validate([
            'notification_ids' => 'required|array',
            'notification_ids.*' => 'integer|exists:notifications,id'
        ]);
        
        $count = 0;
        
        DB::beginTransaction();
        try {
            $ids = $request->notification_ids;
            
            // First delete associated notification recipients
            NotificationRecipient::whereIn('notification_id', $ids)->delete();
            
            // Then delete notifications
            $count = Notification::whereIn('id', $ids)->delete();
            
            DB::commit();
            return redirect()->route('notifications.index')
                ->with('success', $count . ' notifications have been deleted successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Error deleting notifications: ' . $e->getMessage());
        }
    }
    
    /**
     * Bulk update notification status
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function bulkUpdateStatus(Request $request)
    {
        $request->validate([
            'notification_ids' => 'required|array',
            'notification_ids.*' => 'integer|exists:notifications,id',
            'status' => 'required|string|in:draft,sent'
        ]);
        
        $count = 0;
        
        DB::beginTransaction();
        try {
            $ids = $request->notification_ids;
            $status = $request->status;
            
            // Update notification status
            $count = Notification::whereIn('id', $ids)->update(['status' => $status]);
            
            // If marking as sent, also update individual recipients
            if ($status === 'sent') {
                $now = now();
                NotificationRecipient::whereIn('notification_id', $ids)
                    ->update([
                        'status' => 'sent',
                        'sent_at' => $now
                    ]);
            }
            
            DB::commit();
            return redirect()->route('notifications.index')
                ->with('success', $count . ' notifications have been updated to "' . ucfirst($status) . '" status.');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Error updating notification status: ' . $e->getMessage());
        }
    }
    /**
     * Display a listing of notifications.
     */
    public function index()
    {
        $notifications = Notification::orderBy('created_at', 'desc')->paginate(15);
        return view('notifications.index', compact('notifications'));
    }

    /**
     * Show the form for creating a new notification.
     */
    public function create()
    {
        // Get unique values for student filters
        $levels = $this->getUniqueStudentValues('level');
        $classes = $this->getUniqueStudentValues('class');
        
        // Get unique values for employee filters
        $contractTypes = $this->getUniqueEmployeeValues('contract_type');
        $jobStatuses = $this->getUniqueEmployeeValues('job_status');
        
        return view('notifications.create', compact(
            'levels', 
            'classes', 
            'contractTypes', 
            'jobStatuses'
        ));
    }

    /**
     * Store a newly created notification in storage.
     */
    public function store(Request $request)
    {
        // Validate the notification data
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'message' => 'required|string',
            'recipient_group_type' => 'required|string',
            'priority' => 'required|in:low,medium,high',
            'attachments' => 'nullable|array',
            'attachments.*' => 'nullable|file|max:10240',
        ]);
        
        // Start DB transaction
        DB::beginTransaction();
        
        try {
            // Handle multiple file uploads if present
            $attachmentPaths = [];
            $attachmentNames = [];
            
            if ($request->hasFile('attachments')) {
                foreach ($request->file('attachments') as $file) {
                    $attachmentNames[] = $file->getClientOriginalName();
                    $attachmentPaths[] = $file->store('notifications', 'public');
                }
            }
            
            // Create the notification
            $notification = new Notification();
            $notification->title = $request->title;
            $notification->message = $request->message;
            $notification->sender_type = 'admin';
            $notification->sender_id = Auth::id() ?? 1;
            $notification->sender_name = Auth::user()->name ?? 'System Admin';
            $notification->priority = $request->priority;
            // Since we have array cast in model, save as arrays directly
            $notification->attachment_path = !empty($attachmentPaths) ? $attachmentPaths : null;
            $notification->attachment_name = !empty($attachmentNames) ? $attachmentNames : null;
            $notification->status = 'sent';
            
            // Handle different recipient types and get recipient IDs
            $recipientIds = $this->getRecipientIds($request);
            $notification->recipient_type = $request->recipient_group_type;
            $notification->recipient_ids = json_encode($recipientIds);

            // Debug logging
            \Log::info('Notification Recipients Debug:', [
                'recipient_group_type' => $request->recipient_group_type,
                'recipient_ids_count' => count($recipientIds),
                'recipient_ids' => $recipientIds,
                'selected_levels' => $request->input('selected_levels', []),
                'selected_classes' => $request->input('selected_classes', []),
            ]);
            
            $notification->save();
            
            // Save individual notification recipients
            foreach ($recipientIds as $recipientId) {
                // Extract the numeric part from recipient IDs (e.g., 'student_123' becomes 123)
                $numericId = $this->extractNumericId($recipientId);
                if (!$numericId) {
                    continue; // Skip invalid IDs
                }
                
                // Determine recipient type based on the ID itself or the group type
                // For 'all' group type, the IDs will be prefixed to distinguish between students and employees
                $recipientType = strpos($recipientId, 'student_') === 0 || strpos($recipientId, 'employee_') === 0
                    ? $this->getRecipientModelType($recipientId)  // Use the prefixed ID to determine type
                    : $this->getRecipientModelType($request->recipient_group_type); // Otherwise use group type

                // Debug logging for recipient type determination
                \Log::info('Recipient Type Debug:', [
                    'recipient_id' => $recipientId,
                    'group_type' => $request->recipient_group_type,
                    'determined_type' => $recipientType,
                    'numeric_id' => $this->extractNumericId($recipientId),
                ]);
                
                $recipientName = '';
                $recipientClass = null;
                
                // Get recipient name based on type
                if ($recipientType === 'student') {
                    $student = Student::find($numericId);
                    if ($student) {
                        $recipientName = $student->full_name ?? $student->name ?? 'Student #' . $numericId;
                        $recipientClass = $student->class;
                    }
                } else { // employee
                    $employee = Employee::find($numericId);
                    if ($employee) {
                        $recipientName = $employee->full_name ?? $employee->name ?? 'Employee #' . $numericId;
                        $recipientClass = $employee->contract_type;
                    }
                }
                
                $recipient = new NotificationRecipient();
                $recipient->notification_id = $notification->id;
                $recipient->recipient_id = $numericId; // Use the extracted numeric ID
                $recipient->recipient_type = $recipientType;
                $recipient->recipient_name = $recipientName;
                $recipient->recipient_class = $recipientClass;
                $recipient->read_at = null;
                $recipient->status = 'sent';
                $recipient->sent_at = now();
                $recipient->save();
            }
            
            DB::commit();
            
            return redirect()->route('notifications.index')
                ->with('success', 'Notification sent successfully to ' . count($recipientIds) . ' recipients');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Error sending notification: ' . $e->getMessage())->withInput();
        }
    }

    /**
     * Display the specified notification.
     */
    public function show($id)
    {
        $notification = Notification::findOrFail($id);
        $recipients = NotificationRecipient::where('notification_id', $id)->paginate(50);
        
        return view('notifications.show', compact('notification', 'recipients'));
    }

    /**
     * Get unique values for student filters
     */
    private function getUniqueStudentValues($field)
    {
        return DB::table('students')
            ->select($field)
            ->whereNotNull($field)
            ->where($field, '!=', '')
            ->distinct()
            ->orderBy($field)
            ->pluck($field)
            ->toArray();
    }

    /**
     * Get unique values for employee filters
     */
    private function getUniqueEmployeeValues($field)
    {
        return DB::table('employees')
            ->select($field)
            ->whereNotNull($field)
            ->where($field, '!=', '')
            ->distinct()
            ->orderBy($field)
            ->pluck($field)
            ->toArray();
    }

    /**
     * Get recipient IDs based on the group selection
     */
    private function getRecipientIds(Request $request)
    {
        $groupType = $request->recipient_group_type;
        
        switch ($groupType) {
            case 'all':
                // All students and employees
                $studentIds = Student::where('is_active', 1)->pluck('id')->toArray();
                $employeeIds = Employee::pluck('id')->toArray(); // Get all employees regardless of job_status
                
                // Prefix IDs with type to distinguish between students and employees
                $studentIds = array_map(function($id) { return 'student_' . $id; }, $studentIds);
                $employeeIds = array_map(function($id) { return 'employee_' . $id; }, $employeeIds);
                
                return array_merge($studentIds, $employeeIds);
                
            case 'all_students':
                // All active students
                return Student::where('is_active', 1)->pluck('id')->toArray();
                
            case 'student_level':
                // Filter students by level(s)
                $levels = $request->input('selected_levels', []);

                if (empty($levels)) {
                    return [];
                }

                return Student::where('is_active', 1)
                    ->whereIn('level', $levels)
                    ->pluck('id')
                    ->toArray();
                
            case 'student_class':
                // Filter students by class(es)
                $classes = $request->input('selected_classes', []);

                if (empty($classes)) {
                    return [];
                }

                return Student::where('is_active', 1)
                    ->whereIn('class', $classes)
                    ->pluck('id')
                    ->toArray();
                
            case 'all_employees':
                // Get all employees without filtering by job status
                return Employee::pluck('id')->toArray();
                
            case 'employee_contract':
                // Filter employees by contract type
                $contractTypes = $request->input('selected_contract_types', []);

                if (empty($contractTypes)) {
                    return [];
                }

                return Employee::whereIn('contract_type', $contractTypes)
                    ->pluck('id')
                    ->toArray();
                
            case 'employee_status':
                // Filter employees by job status
                $jobStatuses = $request->input('selected_job_statuses', []);
                return Employee::whereIn('job_status', $jobStatuses)
                    ->pluck('id')
                    ->toArray();
                
            default:
                return [];
        }
    }
    
    /**
     * Get the recipient model type based on group type or prefixed ID
     */
    private function getRecipientModelType($groupTypeOrId)
    {
        // Check if this is a prefixed ID like 'student_123' or 'employee_456'
        if (strpos($groupTypeOrId, 'student_') === 0) {
            return 'student';
        } elseif (strpos($groupTypeOrId, 'employee_') === 0) {
            return 'employee';
        }

        // If not a prefixed ID, check the group type
        $studentGroupTypes = [
            'all_students',
            'student_level',
            'student_class',
            'students_by_level',
            'students_by_class'
        ];

        $employeeGroupTypes = [
            'all_employees',
            'employee_contract',
            'employees_by_contract',
            'employees_by_type'
        ];

        if (in_array($groupTypeOrId, $studentGroupTypes) || strpos($groupTypeOrId, 'student') === 0) {
            return 'student';
        } elseif (in_array($groupTypeOrId, $employeeGroupTypes) || strpos($groupTypeOrId, 'employee') === 0) {
            return 'employee';
        } else {
            // For 'all' type, this should not be reached as IDs are prefixed
            return 'student'; // Default to student for safety
        }
    }
    
    /**
     * Extract numeric ID from prefixed ID strings like 'student_123' or 'employee_456'
     */
    private function extractNumericId($prefixedId)
    {
        // Use regex to extract numeric part after underscore or before end of string
        if (preg_match('/(?:_)?(\d+)$/', $prefixedId, $matches)) {
            return (int) $matches[1];
        }
        return null;
    }
}
