<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Responsive Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --costa-del-sol: #7d9178;
            --locust: #a8b89a;
            --mid-gray: #6c757d;
            --thistle-green: #e6ebd6;
        }

        /* Default display settings */
        .notifications-table-wrapper {
            display: block;
        }
        
        .notifications-card-view {
            display: none;
        }
        
        /* Card view for mobile */
        @media (max-width: 768px) {
            .notifications-table-wrapper {
                display: none !important;
            }
            
            .notifications-card-view {
                display: block !important;
                background: transparent;
            }
            
            .notification-card {
                background: #ffffff !important;
                border: 1px solid #e9ecef;
                border-radius: 12px;
                margin-bottom: 1rem;
                padding: 1.25rem;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                border-left: 4px solid var(--costa-del-sol);
            }
            
            .notification-card.unread {
                background-color: rgba(230, 235, 214, 0.3) !important;
                border-left-color: var(--locust);
                border-left-width: 5px;
            }
            
            .notification-card-title {
                font-weight: 600;
                color: var(--costa-del-sol) !important;
                font-size: 1rem;
                margin-bottom: 0.5rem;
            }
            
            .notification-card-meta {
                display: flex;
                flex-wrap: wrap;
                gap: 0.75rem;
                margin-bottom: 1rem;
                font-size: 0.85rem;
                color: var(--mid-gray);
            }
            
            .priority-badge {
                padding: 4px 8px;
                border-radius: 20px;
                font-size: 0.75rem;
                font-weight: 500;
            }
            
            .priority-medium {
                background-color: rgba(255, 193, 7, 0.15);
                color: #856404;
            }
            
            .status-badge {
                padding: 4px 8px;
                border-radius: 20px;
                font-size: 0.75rem;
                font-weight: 500;
            }
            
            .status-unread {
                background-color: rgba(255, 193, 7, 0.15);
                color: #856404;
            }
        }
    </style>
</head>
<body style="background-color: #f8f9fa;">
    <div class="container-fluid p-4">
        <h2 class="mb-4">Responsive Test - Notifications</h2>
        
        <div class="alert alert-info">
            <strong>Screen width test:</strong>
            <span class="d-block d-md-none">Mobile view (< 768px)</span>
            <span class="d-none d-md-block">Desktop view (>= 768px)</span>
        </div>
        
        <!-- Table View (Desktop) -->
        <div class="notifications-table-wrapper">
            <div class="card">
                <div class="card-body">
                    <h5>Table View (Desktop)</h5>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Title</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>Test Notification</td>
                                <td><span class="badge bg-warning">Unread</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- Card View (Mobile) -->
        <div class="notifications-card-view">
            <h5 class="mb-3">Card View (Mobile)</h5>
            
            <div class="notification-card unread">
                <div class="notification-card-title">Test Notification Title</div>
                <div class="notification-card-meta">
                    <span class="priority-badge priority-medium">Medium</span>
                    <span>Dec 11, 2024 14:30</span>
                    <span class="status-badge status-unread">
                        <i class="fas fa-circle me-1" style="font-size: 8px;"></i> Unread
                    </span>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-info btn-sm flex-1">
                        <i class="fas fa-eye me-1"></i> View
                    </button>
                    <button class="btn btn-success btn-sm flex-1">
                        <i class="fas fa-check me-1"></i> Mark Read
                    </button>
                </div>
            </div>
            
            <div class="notification-card">
                <div class="notification-card-title">Another Test Notification</div>
                <div class="notification-card-meta">
                    <span class="priority-badge priority-medium">High</span>
                    <span>Dec 10, 2024 09:15</span>
                    <span class="status-badge status-read">
                        <i class="fas fa-check-circle me-1"></i> Read
                    </span>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-info btn-sm flex-1">
                        <i class="fas fa-eye me-1"></i> View
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
