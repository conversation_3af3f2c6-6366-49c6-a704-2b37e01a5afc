<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Tymon\JWTAuth\Contracts\JWTSubject;

class Employee extends Authenticatable implements J<PERSON>TSubject
{
    use HasFactory, Notifiable;
    
    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        static::creating(function ($employee) {
            // Generate username if not provided
            if (empty($employee->username)) {
                $employee->username = self::generateUniqueUsername();
            }
            
            // Set default password if not provided
            if (empty($employee->password)) {
                $employee->password = bcrypt('emp123');
            }
        });
    }

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'full_name',
        'contract_type',
        'employee_type',
        'phone',
        'job_status',
        'automatic_number',
        'financial_number',
        'state_cooperative_number',
        'bank_account_number',
        'username',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'password' => 'hashed',
        ];
    }

    /**
     * Get the identifier that will be stored in the subject claim of the JWT.
     */
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    /**
     * Return a key value array, containing any custom claims to be added to the JWT.
     */
    public function getJWTCustomClaims()
    {
        return [
            'user_type' => 'employee',
            'full_name' => $this->full_name,
            'contract_type' => $this->contract_type,
        ];
    }
    
    // generateUniqueEmployeeId method removed as employee_id field no longer exists in the database
    
    /**
     * Generate a unique username
     *
     * @return string
     */
    protected static function generateUniqueUsername()
    {
        $prefix = 'emp';
        $randomNumber = mt_rand(10000, 99999); // 5-digit random number
        $username = $prefix . $randomNumber;
        
        // Check if the generated username already exists
        while (self::where('username', $username)->exists()) {
            $randomNumber = mt_rand(10000, 99999);
            $username = $prefix . $randomNumber;
        }
        
        return $username;
    }

    /**
     * Authenticate employee by username, phone, or other identifier
     */
    public static function authenticate($identifier, $password)
    {
        // Try to find employee by username first, then by phone
        $employee = self::where('username', $identifier)
                        ->orWhere('phone', $identifier)
                        ->first();

        if ($employee && \Hash::check($password, $employee->password)) {
            return $employee;
        }

        return null;
    }
}
