<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Direct test route
Route::get('direct-test', function() {
    return response()->json([
        'success' => true,
        'message' => 'Direct route works!',
        'timestamp' => now()
    ]);
});

// Employee dashboard direct route
Route::get('employee/dashboard', function() {
    try {
        $employee = \App\Models\Employee::where('username', 'test_employee')->first();

        if (!$employee) {
            return response()->json([
                'success' => false,
                'message' => 'Test employee not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'employee_info' => [
                    'name' => $employee->full_name,
                    'username' => $employee->username,
                    'employee_type' => $employee->employee_type,
                    'contract_type' => $employee->contract_type,
                    'job_status' => $employee->job_status,
                    'phone' => $employee->phone,
                ],
                'notifications' => [
                    'total' => 5,
                    'unread' => 2,
                ],
                'work_info' => [
                    'employee_type' => $employee->employee_type,
                    'contract_type' => $employee->contract_type,
                    'job_status' => $employee->job_status,
                    'automatic_number' => $employee->automatic_number,
                    'financial_number' => $employee->financial_number,
                    'state_cooperative_number' => $employee->state_cooperative_number,
                    'bank_account_number' => $employee->bank_account_number,
                ],
            ],
            'message' => 'Dashboard data retrieved successfully'
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Error: ' . $e->getMessage()
        ], 500);
    }
});

// Public routes (no authentication required)
Route::prefix('auth')->group(function () {
    Route::post('admin/login', [AuthController::class, 'adminLogin']);
    Route::post('student/login', [AuthController::class, 'studentLogin']);
    Route::post('employee/login', [AuthController::class, 'employeeLogin']);
});

// Protected routes (authentication required) - Accept admin, student, or employee tokens
Route::middleware('auth:admin_api,api,employee_api')->group(function () {

    // Authentication routes
    Route::prefix('auth')->group(function () {
        Route::post('logout', [AuthController::class, 'logout']);
    });

    // Student management routes (Admin)
    Route::prefix('students')->group(function () {
        Route::get('/', [\App\Http\Controllers\Api\StudentController::class, 'index']);
        Route::post('/', [\App\Http\Controllers\Api\StudentController::class, 'store']);
        Route::get('/{id}', [\App\Http\Controllers\Api\StudentController::class, 'show']);
        Route::put('/{id}', [\App\Http\Controllers\Api\StudentController::class, 'update']);
        Route::delete('/{id}', [\App\Http\Controllers\Api\StudentController::class, 'destroy']);
        Route::get('/classes/list', [\App\Http\Controllers\Api\StudentController::class, 'getClasses']);

        // Admin only - Delete all students
        Route::delete('/admin/delete-all', [\App\Http\Controllers\Api\StudentController::class, 'deleteAll']);
    });

    // Student dashboard routes (Student only)
    Route::prefix('student')->group(function () {
        // Personal information
        Route::get('/personal-info', [\App\Http\Controllers\Api\StudentController::class, 'getPersonalInfo']);
        Route::put('/personal-info', [\App\Http\Controllers\Api\StudentController::class, 'updatePersonalInfo']);

        // Academic information
        Route::get('/academic-info', [\App\Http\Controllers\Api\StudentController::class, 'getAcademicInfo']);

        // Test authentication
        Route::get('/test-auth', [\App\Http\Controllers\Api\StudentController::class, 'testAuth']);

        // Dashboard data
        Route::get('/dashboard', [\App\Http\Controllers\Api\StudentController::class, 'getDashboard']);

        // Notifications
        Route::get('/notifications', [\App\Http\Controllers\Api\StudentController::class, 'getNotifications']);
        Route::post('/notifications/{id}/mark-read', [\App\Http\Controllers\Api\StudentController::class, 'markNotificationAsRead']);
        Route::post('/notifications/{id}/mark-unread', [\App\Http\Controllers\Api\StudentController::class, 'markNotificationAsUnread']);
    });

    // Employee dashboard routes (protected)
    Route::middleware('auth:employee_api')->prefix('employee')->group(function () {
        // Test authentication
        Route::get('/test-auth', [\App\Http\Controllers\Api\EmployeeController::class, 'testAuth']);

        // Dashboard data
        Route::get('/dashboard', [\App\Http\Controllers\Api\EmployeeController::class, 'getDashboard']);

        // Notifications
        Route::get('/notifications', [\App\Http\Controllers\Api\EmployeeController::class, 'getNotifications']);
        Route::post('/notifications/{id}/mark-read', [\App\Http\Controllers\Api\EmployeeController::class, 'markNotificationAsRead']);
        Route::post('/notifications/{id}/mark-unread', [\App\Http\Controllers\Api\EmployeeController::class, 'markNotificationAsUnread']);
    });

    // Temporary unprotected routes for testing
    Route::prefix('employee-test')->group(function () {
        Route::get('/simple', [\App\Http\Controllers\Api\EmployeeController::class, 'testSimple']);
        Route::get('/dashboard', [\App\Http\Controllers\Api\EmployeeController::class, 'getDashboard']);
        Route::get('/test-auth', [\App\Http\Controllers\Api\EmployeeController::class, 'testAuth']);
    });

    // Test controller routes
    Route::prefix('test')->group(function () {
        Route::get('/simple', [\App\Http\Controllers\Api\TestController::class, 'simple']);
        Route::get('/dashboard', [\App\Http\Controllers\Api\TestController::class, 'dashboard']);
    });

    // Employee management routes
    Route::prefix('employees')->group(function () {
        Route::get('/', [\App\Http\Controllers\Api\EmployeeController::class, 'index']);
        Route::post('/', [\App\Http\Controllers\Api\EmployeeController::class, 'store']);
        Route::get('/{id}', [\App\Http\Controllers\Api\EmployeeController::class, 'show']);
        Route::put('/{id}', [\App\Http\Controllers\Api\EmployeeController::class, 'update']);
        Route::delete('/{id}', [\App\Http\Controllers\Api\EmployeeController::class, 'destroy']);

        // Get dropdown options from database
        Route::get('/options/contract-types', [\App\Http\Controllers\Api\EmployeeController::class, 'getContractTypes']);
        Route::get('/options/employee-types', [\App\Http\Controllers\Api\EmployeeController::class, 'getEmployeeTypes']);
        Route::get('/options/job-statuses', [\App\Http\Controllers\Api\EmployeeController::class, 'getJobStatuses']);

        // Admin only - Delete all employees
        Route::delete('/admin/delete-all', [\App\Http\Controllers\Api\EmployeeController::class, 'deleteAll']);
    });
    
    // Notification management routes
    Route::prefix('notifications')->group(function () {
        Route::get('/', [\App\Http\Controllers\Api\NotificationController::class, 'index']);
        Route::post('/', [\App\Http\Controllers\Api\NotificationController::class, 'store']);
        Route::post('/send', [\App\Http\Controllers\Api\NotificationController::class, 'send']);
        Route::get('/{id}', [\App\Http\Controllers\Api\NotificationController::class, 'show']);
        Route::put('/{id}', [\App\Http\Controllers\Api\NotificationController::class, 'update']);
        Route::delete('/{id}', [\App\Http\Controllers\Api\NotificationController::class, 'destroy']);
        Route::post('/{id}/mark-read', [\App\Http\Controllers\Api\NotificationController::class, 'markAsRead']);
        Route::get('/student/notifications', [\App\Http\Controllers\Api\NotificationController::class, 'studentNotifications']);
        Route::get('/employee/notifications', [\App\Http\Controllers\Api\NotificationController::class, 'employeeNotifications']);
        
        // Bulk operations
        Route::delete('/bulk/delete', [\App\Http\Controllers\Api\NotificationController::class, 'bulkDelete']);
        Route::patch('/bulk/update-status', [\App\Http\Controllers\Api\NotificationController::class, 'bulkUpdateStatus']);
    });
    
    // System Logs API routes
    Route::prefix('logs')->group(function () {
        Route::get('/', [\App\Http\Controllers\Api\LogController::class, 'index']);
        Route::get('/{filename}', [\App\Http\Controllers\Api\LogController::class, 'show']);
        Route::delete('/{filename}', [\App\Http\Controllers\Api\LogController::class, 'destroy']);
    });
    
    // Database Backups API routes
    Route::prefix('backups')->group(function () {
        Route::get('/', [\App\Http\Controllers\Api\BackupController::class, 'index']);
        Route::post('/', [\App\Http\Controllers\Api\BackupController::class, 'create']);
        Route::get('/{filename}', [\App\Http\Controllers\Api\BackupController::class, 'download']);
        Route::delete('/{filename}', [\App\Http\Controllers\Api\BackupController::class, 'destroy']);
    });
});

// Health check route
Route::get('health', function () {
    return response()->json([
        'success' => true,
        'message' => 'AppNote API is running',
        'timestamp' => now(),
        'version' => '1.0.0'
    ]);
});

// Test database connection
Route::get('test-db', function () {
    try {
        \DB::connection()->getPdo();

        return response()->json([
            'success' => true,
            'message' => 'Database connection successful',
            'database' => config('database.connections.mysql.database'),
            'host' => config('database.connections.mysql.host'),
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Database connection failed',
            'error' => $e->getMessage()
        ], 500);
    }
});

// API documentation
Route::get('docs', function () {
    return response()->json([
        'success' => true,
        'message' => 'AppNote API Documentation',
        'endpoints' => [
            'Authentication' => [
                'POST /api/auth/admin/login' => 'Admin login (supports username, name, or email)',
                'POST /api/auth/student/login' => 'Student login',
                'POST /api/auth/employee/login' => 'Employee login',
                'POST /api/auth/logout' => 'Logout (requires auth)',
            ],
            'Notifications' => [
                'GET /api/notifications' => 'Get all notifications',
                'POST /api/notifications' => 'Create a new notification',
                'POST /api/notifications/send' => 'Send a notification',
                'GET /api/notifications/{id}' => 'Get a specific notification',
                'PUT /api/notifications/{id}' => 'Update a notification',
                'DELETE /api/notifications/{id}' => 'Delete a notification',
                'POST /api/notifications/{id}/mark-read' => 'Mark notification as read',
                'GET /api/notifications/student/notifications' => 'Get student notifications',
                'GET /api/notifications/employee/notifications' => 'Get employee notifications',
                'DELETE /api/notifications/bulk/delete' => 'Bulk delete notifications',
                'PATCH /api/notifications/bulk/update-status' => 'Bulk update notification status',
            ],
            'Utility' => [
                'GET /api/health' => 'API health check',
                'GET /api/test-db' => 'Test database connection',
                'GET /api/docs' => 'This documentation',
            ],
        ],
        'authentication' => [
            'type' => 'JWT Bearer Token',
            'header' => 'Authorization: Bearer {token}',
            'expiry' => '60 minutes',
        ],
        'base_url' => url('/api'),
    ]);
});
