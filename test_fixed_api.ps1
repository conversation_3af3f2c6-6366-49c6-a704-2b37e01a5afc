# Comprehensive API Testing Script - After Fixes
$baseUrl = "http://localhost/appnote-api/public/api"

Write-Host "=== Testing Fixed Multi-Field Authentication API ===" -ForegroundColor Green

# Test 1: Admin Login with different methods
Write-Host "`n1. Testing Admin Login Methods..." -ForegroundColor Yellow

$adminTests = @(
    @{ name = "Username (admin123)"; username = "admin123"; password = "admin123" },
    @{ name = "Email (<EMAIL>)"; username = "<EMAIL>"; password = "admin123" },
    @{ name = "Username (admin)"; username = "admin"; password = "admin123" },
    @{ name = "Email (<EMAIL>)"; username = "<EMAIL>"; password = "admin123" }
)

$adminToken = $null

foreach ($test in $adminTests) {
    Write-Host "  Testing: $($test.name)" -ForegroundColor Cyan
    
    try {
        $loginBody = @{
            username = $test.username
            password = $test.password
        } | ConvertTo-Json -Depth 2

        $response = Invoke-WebRequest -Uri "$baseUrl/auth/admin/login" -Method POST -ContentType "application/json" -Body $loginBody -ErrorAction Stop
        $data = $response.Content | ConvertFrom-Json
        
        if ($data.success) {
            Write-Host "    ✅ SUCCESS: $($data.message)" -ForegroundColor Green
            Write-Host "       User: $($data.data.user.name) ($($data.data.user.email))" -ForegroundColor Gray
            if (-not $adminToken) { $adminToken = $data.data.token }
        } else {
            Write-Host "    ❌ FAILED: $($data.message)" -ForegroundColor Red
        }
    } catch {
        Write-Host "    ❌ ERROR: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test 2: Test Student API with new schema
Write-Host "`n2. Testing Student API (Create/Read)..." -ForegroundColor Yellow

if ($adminToken) {
    try {
        # Create a test student with new schema
        $studentData = @{
            username = "test_student_001"
            full_name = "Test Student"
            phone = "70123456"
            class = "Grade 10"
            nationality = "Lebanese"
            specialization = "Science"
            section = "A"
            level = "1"
            password = "test123"
        } | ConvertTo-Json -Depth 2

        $headers = @{
            "Authorization" = "Bearer $adminToken"
            "Content-Type" = "application/json"
        }

        Write-Host "  Creating student with new schema..." -ForegroundColor Cyan
        $response = Invoke-WebRequest -Uri "$baseUrl/students" -Method POST -Headers $headers -Body $studentData -ErrorAction Stop
        $data = $response.Content | ConvertFrom-Json
        
        if ($data.success) {
            Write-Host "    ✅ Student created successfully" -ForegroundColor Green
            Write-Host "       ID: $($data.data.id), Username: $($data.data.username)" -ForegroundColor Gray
            
            # Test student login
            Write-Host "  Testing student login..." -ForegroundColor Cyan
            $studentLoginData = @{
                identifier = "test_student_001"
                password = "test123"
            } | ConvertTo-Json -Depth 2

            $loginResponse = Invoke-WebRequest -Uri "$baseUrl/auth/student/login" -Method POST -ContentType "application/json" -Body $studentLoginData -ErrorAction Stop
            $loginData = $loginResponse.Content | ConvertFrom-Json
            
            if ($loginData.success) {
                Write-Host "    ✅ Student login successful" -ForegroundColor Green
                Write-Host "       Student: $($loginData.data.student.full_name)" -ForegroundColor Gray
            } else {
                Write-Host "    ❌ Student login failed: $($loginData.message)" -ForegroundColor Red
            }
        } else {
            Write-Host "    ❌ Student creation failed: $($data.message)" -ForegroundColor Red
            if ($data.errors) {
                Write-Host "       Errors: $($data.errors | ConvertTo-Json)" -ForegroundColor Red
            }
        }
    } catch {
        Write-Host "    ❌ ERROR: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.Exception.Response) {
            $statusCode = $_.Exception.Response.StatusCode
            Write-Host "       Status Code: $statusCode" -ForegroundColor Red
        }
    }
} else {
    Write-Host "  ⚠️  Skipping student tests - no admin token available" -ForegroundColor Yellow
}

# Test 3: Test Employee API with new schema
Write-Host "`n3. Testing Employee API (Create)..." -ForegroundColor Yellow

if ($adminToken) {
    try {
        $employeeData = @{
            full_name = "Test Employee"
            phone = "81123456"
            contract_type = "Full Time"
            employee_type = "Teacher"
            job_status = "Active"
            username = "test_emp_001"
            password = "emp123"
        } | ConvertTo-Json -Depth 2

        $headers = @{
            "Authorization" = "Bearer $adminToken"
            "Content-Type" = "application/json"
        }

        Write-Host "  Creating employee with new schema..." -ForegroundColor Cyan
        $response = Invoke-WebRequest -Uri "$baseUrl/employees" -Method POST -Headers $headers -Body $employeeData -ErrorAction Stop
        $data = $response.Content | ConvertFrom-Json
        
        if ($data.success) {
            Write-Host "    ✅ Employee created successfully" -ForegroundColor Green
            Write-Host "       ID: $($data.data.id), Username: $($data.data.username)" -ForegroundColor Gray
            
            # Test employee login
            Write-Host "  Testing employee login..." -ForegroundColor Cyan
            $empLoginData = @{
                identifier = "test_emp_001"
                password = "emp123"
            } | ConvertTo-Json -Depth 2

            $loginResponse = Invoke-WebRequest -Uri "$baseUrl/auth/employee/login" -Method POST -ContentType "application/json" -Body $empLoginData -ErrorAction Stop
            $loginData = $loginResponse.Content | ConvertFrom-Json
            
            if ($loginData.success) {
                Write-Host "    ✅ Employee login successful" -ForegroundColor Green
                Write-Host "       Employee: $($loginData.data.employee.full_name)" -ForegroundColor Gray
            } else {
                Write-Host "    ❌ Employee login failed: $($loginData.message)" -ForegroundColor Red
            }
        } else {
            Write-Host "    ❌ Employee creation failed: $($data.message)" -ForegroundColor Red
            if ($data.errors) {
                Write-Host "       Errors: $($data.errors | ConvertTo-Json)" -ForegroundColor Red
            }
        }
    } catch {
        Write-Host "    ❌ ERROR: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "  ⚠️  Skipping employee tests - no admin token available" -ForegroundColor Yellow
}

Write-Host "`n=== Testing Complete ===" -ForegroundColor Green
Write-Host "✅ All API endpoints have been updated to use the correct database schema!" -ForegroundColor Green
