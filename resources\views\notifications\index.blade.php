@extends('layouts.app')

@section('content')
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 style="color: var(--costa-del-sol);">Notification Management</h2>
        <div>
            <a href="{{ route('notifications.create') }}" class="btn btn-primary me-2">
                <i class="fas fa-plus"></i> Send New Notification
            </a>
        </div>
    </div>
    
    <!-- Bulk Actions Bar -->
    <div class="card mb-4" id="bulk-actions-card" style="display: none;">
        <div class="card-body d-flex align-items-center bg-light">
            <span class="me-3"><span id="selected-count">0</span> items selected</span>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-sm btn-danger" id="bulk-delete-btn" title="Delete selected notifications">
                    <i class="fas fa-trash-alt me-1"></i> Delete Selected
                </button>
                <button type="button" class="btn btn-sm btn-success" id="bulk-mark-sent-btn" title="Mark selected as sent">
                    <i class="fas fa-check-circle me-1"></i> Mark as Sent
                </button>
                <button type="button" class="btn btn-sm btn-warning" id="bulk-mark-draft-btn" title="Mark selected as draft">
                    <i class="fas fa-pencil-alt me-1"></i> Mark as Draft
                </button>
            </div>
            <button type="button" class="btn btn-sm btn-outline-secondary ms-auto" id="clear-selection-btn" title="Clear all selections">
                <i class="fas fa-times me-1"></i> Clear Selection
            </button>
        </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success">
            {{ session('success') }}
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger">
            {{ session('error') }}
        </div>
    @endif

    <!-- Notifications Table -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <span>Notifications History</span>
            <form class="d-flex" action="{{ route('notifications.index') }}" method="GET">
                <input class="form-control me-2" type="search" name="search" placeholder="Search notifications" value="{{ request('search') }}">
                <button class="btn btn-outline-success" type="submit">Search</button>
            </form>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th width="40">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="select-all">
                                </div>
                            </th>
                            <th>ID</th>
                            <th>Title</th>
                            <th>Priority</th>
                            <th>Recipient Group</th>
                            <th>Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($notifications as $notification)
                            <tr data-id="{{ $notification->id }}">
                                <td>
                                    <div class="form-check">
                                        <input class="form-check-input notification-checkbox" type="checkbox" value="{{ $notification->id }}">
                                    </div>
                                </td>
                                <td>{{ $notification->id }}</td>
                                <td>{{ $notification->title }}</td>
                                <td>
                                    <span class="badge bg-{{ $notification->priority === 'high' ? 'danger' : ($notification->priority === 'medium' ? 'warning' : 'info') }}">
                                        {{ ucfirst($notification->priority) }}
                                    </span>
                                </td>
                                <td>{{ ucfirst(str_replace('_', ' ', $notification->recipient_type)) }}</td>
                                <td>{{ $notification->created_at->format('Y-m-d H:i') }}</td>
                                <td>
                                    <span class="badge bg-{{ $notification->status === 'sent' ? 'success' : ($notification->status === 'draft' ? 'secondary' : 'danger') }}">
                                        {{ ucfirst($notification->status) }}
                                    </span>
                                </td>
                                <td>
                                    <a href="{{ route('notifications.show', $notification->id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="8" class="text-center">No notifications found</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
            <div class="d-flex justify-content-center mt-4">
                {{ $notifications->links('pagination::bootstrap-5') }}
            </div>
        </div>
    </div>
</div>
@endsection

@section('styles')
<style>
    /* Custom checkbox styling */
    .form-check-input {
        width: 1.2em;
        height: 1.2em;
        border: 2px solid #6c757d;
        border-radius: 4px;
        background-color: transparent;
        transition: all 0.3s ease;
    }

    .form-check-input:checked {
        background-color: #28a745;
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }

    .form-check-input:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }

    .form-check-input:hover {
        border-color: #28a745;
        transform: scale(1.05);
    }

    /* Select all checkbox special styling */
    #select-all {
        background-color: #f8f9fa;
        border: 2px solid #495057;
    }

    #select-all:checked {
        background-color: #007bff;
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    #select-all:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    #select-all:hover {
        border-color: #007bff;
    }

    /* Bulk actions card styling */
    #bulk-actions-card {
        border-left: 4px solid #28a745;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    /* Bulk action buttons styling */
    #bulk-actions-card .btn {
        border-radius: 6px;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    #bulk-actions-card .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    /* Selected count styling */
    #selected-count {
        font-weight: bold;
        color: #28a745;
        font-size: 1.1em;
    }

    /* Table row styling when checkbox is checked */
    tr:has(.notification-checkbox:checked) {
        background-color: rgba(40, 167, 69, 0.08);
        border-left: 3px solid #28a745;
        transition: all 0.3s ease;
    }

    /* Table row hover effect */
    tbody tr:hover {
        background-color: rgba(0, 123, 255, 0.05);
        transform: scale(1.001);
        transition: all 0.2s ease;
    }

    /* Table header styling */
    thead th {
        background-color: #f8f9fa;
        border-bottom: 2px solid #dee2e6;
        font-weight: 600;
        color: #495057;
    }

    /* Animation for bulk actions card */
    #bulk-actions-card {
        animation: slideDown 0.3s ease-out;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const selectAllCheckbox = document.getElementById('select-all');
        const notificationCheckboxes = document.querySelectorAll('.notification-checkbox');
        const bulkActionsCard = document.getElementById('bulk-actions-card');
        const selectedCountSpan = document.getElementById('selected-count');
        const bulkDeleteBtn = document.getElementById('bulk-delete-btn');
        const bulkMarkSentBtn = document.getElementById('bulk-mark-sent-btn');
        const bulkMarkDraftBtn = document.getElementById('bulk-mark-draft-btn');
        const clearSelectionBtn = document.getElementById('clear-selection-btn');
        
        // Function to update the bulk actions UI
        function updateBulkActionsUI() {
            const selectedCheckboxes = document.querySelectorAll('.notification-checkbox:checked');
            const selectedCount = selectedCheckboxes.length;
            
            if (selectedCount > 0) {
                bulkActionsCard.style.display = 'block';
                selectedCountSpan.textContent = selectedCount;
            } else {
                bulkActionsCard.style.display = 'none';
            }
            
            // Update select-all checkbox state
            selectAllCheckbox.checked = selectedCount > 0 && selectedCount === notificationCheckboxes.length;
        }
        
        // Toggle all checkboxes
        selectAllCheckbox.addEventListener('change', function() {
            notificationCheckboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
            });
            updateBulkActionsUI();
        });
        
        // Individual checkbox change
        notificationCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateBulkActionsUI);
        });
        
        // Clear selection
        clearSelectionBtn.addEventListener('click', function() {
            notificationCheckboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            updateBulkActionsUI();
        });
        
        // Handle bulk delete action
        bulkDeleteBtn.addEventListener('click', function() {
            if (confirm('Are you sure you want to delete the selected notifications?')) {
                // Get IDs of selected notifications
                const selectedIds = Array.from(document.querySelectorAll('.notification-checkbox:checked'))
                    .map(checkbox => checkbox.value);
                
                // Send delete request
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '{{ route("notifications.bulkDelete") }}';
                form.style.display = 'none';
                
                // Add CSRF token
                const csrfToken = document.createElement('input');
                csrfToken.type = 'hidden';
                csrfToken.name = '_token';
                csrfToken.value = '{{ csrf_token() }}';
                form.appendChild(csrfToken);
                
                // Add method field
                const methodField = document.createElement('input');
                methodField.type = 'hidden';
                methodField.name = '_method';
                methodField.value = 'DELETE';
                form.appendChild(methodField);
                
                // Add selected IDs
                selectedIds.forEach(id => {
                    const input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = 'notification_ids[]';
                    input.value = id;
                    form.appendChild(input);
                });
                
                document.body.appendChild(form);
                form.submit();
            }
        });
        
        // Handle bulk mark as sent
        bulkMarkSentBtn.addEventListener('click', function() {
            performBulkStatusUpdate('sent');
        });
        
        // Handle bulk mark as draft
        bulkMarkDraftBtn.addEventListener('click', function() {
            performBulkStatusUpdate('draft');
        });
        
        // Function to update status
        function performBulkStatusUpdate(status) {
            const selectedIds = Array.from(document.querySelectorAll('.notification-checkbox:checked'))
                .map(checkbox => checkbox.value);
            
            if (selectedIds.length === 0) return;
            
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '{{ route("notifications.bulkUpdateStatus") }}';
            form.style.display = 'none';
            
            // Add CSRF token
            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = '{{ csrf_token() }}';
            form.appendChild(csrfToken);
            
            // Add method field
            const methodField = document.createElement('input');
            methodField.type = 'hidden';
            methodField.name = '_method';
            methodField.value = 'PATCH';
            form.appendChild(methodField);
            
            // Add status field
            const statusInput = document.createElement('input');
            statusInput.type = 'hidden';
            statusInput.name = 'status';
            statusInput.value = status;
            form.appendChild(statusInput);
            
            // Add selected IDs
            selectedIds.forEach(id => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'notification_ids[]';
                input.value = id;
                form.appendChild(input);
            });
            
            document.body.appendChild(form);
            form.submit();
        }
    });
</script>
@endsection
