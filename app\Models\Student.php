<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Tymon\JWTAuth\Contracts\JWTSubject;

class Student extends Authenticatable implements JWTSubject
{
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'username',
        'full_name',
        'nationality',
        'phone',
        'specialization',
        'section',
        'class',
        'level',
        'result',
        'password',
        'is_active',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     */
    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
            'password' => 'hashed',
        ];
    }

    /**
     * Get the identifier that will be stored in the subject claim of the JWT.
     */
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    /**
     * Return a key value array, containing any custom claims to be added to the JWT.
     */
    public function getJWTCustomClaims()
    {
        return [
            'user_type' => 'student',
            'username' => $this->username,
            'full_name' => $this->full_name,
            'class' => $this->class,
            'level' => $this->level,
        ];
    }

    /**
     * Get notifications for this student
     */
    public function notifications()
    {
        return $this->hasMany(NotificationRecipient::class, 'recipient_id')
                    ->where('recipient_type', 'student')
                    ->with('notification');
    }

    /**
     * Authenticate student by username and password
     */
    public static function authenticate($username, $password)
    {
        $student = self::where('username', $username)
                       ->where('is_active', true)
                       ->first();

        if ($student && \Hash::check($password, $student->password)) {
            return $student;
        }

        return null;
    }
    
    /**
     * Authenticate student by phone and password (for backward compatibility)
     */
    public static function authenticateByPhone($phone, $password)
    {
        $student = self::where('phone', $phone)
                       ->where('is_active', true)
                       ->first();

        if ($student && \Hash::check($password, $student->password)) {
            return $student;
        }

        return null;
    }
}
