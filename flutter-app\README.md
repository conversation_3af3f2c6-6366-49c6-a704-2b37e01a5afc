# معهد النبطية الفني - Flutter App

A clean Flutter application for Nabatieh Technical Institute that connects to a Laravel API for authentication and user management.

## Features

- **Multi-User Authentication System**
  - Admin login (email + password)
  - Student login (username + password)
  - Employee login (username + password)

- **Modern UI/UX**
  - Clean and intuitive interface with earth-tone color palette
  - Role-based dashboards with distinct color themes
  - Responsive design
  - Material Design 3
  - Beautiful earth-tone colors: Parchment, Costa del Sol, Locust, Gurkha, Avocado

- **State Management**
  - Provider pattern for state management
  - Persistent authentication
  - Automatic session handling

## Laravel API Integration

### API Configuration

The app is configured to connect to your Laravel API located at:
```
C:\laragon\www\appnote-api
```

**Base URL:** `http://localhost/appnote-api/public/api`

### Required API Endpoints

Your Laravel API should implement the following endpoints:

#### Authentication Endpoints
- `POST /api/admin/login` - Admin authentication
- `POST /api/student/login` - Student authentication
- `POST /api/employee/login` - Employee authentication

#### Profile Endpoints
- `GET /api/admin/profile` - Get admin profile
- `GET /api/student/profile` - Get student profile
- `GET /api/employee/profile` - Get employee profile

### Expected Request/Response Format

#### Login Request Format
```json
// Admin Login
{
  "email": "<EMAIL>",
  "password": "password"
}

// Student/Employee Login
{
  "username": "student123",
  "password": "password"
}
```

#### Login Response Format
```json
{
  "token": "bearer_token_here",
  "user": {
    "id": 1,
    "email": "<EMAIL>", // for admin
    "username": "student123",     // for student/employee
    "name": "User Name",
    "created_at": "2024-01-01T00:00:00.000000Z",
    "updated_at": "2024-01-01T00:00:00.000000Z"
  },
  "message": "Login successful"
}
```

## Setup Instructions

### Prerequisites
- Flutter SDK (3.32.2 or later)
- Dart SDK (3.8.1 or later)
- Laravel API running on Laragon

### Installation

1. **Clone or navigate to the project directory:**
   ```bash
   cd C:\Users\<USER>\Desktop\apps\flutter-app
   ```

2. **Install dependencies:**
   ```bash
   flutter pub get
   ```

3. **Configure API URL:**
   - Open `lib/utils/constants.dart`
   - Update the `baseUrl` if your Laravel API is running on a different URL:
   ```dart
   static const String baseUrl = 'http://localhost/appnote-api/public/api';
   ```

4. **Run the app:**
   ```bash
   flutter run
   ```

## Dependencies

- **flutter**: SDK
- **provider**: ^6.1.5 - State management
- **http**: ^1.4.0 - HTTP requests
- **shared_preferences**: ^2.5.3 - Local storage

## Usage

1. **Launch the app** - You'll see the login selection screen
2. **Choose user type** - Admin, Student, or Employee
3. **Enter credentials** - Based on the selected user type
4. **Access dashboard** - Role-specific dashboard with user information

## Development Notes

- The app uses Provider for state management
- Authentication tokens are stored securely using SharedPreferences
- All API calls include proper error handling
- The UI follows Material Design 3 guidelines
- The app supports hot reload for faster development

## Troubleshooting

### Common Issues

1. **API Connection Failed**
   - Ensure Laravel API is running
   - Check the base URL in `constants.dart`
   - Verify CORS settings in Laravel

2. **Login Failed**
   - Check API endpoint implementations
   - Verify request/response format
   - Check Laravel logs for errors

3. **Build Issues**
   - Run `flutter clean`
   - Run `flutter pub get`
   - Restart your IDE
