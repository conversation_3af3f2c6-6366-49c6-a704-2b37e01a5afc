<!-- Notification Detail Modal -->
<div class="modal fade" id="notificationModal{{ $notification->id }}" tabindex="-1" aria-labelledby="notificationModalLabel{{ $notification->id }}" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="notificationModalLabel{{ $notification->id }}">{{ $notification->title }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                @php
                    // Check if notification has attachments
                    $attachmentPaths = $notification->attachment_path ?? [];
                    if (!is_array($attachmentPaths)) {
                        $attachmentPaths = [$attachmentPaths];
                    }
                    $hasAttachments = !empty($attachmentPaths) && !empty(array_filter($attachmentPaths));
                @endphp
                
                <!-- Notification metadata -->
                <div class="d-flex justify-content-between mb-3">
                    <div class="notification-meta">
                        <span class="notification-indicator {{ $isUnread ? 'unread' : 'read' }}"></span>
                        {{ $notification->created_at->format('F d, Y · h:i A') }}
                    </div>
                </div>
                
                <!-- Notification content -->
                <div class="notification-content mb-4">
                    {!! $notification->message !!}
                </div>
                
                <!-- Attachments section -->
                @if($hasAttachments)
                <div class="attachment-section mt-4">
                    <div class="attachment-header">
                        <h6 class="mb-0"><i class="fas fa-paperclip me-2"></i>Attachments</h6>
                    </div>
                    <div class="attachment-container">
                        @php
                        // Since we added cast to array in model, these should be arrays now
                        $paths = $notification->attachment_path ?? [];
                        $names = $notification->attachment_name ?? [];

                        // Ensure they are arrays (fallback for old data)
                        if (!is_array($paths)) {
                            $paths = [$paths];
                        }
                        if (!is_array($names)) {
                            $names = [$names];
                        }
                        @endphp
                        
                        @foreach($paths as $index => $path)
                            @php
                            // Clean up attachment path
                            $cleanPath = trim($path, '"[]\\');
                            $filenameOnly = basename($cleanPath);
                            
                            // Get the corresponding file name if available
                            $displayName = isset($names[$index]) && !empty($names[$index]) ? $names[$index] : $filenameOnly;
                            $displayName = trim($displayName, '"[]\\');
                            
                            // Determine file extension for icon
                            $fileExtension = pathinfo($filenameOnly, PATHINFO_EXTENSION);
                            $fileIcon = 'fa-file'; // default icon
                            
                            // Set appropriate file icon based on extension
                            switch(strtolower($fileExtension)) {
                                case 'pdf': $fileIcon = 'fa-file-pdf'; break;
                                case 'doc': case 'docx': $fileIcon = 'fa-file-word'; break;
                                case 'xls': case 'xlsx': $fileIcon = 'fa-file-excel'; break;
                                case 'ppt': case 'pptx': $fileIcon = 'fa-file-powerpoint'; break;
                                case 'jpg': case 'jpeg': case 'png': case 'gif': case 'bmp': $fileIcon = 'fa-file-image'; break;
                                case 'zip': case 'rar': case 'tar': case 'gz': $fileIcon = 'fa-file-archive'; break;
                                case 'mp3': case 'wav': case 'ogg': $fileIcon = 'fa-file-audio'; break;
                                case 'mp4': case 'avi': case 'mov': case 'wmv': $fileIcon = 'fa-file-video'; break;
                                case 'txt': case 'md': case 'csv': $fileIcon = 'fa-file-alt'; break;
                                default: $fileIcon = 'fa-file'; break;
                            }
                            
                            // Create download URLs with different path patterns
                            $downloadUrl1 = asset('storage/' . $cleanPath);
                            $downloadUrl2 = asset('storage/notifications/' . $filenameOnly);
                            @endphp
                            
                            <div class="attachment-item">
                                <a href="{{ $downloadUrl1 }}" 
                                  class="download-attachment-btn"
                                  target="_blank" 
                                  download="{{ $displayName }}" 
                                  onerror="this.href='{{ $downloadUrl2 }}'">
                                    <div class="attachment-icon">
                                        <i class="fas {{ $fileIcon }}"></i>
                                    </div>
                                    <div class="attachment-details">
                                        <span class="attachment-name" dir="auto" title="{{ $displayName }}">{{ $displayName }}</span>
                                        <small class="attachment-info">{{ strtoupper($fileExtension) }} File</small>
                                    </div>
                                </a>
                            </div>
                        @endforeach
                    </div>
                </div>
                @endif
            </div>
            <div class="modal-footer">
                @if($isUnread)
                    <form action="{{ route('employee.notifications.mark-read', $notification->id) }}" method="POST" class="d-inline">
                        @csrf
                        <button type="submit" class="btn mark-read-btn">
                            <i class="fas fa-check-circle me-1"></i> Mark as Read
                        </button>
                    </form>
                @endif
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
