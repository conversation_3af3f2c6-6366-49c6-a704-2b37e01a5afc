// Service Worker for AppNote PWA
const CACHE_NAME = 'appnote-v1.2.0';
const OFFLINE_URL = '/offline.html';
const RUNTIME_CACHE = 'appnote-runtime-v1.2.0';

console.log('🔧 Service Worker script loaded');
console.log('📦 Cache Name:', CACHE_NAME);
console.log('📦 Runtime Cache:', RUNTIME_CACHE);

// Files to cache for offline functionality
const STATIC_CACHE_URLS = [
    '/',
    '/offline.html',
    '/manifest.json',
    '/browserconfig.xml',
    // CSS files
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
    'https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap',
    // JS files
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js',
    // Icons
    '/icons/icon-72x72.png',
    '/icons/icon-96x96.png',
    '/icons/icon-128x128.png',
    '/icons/icon-144x144.png',
    '/icons/icon-152x152.png',
    '/icons/icon-192x192.png',
    '/icons/icon-384x384.png',
    '/icons/icon-512x512.png',
    '/icons/Icon-maskable-192.png',
    '/icons/Icon-maskable-512.png',
    // Logo
    '/logo.jpeg'
];

// Install event - cache static resources
self.addEventListener('install', event => {
    console.log('🔧 Service Worker: Installing...');

    // Test Cache API availability
    if ('caches' in self) {
        console.log('✅ Cache API is available');
    } else {
        console.error('❌ Cache API is not available');
        return;
    }

    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => {
                console.log('📦 Service Worker: Caching static files');
                console.log('📋 Files to cache:', STATIC_CACHE_URLS.length);
                return cache.addAll(STATIC_CACHE_URLS);
            })
            .then(() => {
                console.log('✅ Service Worker: Installation complete');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('❌ Service Worker: Installation failed', error);
                console.error('❌ Error details:', error.message);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('Service Worker: Activating...');

    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== CACHE_NAME && cacheName !== RUNTIME_CACHE) {
                            console.log('Service Worker: Deleting old cache', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker: Activation complete');
                return self.clients.claim();
            })
    );
});

// Fetch event - serve cached content when offline
self.addEventListener('fetch', event => {
    // Skip non-GET requests
    if (event.request.method !== 'GET') {
        return;
    }

    // Skip chrome-extension and other non-http requests
    if (!event.request.url.startsWith('http')) {
        return;
    }

    const url = new URL(event.request.url);

    // Handle API requests with network-first strategy
    if (url.pathname.startsWith('/api/')) {
        event.respondWith(networkFirstStrategy(event.request));
        return;
    }

    // Handle static assets with cache-first strategy
    if (isStaticAsset(event.request)) {
        event.respondWith(cacheFirstStrategy(event.request));
        return;
    }

    // Handle navigation requests with network-first, fallback to offline page
    if (event.request.mode === 'navigate') {
        event.respondWith(navigationStrategy(event.request));
        return;
    }

    // Default strategy for other requests
    event.respondWith(networkFirstStrategy(event.request));
});

// Network-first strategy for API and dynamic content
async function networkFirstStrategy(request) {
    try {
        const response = await fetch(request);

        if (response.ok) {
            const cache = await caches.open(RUNTIME_CACHE);
            cache.put(request, response.clone());
        }

        return response;
    } catch (error) {
        const cachedResponse = await caches.match(request);
        return cachedResponse || new Response('Offline', {
            status: 503,
            statusText: 'Service Unavailable',
            headers: new Headers({
                'Content-Type': 'text/plain'
            })
        });
    }
}

// Cache-first strategy for static assets
async function cacheFirstStrategy(request) {
    const cachedResponse = await caches.match(request);

    if (cachedResponse) {
        return cachedResponse;
    }

    try {
        const response = await fetch(request);

        if (response.ok) {
            const cache = await caches.open(CACHE_NAME);
            cache.put(request, response.clone());
        }

        return response;
    } catch (error) {
        return new Response('Asset not available offline', {
            status: 503,
            statusText: 'Service Unavailable'
        });
    }
}

// Navigation strategy with offline fallback
async function navigationStrategy(request) {
    try {
        const response = await fetch(request);
        return response;
    } catch (error) {
        return caches.match(OFFLINE_URL);
    }
}

// Check if request is for static asset
function isStaticAsset(request) {
    const url = new URL(request.url);
    return url.pathname.match(/\.(css|js|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot)$/);
}

// Background sync for notifications
self.addEventListener('sync', event => {
    if (event.tag === 'background-sync-notifications') {
        console.log('Service Worker: Background sync for notifications');
        event.waitUntil(syncNotifications());
    }
});

// Push notification handling
self.addEventListener('push', event => {
    console.log('Service Worker: Push notification received', event);

    let notificationData = {
        title: 'معهد النبطية الفني',
        body: 'إشعار جديد من معهد النبطية الفني',
        icon: '/icons/icon-192x192.png',
        badge: '/icons/icon-72x72.png',
        vibrate: [200, 100, 200],
        data: {
            dateOfArrival: Date.now(),
            primaryKey: 1,
            url: '/'
        },
        actions: [
            {
                action: 'view',
                title: 'عرض',
                icon: '/icons/icon-96x96.png'
            },
            {
                action: 'close',
                title: 'إغلاق',
                icon: '/icons/icon-72x72.png'
            }
        ],
        requireInteraction: false,
        silent: false,
        tag: 'appnote-notification',
        renotify: true
    };

    // Parse push data if available
    if (event.data) {
        try {
            const pushData = event.data.json();
            console.log('Push data received:', pushData);

            // Update notification data with received data
            if (pushData.title) notificationData.title = pushData.title;
            if (pushData.body) notificationData.body = pushData.body;
            if (pushData.icon) notificationData.icon = pushData.icon;
            if (pushData.badge) notificationData.badge = pushData.badge;
            if (pushData.data) notificationData.data = { ...notificationData.data, ...pushData.data };
            if (pushData.actions) notificationData.actions = pushData.actions;
            if (pushData.vibrate) notificationData.vibrate = pushData.vibrate;
            if (pushData.tag) notificationData.tag = pushData.tag;
            if (pushData.requireInteraction !== undefined) notificationData.requireInteraction = pushData.requireInteraction;
            if (pushData.silent !== undefined) notificationData.silent = pushData.silent;
            if (pushData.renotify !== undefined) notificationData.renotify = pushData.renotify;
        } catch (error) {
            console.error('Error parsing push data:', error);
            // Use text data as body if JSON parsing fails
            notificationData.body = event.data.text();
        }
    }

    event.waitUntil(
        self.registration.showNotification(notificationData.title, {
            body: notificationData.body,
            icon: notificationData.icon,
            badge: notificationData.badge,
            vibrate: notificationData.vibrate,
            data: notificationData.data,
            actions: notificationData.actions,
            requireInteraction: notificationData.requireInteraction,
            silent: notificationData.silent,
            tag: notificationData.tag,
            renotify: notificationData.renotify
        })
    );
});

// Notification click handling
self.addEventListener('notificationclick', event => {
    console.log('Service Worker: Notification clicked', event);

    event.notification.close();

    // Get the URL to open from notification data
    let urlToOpen = '/';
    if (event.notification.data && event.notification.data.url) {
        urlToOpen = event.notification.data.url;
    } else if (event.notification.data && event.notification.data.action_url) {
        urlToOpen = event.notification.data.action_url;
    }

    // Handle different actions
    if (event.action === 'view') {
        // Open the specific URL
        event.waitUntil(
            clients.matchAll({ type: 'window', includeUncontrolled: true })
                .then(clientList => {
                    // Check if app is already open
                    for (let client of clientList) {
                        if (client.url.includes(self.location.origin) && 'focus' in client) {
                            client.focus();
                            client.navigate(urlToOpen);
                            return;
                        }
                    }
                    // Open new window if app is not open
                    return clients.openWindow(urlToOpen);
                })
        );
    } else if (event.action === 'close') {
        // Just close the notification
        console.log('Notification closed by user action');
        return;
    } else {
        // Default action - open the app
        event.waitUntil(
            clients.matchAll({ type: 'window', includeUncontrolled: true })
                .then(clientList => {
                    // Check if app is already open
                    for (let client of clientList) {
                        if (client.url.includes(self.location.origin) && 'focus' in client) {
                            client.focus();
                            if (urlToOpen !== '/') {
                                client.navigate(urlToOpen);
                            }
                            return;
                        }
                    }
                    // Open new window if app is not open
                    return clients.openWindow(urlToOpen);
                })
        );
    }
});

// Helper function for background sync
async function syncNotifications() {
    try {
        // This would sync with your API
        console.log('Service Worker: Syncing notifications...');
        // Implementation depends on your API structure
    } catch (error) {
        console.error('Service Worker: Sync failed', error);
    }
}

// Message handling from main thread
self.addEventListener('message', event => {
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
});
