# 📱 Notification API Documentation for Flutter

## 🔗 **Base URL**
```
http://localhost/appnote-api/public/api
```

## 🔐 **Authentication**
All notification endpoints require Bearer token authentication:
```dart
headers: {
  'Authorization': 'Bearer $token',
  'Content-Type': 'application/json',
  'Accept': 'application/json',
}
```

---

## 📋 **API Endpoints**

### 1. **Send Notification** 📤
**Endpoint:** `POST /notifications`

#### **Request Body (JSON):**
```json
{
  "title": "Notification Title",
  "message": "Notification content/message",
  "sender_id": 1,
  "sender_name": "Sender Name",
  "sender_type": "admin",
  "recipient_type": "students",
  "recipient_ids": [1, 2, 3],
  "priority": "high",
  "attachment_paths": ["path1.pdf", "path2.docx"],
  "attachment_names": ["File 1.pdf", "File 2.docx"]
}
```

#### **Flutter Implementation:**
```dart
Future<Map<String, dynamic>> sendNotification({
  required String title,
  required String message,
  required int senderId,
  required String senderName,
  required String recipientType,
  required List<int> recipientIds,
  String senderType = 'admin',
  String priority = 'medium',
  List<String>? attachmentPaths,
  List<String>? attachmentNames,
}) async {
  final response = await http.post(
    Uri.parse('$baseUrl/notifications'),
    headers: {
      'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
    body: jsonEncode({
      'title': title,
      'message': message,
      'sender_id': senderId,
      'sender_name': senderName,
      'sender_type': senderType,
      'recipient_type': recipientType,
      'recipient_ids': recipientIds,
      'priority': priority,
      if (attachmentPaths != null) 'attachment_paths': attachmentPaths,
      if (attachmentNames != null) 'attachment_names': attachmentNames,
    }),
  );

  if (response.statusCode == 201) {
    return jsonDecode(response.body);
  } else {
    throw Exception('Failed to send notification: ${response.body}');
  }
}
```

#### **Response (Success - 201):**
```json
{
  "success": true,
  "message": "Notification sent successfully",
  "data": {
    "id": 36,
    "title": "Notification Title",
    "message": "Notification content",
    "sender_type": "admin",
    "sender_id": 1,
    "sender_name": "Sender Name",
    "recipient_type": "students",
    "recipient_ids": [1, 2, 3],
    "attachment_path": ["path1.pdf", "path2.docx"],
    "attachment_name": ["File 1.pdf", "File 2.docx"],
    "priority": "high",
    "status": "sent",
    "created_at": "2025-06-16T19:24:59.000000Z",
    "updated_at": "2025-06-16T19:24:59.000000Z"
  }
}
```

---

### 2. **Send Notification with File Upload** 📎
**Endpoint:** `POST /notifications` (Multipart Form Data)

#### **Flutter Implementation:**
```dart
Future<Map<String, dynamic>> sendNotificationWithFiles({
  required String title,
  required String message,
  required int senderId,
  required String senderName,
  required String recipientType,
  required List<int> recipientIds,
  required List<File> attachments,
  String senderType = 'admin',
  String priority = 'medium',
}) async {
  var request = http.MultipartRequest(
    'POST',
    Uri.parse('$baseUrl/notifications'),
  );

  // Add headers
  request.headers.addAll({
    'Authorization': 'Bearer $token',
    'Accept': 'application/json',
  });

  // Add form fields
  request.fields.addAll({
    'title': title,
    'message': message,
    'sender_id': senderId.toString(),
    'sender_name': senderName,
    'sender_type': senderType,
    'recipient_type': recipientType,
    'priority': priority,
  });

  // Add recipient_ids as array
  for (int i = 0; i < recipientIds.length; i++) {
    request.fields['recipient_ids[$i]'] = recipientIds[i].toString();
  }

  // Add files
  for (File file in attachments) {
    request.files.add(await http.MultipartFile.fromPath(
      'attachments[]',
      file.path,
      filename: basename(file.path),
    ));
  }

  var response = await request.send();
  var responseBody = await response.stream.bytesToString();

  if (response.statusCode == 201) {
    return jsonDecode(responseBody);
  } else {
    throw Exception('Failed to upload files: $responseBody');
  }
}
```

---

### 3. **Get All Notifications** 📋
**Endpoint:** `GET /notifications`

#### **Flutter Implementation:**
```dart
Future<List<Map<String, dynamic>>> getAllNotifications() async {
  final response = await http.get(
    Uri.parse('$baseUrl/notifications'),
    headers: {
      'Authorization': 'Bearer $token',
      'Accept': 'application/json',
    },
  );

  if (response.statusCode == 200) {
    final data = jsonDecode(response.body);
    return List<Map<String, dynamic>>.from(data['data']);
  } else {
    throw Exception('Failed to fetch notifications: ${response.body}');
  }
}
```

#### **Response (Success - 200):**
```json
{
  "success": true,
  "message": "Notifications retrieved successfully",
  "data": [
    {
      "id": 36,
      "title": "Notification Title",
      "message": "Notification content",
      "sender_name": "Sender Name",
      "recipient_type": "students",
      "attachment_path": ["path1.pdf"],
      "attachment_name": ["File 1.pdf"],
      "priority": "high",
      "status": "sent",
      "created_at": "2025-06-16T19:24:59.000000Z"
    }
  ]
}
```

---

### 4. **Get Single Notification** 🔍
**Endpoint:** `GET /notifications/{id}`

#### **Flutter Implementation:**
```dart
Future<Map<String, dynamic>> getNotification(int id) async {
  final response = await http.get(
    Uri.parse('$baseUrl/notifications/$id'),
    headers: {
      'Authorization': 'Bearer $token',
      'Accept': 'application/json',
    },
  );

  if (response.statusCode == 200) {
    return jsonDecode(response.body);
  } else {
    throw Exception('Failed to fetch notification: ${response.body}');
  }
}
```

---

### 5. **Delete Notification** 🗑️
**Endpoint:** `DELETE /notifications/{id}`

#### **Flutter Implementation:**
```dart
Future<bool> deleteNotification(int id) async {
  final response = await http.delete(
    Uri.parse('$baseUrl/notifications/$id'),
    headers: {
      'Authorization': 'Bearer $token',
      'Accept': 'application/json',
    },
  );

  return response.statusCode == 200;
}
```

---

### 6. **Bulk Delete Notifications** 🗑️📦
**Endpoint:** `DELETE /notifications/bulk-delete`

#### **Request Body:**
```json
{
  "notification_ids": [1, 2, 3, 4]
}
```

#### **Flutter Implementation:**
```dart
Future<Map<String, dynamic>> bulkDeleteNotifications(List<int> ids) async {
  final response = await http.delete(
    Uri.parse('$baseUrl/notifications/bulk-delete'),
    headers: {
      'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
    body: jsonEncode({
      'notification_ids': ids,
    }),
  );

  if (response.statusCode == 200) {
    return jsonDecode(response.body);
  } else {
    throw Exception('Failed to bulk delete: ${response.body}');
  }
}
```

---

### 7. **Mark Notification as Read** ✅
**Endpoint:** `POST /notifications/{id}/mark-read`

#### **Flutter Implementation:**
```dart
Future<bool> markNotificationAsRead(int id) async {
  final response = await http.post(
    Uri.parse('$baseUrl/notifications/$id/mark-read'),
    headers: {
      'Authorization': 'Bearer $token',
      'Accept': 'application/json',
    },
  );

  return response.statusCode == 200;
}
```

---

## 📊 **Data Models**

### **Notification Model:**
```dart
class NotificationModel {
  final int id;
  final String title;
  final String message;
  final String senderName;
  final String recipientType;
  final List<String>? attachmentPaths;
  final List<String>? attachmentNames;
  final String priority;
  final String status;
  final DateTime createdAt;

  NotificationModel({
    required this.id,
    required this.title,
    required this.message,
    required this.senderName,
    required this.recipientType,
    this.attachmentPaths,
    this.attachmentNames,
    required this.priority,
    required this.status,
    required this.createdAt,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'],
      title: json['title'],
      message: json['message'],
      senderName: json['sender_name'],
      recipientType: json['recipient_type'],
      attachmentPaths: json['attachment_path'] != null 
          ? List<String>.from(json['attachment_path'])
          : null,
      attachmentNames: json['attachment_name'] != null
          ? List<String>.from(json['attachment_name'])
          : null,
      priority: json['priority'],
      status: json['status'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  bool get hasAttachments => 
      attachmentPaths != null && attachmentPaths!.isNotEmpty;
}
```

---

## 🎯 **Field Validation**

### **Required Fields:**
| Field | Type | Description |
|-------|------|-------------|
| `title` | String (max: 255) | Notification title |
| `message` | String | Notification content |
| `sender_id` | Integer | ID of sender |
| `sender_name` | String (max: 255) | Name of sender |
| `recipient_type` | String | Type: students/employees/all |
| `recipient_ids` | Array[Integer] | List of recipient IDs |

### **Optional Fields:**
| Field | Type | Default | Options |
|-------|------|---------|---------|
| `sender_type` | String | "admin" | admin/employee/student |
| `priority` | String | "medium" | low/medium/high |
| `attachment_paths` | Array[String] | null | File paths |
| `attachment_names` | Array[String] | null | Display names |
| `attachments[]` | Files | null | Actual files (multipart) |

### **File Upload Limits:**
- **Max file size:** 10MB per file
- **Supported formats:** PDF, DOC, DOCX, XLS, XLSX, JPG, PNG, TXT, ZIP
- **Max files:** No limit (but consider performance)

---

## ⚠️ **Error Handling**

### **Common Error Responses:**

#### **Validation Error (422):**
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "title": ["The title field is required."],
    "recipient_ids": ["The recipient ids field is required."]
  }
}
```

#### **Authentication Error (401):**
```json
{
  "success": false,
  "message": "Unauthenticated"
}
```

#### **Server Error (500):**
```json
{
  "success": false,
  "message": "Error sending notification: Internal server error",
  "data": null
}
```

### **Flutter Error Handling:**
```dart
try {
  final result = await sendNotification(/* parameters */);
  // Handle success
} on Exception catch (e) {
  if (e.toString().contains('422')) {
    // Handle validation errors
  } else if (e.toString().contains('401')) {
    // Handle authentication errors
  } else {
    // Handle other errors
  }
}
```

---

## 🧪 **Testing Examples**

### **Complete Flutter Service Class:**
```dart
class NotificationService {
  final String baseUrl;
  final String token;

  NotificationService({required this.baseUrl, required this.token});

  // Send simple notification
  Future<NotificationModel> sendSimpleNotification({
    required String title,
    required String message,
    required List<int> recipientIds,
    String recipientType = 'students',
    String priority = 'medium',
  }) async {
    final result = await sendNotification(
      title: title,
      message: message,
      senderId: 1,
      senderName: 'Flutter App',
      recipientType: recipientType,
      recipientIds: recipientIds,
      priority: priority,
    );
    
    return NotificationModel.fromJson(result['data']);
  }

  // Send notification with attachments
  Future<NotificationModel> sendNotificationWithAttachments({
    required String title,
    required String message,
    required List<int> recipientIds,
    required List<File> files,
    String recipientType = 'students',
    String priority = 'high',
  }) async {
    final result = await sendNotificationWithFiles(
      title: title,
      message: message,
      senderId: 1,
      senderName: 'Flutter App',
      recipientType: recipientType,
      recipientIds: recipientIds,
      attachments: files,
      priority: priority,
    );
    
    return NotificationModel.fromJson(result['data']);
  }

  // Get all notifications
  Future<List<NotificationModel>> getNotifications() async {
    final notifications = await getAllNotifications();
    return notifications.map((n) => NotificationModel.fromJson(n)).toList();
  }
}
```

---

## 🚀 **Quick Start Example**

```dart
// Initialize service
final notificationService = NotificationService(
  baseUrl: 'http://localhost/appnote-api/public/api',
  token: 'your_bearer_token_here',
);

// Send simple notification
await notificationService.sendSimpleNotification(
  title: 'Welcome Message',
  message: 'Welcome to our app!',
  recipientIds: [1, 2, 3],
  priority: 'high',
);

// Send notification with files
await notificationService.sendNotificationWithAttachments(
  title: 'Documents Shared',
  message: 'Please review the attached documents.',
  recipientIds: [1, 2, 3],
  files: [File('path/to/document.pdf')],
);
```

---

## ✅ **API Status**
- ✅ **Send Notifications** - Working
- ✅ **File Upload** - Working  
- ✅ **Multiple Attachments** - Working
- ✅ **Get Notifications** - Working
- ✅ **Delete Notifications** - Working
- ✅ **Mark as Read** - Working
- ✅ **Bulk Operations** - Working

**Ready for Flutter integration!** 🎉

---

## 📱 **Flutter Widget Examples**

### **Send Notification Widget:**
```dart
class SendNotificationWidget extends StatefulWidget {
  @override
  _SendNotificationWidgetState createState() => _SendNotificationWidgetState();
}

class _SendNotificationWidgetState extends State<SendNotificationWidget> {
  final _titleController = TextEditingController();
  final _messageController = TextEditingController();
  List<File> _selectedFiles = [];
  String _priority = 'medium';
  String _recipientType = 'students';
  List<int> _recipientIds = [1, 2, 3];

  Future<void> _pickFiles() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      allowMultiple: true,
      type: FileType.custom,
      allowedExtensions: ['pdf', 'doc', 'docx', 'jpg', 'png', 'txt'],
    );

    if (result != null) {
      setState(() {
        _selectedFiles = result.paths.map((path) => File(path!)).toList();
      });
    }
  }

  Future<void> _sendNotification() async {
    try {
      final notificationService = NotificationService(
        baseUrl: 'http://your-api-url/api',
        token: 'your-token',
      );

      if (_selectedFiles.isNotEmpty) {
        await notificationService.sendNotificationWithAttachments(
          title: _titleController.text,
          message: _messageController.text,
          recipientIds: _recipientIds,
          files: _selectedFiles,
          recipientType: _recipientType,
          priority: _priority,
        );
      } else {
        await notificationService.sendSimpleNotification(
          title: _titleController.text,
          message: _messageController.text,
          recipientIds: _recipientIds,
          recipientType: _recipientType,
          priority: _priority,
        );
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Notification sent successfully!')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Send Notification')),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          children: [
            TextField(
              controller: _titleController,
              decoration: InputDecoration(labelText: 'Title'),
            ),
            TextField(
              controller: _messageController,
              decoration: InputDecoration(labelText: 'Message'),
              maxLines: 3,
            ),
            DropdownButton<String>(
              value: _priority,
              onChanged: (value) => setState(() => _priority = value!),
              items: ['low', 'medium', 'high']
                  .map((p) => DropdownMenuItem(value: p, child: Text(p)))
                  .toList(),
            ),
            ElevatedButton(
              onPressed: _pickFiles,
              child: Text('Pick Files (${_selectedFiles.length} selected)'),
            ),
            ElevatedButton(
              onPressed: _sendNotification,
              child: Text('Send Notification'),
            ),
          ],
        ),
      ),
    );
  }
}
```

### **Notification List Widget:**
```dart
class NotificationListWidget extends StatefulWidget {
  @override
  _NotificationListWidgetState createState() => _NotificationListWidgetState();
}

class _NotificationListWidgetState extends State<NotificationListWidget> {
  List<NotificationModel> _notifications = [];
  bool _loading = true;

  @override
  void initState() {
    super.initState();
    _loadNotifications();
  }

  Future<void> _loadNotifications() async {
    try {
      final notificationService = NotificationService(
        baseUrl: 'http://your-api-url/api',
        token: 'your-token',
      );

      final notifications = await notificationService.getNotifications();
      setState(() {
        _notifications = notifications;
        _loading = false;
      });
    } catch (e) {
      setState(() => _loading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading notifications: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_loading) {
      return Center(child: CircularProgressIndicator());
    }

    return ListView.builder(
      itemCount: _notifications.length,
      itemBuilder: (context, index) {
        final notification = _notifications[index];
        return Card(
          child: ListTile(
            title: Text(notification.title),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(notification.message),
                if (notification.hasAttachments)
                  Wrap(
                    children: notification.attachmentNames!
                        .map((name) => Chip(label: Text(name)))
                        .toList(),
                  ),
              ],
            ),
            trailing: Chip(
              label: Text(notification.priority),
              backgroundColor: notification.priority == 'high'
                  ? Colors.red
                  : notification.priority == 'medium'
                      ? Colors.orange
                      : Colors.green,
            ),
          ),
        );
      },
    );
  }
}
```

---

## 🔧 **Environment Configuration**

### **config.dart:**
```dart
class ApiConfig {
  static const String baseUrl = 'http://localhost/appnote-api/public/api';
  static const String storageUrl = 'http://localhost/appnote-api/public/storage';

  // For production
  // static const String baseUrl = 'https://your-domain.com/api';
  // static const String storageUrl = 'https://your-domain.com/storage';
}
```

### **Download Attachment Helper:**
```dart
Future<void> downloadAttachment(String path, String filename) async {
  final url = '${ApiConfig.storageUrl}/$path';

  try {
    final response = await http.get(Uri.parse(url));

    if (response.statusCode == 200) {
      // Save file to device
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/$filename');
      await file.writeAsBytes(response.bodyBytes);

      // Show success message
      print('File downloaded: ${file.path}');
    } else {
      throw Exception('Failed to download file');
    }
  } catch (e) {
    print('Download error: $e');
  }
}
```

**Ready for Flutter integration!** 🎉
