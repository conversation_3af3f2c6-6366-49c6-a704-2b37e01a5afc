@php
use Illuminate\Support\Facades\Auth;
@endphp
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ config('app.name', 'معهد النبطية الفني') }}</title>

    <!-- PWA Meta Tags -->
    <meta name="application-name" content="معهد النبطية الفني - AppNote">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="AppNote">
    <meta name="description" content="نظام إدارة الإشعارات والطلاب لمعهد النبطية الفني">
    <meta name="format-detection" content="telephone=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="msapplication-config" content="/browserconfig.xml">
    <meta name="msapplication-TileColor" content="#5d6e35">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="theme-color" content="#5d6e35">

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">

    <!-- PWA Icons -->
    <link rel="apple-touch-icon" href="/icons/icon-152x152.png">
    <link rel="apple-touch-icon" sizes="152x152" href="/icons/icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/icons/icon-192x192.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/icons/icon-72x72.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/icons/icon-72x72.png">
    <link rel="shortcut icon" href="/favicon.ico">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js" integrity="sha512-fD9DI5bZwQxOi7MhYWnnNPlvXdp/2Pj3XSTRrFs5FQa4mizyGLnJcN6tuvUS6LbmgN1ut+XGSABKvjN0H6Aoow==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        :root {
            --parchment: #f4ecdc;
            --costa-del-sol: #5d6e35;
            --locust: #abae88;
            --gurkha: #949c74;
            --avocado: #949b6c;
            --coral-reef: #c4c2a4;
            --thistle-green: #d0cdb0;
            --tana: #ded8bf;
            --chino: #ccc4a9;
            --font-family-base: 'Cairo', sans-serif;
        }
        
        /* Apply Cairo font to all elements */
        *, ::after, ::before {
            font-family: 'Cairo', sans-serif;
        }
        
        /* Ensure form elements use Cairo font */
        input, select, textarea, button, .btn, .form-control, .dropdown-item {
            font-family: 'Cairo', sans-serif !important;
        }
        
        /* Set appropriate font weights */
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Cairo', sans-serif;
            font-weight: 700;
        }
        
        /* Adjust for Arabic text direction if needed */
        .rtl-text {
            direction: rtl;
            text-align: right;
        }
        
        html, body {
            height: 100%;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background-color: var(--parchment);
            color: #333;
            display: flex;
            flex-direction: column;
            line-height: 1.6;
        }
        
        .content-wrapper {
            flex: 1 0 auto;
            padding-bottom: 2rem;
        }
        
        .navbar {
            background-color: var(--costa-del-sol);
            padding: 0.9rem 1.5rem;
            box-shadow: 0 2px 15px rgba(93, 110, 53, 0.2);
        }
        
        .navbar-brand {
            color: white;
            font-weight: 600;
            letter-spacing: 0.5px;
            font-size: 1.3rem;
        }
        
        .app-logo {
            border-radius: 50%;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            object-fit: cover;
            width: 40px;
            height: 40px;
        }
        
        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.85);
            font-weight: 500;
            padding: 0.6rem 1rem;
            transition: all 0.3s ease;
        }
        
        .navbar-nav .nav-link:hover,
        .navbar-nav .nav-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
        }

        .user-info {
            background-color: rgba(255, 255, 255, 0.15);
            border-radius: 20px;
            padding: 0.4rem 0.8rem !important;
            margin: 0 0.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .user-info:hover {
            background-color: rgba(255, 255, 255, 0.2) !important;
        }

        .username-text {
            font-weight: 600;
            font-size: 0.9rem;
            letter-spacing: 0.3px;
        }
        
        .main-content {
            margin-top: 2rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 6px 15px rgba(93, 110, 53, 0.1);
            transition: all 0.3s;
            margin-bottom: 1.5rem;
            border-left: 5px solid var(--avocado);
            background-color: white;
            overflow: hidden;
        }
        
        .card:hover {
            box-shadow: 0 10px 20px rgba(93, 110, 53, 0.15);
            transform: translateY(-5px);
        }
        
        .card-header {
            background-color: var(--thistle-green);
            color: var(--costa-del-sol);
            font-weight: 600;
            border-bottom: 1px solid var(--coral-reef);
            padding: 0.9rem 1.2rem;
            font-size: 1.1rem;
        }
        
        .card-body {
            padding: 1.3rem;
        }
        
        .stats-card {
            background-color: white;
            border-radius: 12px;
            padding: 1.8rem;
            box-shadow: 0 6px 15px rgba(93, 110, 53, 0.1);
            margin-bottom: 1.5rem;
            height: 100%;
            border-bottom: 4px solid var(--avocado);
            transition: all 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 20px rgba(93, 110, 53, 0.15);
        }
        
        .stats-card .icon {
            background: linear-gradient(45deg, var(--costa-del-sol), var(--locust));
            color: white;
            width: 65px;
            height: 65px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 12px;
            font-size: 1.7rem;
            margin-right: 1.2rem;
            box-shadow: 0 5px 10px rgba(93, 110, 53, 0.2);
        }
        
        .stats-card .count {
            font-size: 2.2rem;
            font-weight: 700;
            color: var(--costa-del-sol);
            margin-bottom: 0.3rem;
            line-height: 1;
        }
        
        .stats-card .label {
            color: var(--gurkha);
            font-size: 1rem;
            font-weight: 500;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, var(--costa-del-sol), var(--avocado));
            border: none;
            padding: 0.6rem 1.5rem;
            border-radius: 6px;
            font-weight: 500;
            box-shadow: 0 4px 10px rgba(93, 110, 53, 0.2);
            transition: all 0.3s;
        }
        
        .btn-primary:hover {
            background: linear-gradient(45deg, var(--avocado), var(--costa-del-sol));
            box-shadow: 0 6px 12px rgba(93, 110, 53, 0.3);
            transform: translateY(-2px);
        }
        
        .sidebar {
            background-color: white;
            border-radius: 12px;
            padding: 1.8rem;
            box-shadow: 0 6px 15px rgba(93, 110, 53, 0.1);
            margin-bottom: 2rem;
        }
        
        .sidebar-title {
            color: var(--costa-del-sol);
            font-weight: 600;
            border-bottom: 2px solid var(--thistle-green);
            padding-bottom: 0.7rem;
            margin-bottom: 1.2rem;
            font-size: 1.2rem;
        }
        
        .footer {
            background-color: var(--costa-del-sol);
            color: white;
            padding: 1.5rem 0;
            margin-top: 0;
            flex-shrink: 0;
            box-shadow: 0 -5px 20px rgba(93, 110, 53, 0.2);
        }

        .list-group-item {
            border: none;
            padding: 0.8rem 0.5rem;
            transition: all 0.3s ease;
            border-radius: 6px;
        }

        .list-group-item:hover {
            background-color: var(--parchment);
            transform: translateX(5px);
            color: var(--costa-del-sol);
        }
        
        .table {
            border-collapse: separate;
            border-spacing: 0;
        }
        
        .table thead th {
            background-color: rgba(208, 205, 176, 0.3);
            color: var(--costa-del-sol);
            font-weight: 600;
            border-bottom: 2px solid var(--coral-reef);
        }
        
        .table tbody tr:hover {
            background-color: rgba(244, 236, 220, 0.5);
        }
        
        .badge {
            padding: 0.5em 0.8em;
            border-radius: 6px;
            font-weight: 500;
            font-size: 0.8rem;
        }
        
        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 10px;
        }
        
        ::-webkit-scrollbar-track {
            background: var(--parchment);
        }
        
        ::-webkit-scrollbar-thumb {
            background: var(--coral-reef);
            border-radius: 10px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: var(--gurkha);
        }
    </style>
    @yield('styles')
</head>
<body>
    <div class="content-wrapper">
        <nav class="navbar navbar-expand-lg">
            <div class="container">
                <a class="navbar-brand d-flex align-items-center" href="{{ route('dashboard') }}">
                    <img src="{{ asset('logo.jpeg') }}" alt="معهد النبطية الفني" class="me-2 app-logo" height="40">
                    <span class="rtl-text">معهد النبطية الفني</span>
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav ms-auto">
                        @if(Auth::guard('student')->check())
                            <!-- Student Navigation -->
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('student.dashboard') ? 'active' : '' }}" href="{{ route('student.dashboard') }}">Dashboard</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('student.notifications') ? 'active' : '' }}" href="{{ route('student.notifications') }}">
                                    <span class="position-relative">
                                        <i class="fas fa-bell me-1"></i> Notifications
                                        @php
                                        // Get notification count directly in the view for immediate display
                                        $unreadCount = 0;
                                        if (Auth::guard('student')->check()) {
                                            $student = Auth::guard('student')->user();
                                            $unreadCount = \App\Models\NotificationRecipient::where('recipient_id', $student->id)
                                                ->where('recipient_type', 'student')
                                                ->whereNull('read_at')
                                                ->count();
                                        }
                                        @endphp
                                        @if($unreadCount > 0)
                                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style="font-size: 0.75rem; margin-top: -5px; margin-left: -5px; padding: 5px 7px;">
                                                {{ $unreadCount }}
                                            </span>
                                        @endif
                                    </span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('student.profile.*') ? 'active' : '' }}" href="{{ route('student.profile.edit') }}"><i class="fas fa-user me-1"></i> Profile</a>
                            </li>
                        @elseif(Auth::guard('employee')->check())
                            <!-- Employee Navigation -->
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('employee.dashboard') ? 'active' : '' }}" href="{{ route('employee.dashboard') }}">Dashboard</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('employee.notifications') ? 'active' : '' }}" href="{{ route('employee.notifications') }}">
                                    <span class="position-relative">
                                        <i class="fas fa-bell me-1"></i> Notifications
                                        @php
                                        // Get notification count directly in the view for employee
                                        $unreadCount = 0;
                                        if (Auth::guard('employee')->check()) {
                                            $employee = Auth::guard('employee')->user();
                                            $unreadCount = \App\Models\NotificationRecipient::where('recipient_id', $employee->id)
                                                ->where('recipient_type', 'employee')
                                                ->whereNull('read_at')
                                                ->count();
                                        }
                                        @endphp
                                        @if($unreadCount > 0)
                                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style="font-size: 0.75rem; margin-top: -5px; margin-left: -5px; padding: 5px 7px;">
                                                {{ $unreadCount }}
                                            </span>
                                        @endif
                                    </span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('employee.profile.*') ? 'active' : '' }}" href="{{ route('employee.profile.edit') }}"><i class="fas fa-user me-1"></i> Profile</a>
                            </li>

                        @else
                            <!-- Admin Navigation -->
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}" href="{{ route('dashboard') }}">Dashboard</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('students.*') ? 'active' : '' }}" href="{{ route('students.index') }}">Students</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('employees.*') ? 'active' : '' }}" href="{{ url('/employees') }}">Employees</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link {{ request()->routeIs('notifications.*') ? 'active' : '' }}" href="{{ route('notifications.index') }}"><i class="fas fa-bell me-1"></i> Notifications</a>
                            </li>
                            @if(Auth::guard('student')->check() || Auth::guard('employee')->check() || Auth::check())
                            <li class="nav-item">
                                <span class="nav-link user-info d-none d-md-inline-flex align-items-center">
                                    <i class="fas fa-user-circle me-2"></i>
                                    <span class="username-text">
                                        @if(Auth::guard('student')->check())
                                            {{ Auth::guard('student')->user()->full_name ?? Auth::guard('student')->user()->name ?? 'طالب' }}
                                        @elseif(Auth::guard('employee')->check())
                                            {{ Auth::guard('employee')->user()->full_name ?? Auth::guard('employee')->user()->name ?? 'موظف' }}
                                        @elseif(Auth::check() && Auth::user())
                                            {{ str_replace('Admin', '', Auth::user()->name) }}
                                        @else
                                            مستخدم
                                        @endif
                                    </span>
                                </span>
                            </li>
                            @endif
                        @endif
                        @if(Auth::guard('student')->check() || Auth::guard('employee')->check() || Auth::check())
                        <li class="nav-item">
                            <form method="POST" action="{{ route('logout') }}" class="d-inline">
                                @csrf
                                <button type="submit" class="nav-link" style="background: none; border: none;">
                                    Logout
                                </button>
                            </form>
                        </li>
                        @endif
                    </ul>
                </div>
            </div>
        </nav>

        @yield('content')
    </div>

    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">&copy; {{ date('Y') }} AppNote API. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">Version 1.0.0</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- PWA Installation Script -->
    <script>
        // PWA Installation
        let deferredPrompt;
        let installButton = null;

        // Check if PWA is already installed
        function isPWAInstalled() {
            return window.matchMedia('(display-mode: standalone)').matches ||
                   window.navigator.standalone === true;
        }

        // Create install button
        function createInstallButton() {
            if (installButton || isPWAInstalled()) return;

            installButton = document.createElement('button');
            installButton.innerHTML = '<i class="fas fa-download me-2"></i>تثبيت التطبيق';
            installButton.className = 'btn btn-success position-fixed';
            installButton.style.cssText = `
                bottom: 20px;
                right: 20px;
                z-index: 1000;
                border-radius: 25px;
                padding: 10px 20px;
                font-weight: 600;
                box-shadow: 0 4px 15px rgba(93, 110, 53, 0.3);
                animation: pulse 2s infinite;
            `;

            installButton.addEventListener('click', installPWA);
            document.body.appendChild(installButton);
        }

        // Install PWA
        async function installPWA() {
            if (!deferredPrompt) return;

            deferredPrompt.prompt();
            const { outcome } = await deferredPrompt.userChoice;

            if (outcome === 'accepted') {
                console.log('PWA installed successfully');
                hideInstallButton();
            }

            deferredPrompt = null;
        }

        // Hide install button
        function hideInstallButton() {
            if (installButton) {
                installButton.remove();
                installButton = null;
            }
        }

        // Service Worker Registration with enhanced error handling
        if ('serviceWorker' in navigator) {
            console.log('✅ Service Worker is supported');

            window.addEventListener('load', async () => {
                try {
                    console.log('🔄 Registering Service Worker...');
                    const registration = await navigator.serviceWorker.register('/sw.js', {
                        scope: '/'
                    });

                    console.log('✅ Service Worker registered successfully:', registration);
                    console.log('📍 Service Worker scope:', registration.scope);
                    console.log('📍 Service Worker state:', registration.installing ? 'installing' : registration.waiting ? 'waiting' : registration.active ? 'active' : 'unknown');

                    // Check for updates
                    registration.addEventListener('updatefound', () => {
                        console.log('🔄 Service Worker update found');
                        const newWorker = registration.installing;
                        if (newWorker) {
                            newWorker.addEventListener('statechange', () => {
                                console.log('🔄 Service Worker state changed to:', newWorker.state);
                                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                    // Show update notification
                                    console.log('🆕 New Service Worker update available');
                                    showUpdateNotification();
                                }
                            });
                        }
                    });

                    // Listen for messages from service worker
                    navigator.serviceWorker.addEventListener('message', event => {
                        console.log('📨 Message from Service Worker:', event.data);
                    });

                    // Check if service worker is ready
                    const swReady = await navigator.serviceWorker.ready;
                    console.log('✅ Service Worker is ready:', swReady);

                } catch (error) {
                    console.error('❌ Service Worker registration failed:', error);
                    console.error('❌ Error details:', error.message);
                }
            });
        } else {
            console.log('❌ Service Worker not supported in this browser');
        }

        // PWA Install Prompt
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;

            // Show install button after a delay
            setTimeout(createInstallButton, 3000);
        });

        // Handle app installed
        window.addEventListener('appinstalled', () => {
            console.log('PWA was installed');
            hideInstallButton();
            deferredPrompt = null;
        });

        // Show update notification
        function showUpdateNotification() {
            const notification = document.createElement('div');
            notification.innerHTML = `
                <div class="alert alert-info alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 1050; max-width: 300px;">
                    <strong>تحديث متوفر!</strong><br>
                    يتوفر إصدار جديد من التطبيق.
                    <button type="button" class="btn btn-sm btn-primary ms-2" onclick="updateApp()">تحديث</button>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            document.body.appendChild(notification);
        }

        // Update app
        function updateApp() {
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.getRegistration().then((registration) => {
                    if (registration && registration.waiting) {
                        registration.waiting.postMessage({ type: 'SKIP_WAITING' });
                        window.location.reload();
                    }
                });
            }
        }

        // Add CSS for pulse animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1); }
            }
        `;
        document.head.appendChild(style);

        // Initialize Push Notifications
        initializePushNotifications();

        // Push Notification Functions
        async function initializePushNotifications() {
            if (!('serviceWorker' in navigator) || !('PushManager' in window)) {
                console.log('Push notifications not supported');
                return;
            }

            try {
                // Wait for service worker to be ready
                const registration = await navigator.serviceWorker.ready;

                // Check if user is already subscribed
                const existingSubscription = await registration.pushManager.getSubscription();

                if (existingSubscription) {
                    console.log('User is already subscribed to push notifications');
                    // Optionally update subscription on server
                    await updateSubscriptionOnServer(existingSubscription);
                } else {
                    // Show push notification permission prompt after a delay
                    setTimeout(() => {
                        showPushNotificationPrompt();
                    }, 5000); // Show after 5 seconds
                }
            } catch (error) {
                console.error('Error initializing push notifications:', error);
            }
        }

        async function showPushNotificationPrompt() {
            // Check if permission is already granted
            if (Notification.permission === 'granted') {
                await subscribeToPushNotifications();
                return;
            }

            // Check if permission is denied
            if (Notification.permission === 'denied') {
                console.log('Push notifications are blocked');
                return;
            }

            // Show custom prompt
            const promptDiv = document.createElement('div');
            promptDiv.innerHTML = `
                <div class="alert alert-info alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 1050; max-width: 350px;">
                    <h6><i class="fas fa-bell me-2"></i>تفعيل الإشعارات</h6>
                    <p class="mb-2">هل تريد تلقي إشعارات فورية عند وصول رسائل جديدة؟</p>
                    <button type="button" class="btn btn-primary btn-sm me-2" onclick="enablePushNotifications()">تفعيل</button>
                    <button type="button" class="btn btn-secondary btn-sm" onclick="dismissPushPrompt()">لاحقاً</button>
                    <button type="button" class="btn-close" onclick="dismissPushPrompt()"></button>
                </div>
            `;
            document.body.appendChild(promptDiv);
            window.pushPromptDiv = promptDiv;
        }

        async function enablePushNotifications() {
            try {
                const permission = await Notification.requestPermission();

                if (permission === 'granted') {
                    await subscribeToPushNotifications();
                    showNotificationSuccess('تم تفعيل الإشعارات بنجاح!');
                } else {
                    showNotificationError('تم رفض تفعيل الإشعارات');
                }
            } catch (error) {
                console.error('Error enabling push notifications:', error);
                showNotificationError('خطأ في تفعيل الإشعارات');
            }

            dismissPushPrompt();
        }

        function dismissPushPrompt() {
            if (window.pushPromptDiv) {
                window.pushPromptDiv.remove();
                window.pushPromptDiv = null;
            }
        }

        async function subscribeToPushNotifications() {
            try {
                // Get VAPID public key from server
                const vapidResponse = await fetch('/api/push/vapid-key');
                const vapidData = await vapidResponse.json();

                if (!vapidData.success) {
                    throw new Error('Failed to get VAPID key');
                }

                const registration = await navigator.serviceWorker.ready;

                // Subscribe to push notifications
                const subscription = await registration.pushManager.subscribe({
                    userVisibleOnly: true,
                    applicationServerKey: urlBase64ToUint8Array(vapidData.public_key)
                });

                // Send subscription to server
                await sendSubscriptionToServer(subscription);

                console.log('Successfully subscribed to push notifications');

            } catch (error) {
                console.error('Error subscribing to push notifications:', error);
            }
        }

        async function sendSubscriptionToServer(subscription) {
            try {
                // Get current user info
                const userType = getCurrentUserType();
                const userId = getCurrentUserId();

                if (!userType || !userId) {
                    console.log('User not logged in, skipping push subscription');
                    return;
                }

                const response = await fetch('/api/push/subscribe', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        subscription: subscription.toJSON(),
                        user_type: userType,
                        user_id: userId
                    })
                });

                const data = await response.json();

                if (data.success) {
                    console.log('Subscription sent to server successfully');
                } else {
                    console.error('Failed to send subscription to server:', data.message);
                }
            } catch (error) {
                console.error('Error sending subscription to server:', error);
            }
        }

        async function updateSubscriptionOnServer(subscription) {
            // Same as sendSubscriptionToServer - the server will update existing subscription
            await sendSubscriptionToServer(subscription);
        }

        function getCurrentUserType() {
            // Determine user type based on current route or user data
            const path = window.location.pathname;
            if (path.includes('/student/')) return 'student';
            if (path.includes('/employee/')) return 'employee';
            if (path.includes('/admin/') || path.includes('/dashboard')) return 'admin';
            return null;
        }

        function getCurrentUserId() {
            // This would need to be set based on your authentication system
            // For now, return a placeholder - you'll need to implement this based on your auth
            @if(Auth::guard('student')->check())
                return {{ Auth::guard('student')->id() }};
            @elseif(Auth::guard('employee')->check())
                return {{ Auth::guard('employee')->id() }};
            @elseif(Auth::check())
                return {{ Auth::id() }};
            @else
                return null;
            @endif
        }

        // Utility function to convert VAPID key
        function urlBase64ToUint8Array(base64String) {
            const padding = '='.repeat((4 - base64String.length % 4) % 4);
            const base64 = (base64String + padding)
                .replace(/-/g, '+')
                .replace(/_/g, '/');

            const rawData = window.atob(base64);
            const outputArray = new Uint8Array(rawData.length);

            for (let i = 0; i < rawData.length; ++i) {
                outputArray[i] = rawData.charCodeAt(i);
            }
            return outputArray;
        }

        function showNotificationSuccess(message) {
            const notification = document.createElement('div');
            notification.innerHTML = `
                <div class="alert alert-success alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 1050; max-width: 300px;">
                    <i class="fas fa-check-circle me-2"></i>${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            document.body.appendChild(notification);
            setTimeout(() => notification.remove(), 5000);
        }

        function showNotificationError(message) {
            const notification = document.createElement('div');
            notification.innerHTML = `
                <div class="alert alert-danger alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 1050; max-width: 300px;">
                    <i class="fas fa-exclamation-circle me-2"></i>${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            document.body.appendChild(notification);
            setTimeout(() => notification.remove(), 5000);
        }

        // Test push notification function (for development)
        async function testPushNotification() {
            try {
                const userType = getCurrentUserType();
                const userId = getCurrentUserId();

                if (!userType || !userId) {
                    showNotificationError('يجب تسجيل الدخول أولاً');
                    return;
                }

                const response = await fetch('/api/push/test', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        user_type: userType,
                        user_id: userId,
                        title: 'اختبار الإشعارات',
                        body: 'هذا إشعار تجريبي للتأكد من عمل النظام'
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showNotificationSuccess('تم إرسال الإشعار التجريبي');
                } else {
                    showNotificationError('فشل في إرسال الإشعار التجريبي');
                }
            } catch (error) {
                console.error('Error testing push notification:', error);
                showNotificationError('خطأ في إرسال الإشعار التجريبي');
            }
        }

        // Make test function available globally for debugging
        window.testPushNotification = testPushNotification;
    </script>

    @yield('scripts')
</body>
</html>
