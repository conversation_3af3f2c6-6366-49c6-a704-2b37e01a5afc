<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Employee;
use Illuminate\Support\Facades\Hash;

class EmployeeSeeder extends Seeder
{
    public function run()
    {
        // Create sample employees if none exist
        if (Employee::count() == 0) {
            Employee::create([
                'username' => 'ahmed.romani',
                'password' => Hash::make('123456'),
                'full_name' => 'أحمد محمد الرومانى',
                'phone' => '***********',
                'employee_type' => 'موظف إداري',
                'contract_type' => 'عقد دائم',
                'job_status' => 'نشط',
                'automatic_number' => 'EMP-001',
                'financial_number' => 'FIN-001',
                'state_cooperative_number' => 'COOP-001',
                'bank_account_number' => 'BANK-001',
            ]);

            Employee::create([
                'username' => 'sara.ahmed',
                'password' => Hash::make('123456'),
                'full_name' => 'سارة أحمد محمد',
                'phone' => '***********',
                'employee_type' => 'موظف فني',
                'contract_type' => 'عقد مؤقت',
                'job_status' => 'نشط',
                'automatic_number' => 'EMP-002',
                'financial_number' => 'FIN-002',
                'state_cooperative_number' => 'COOP-002',
                'bank_account_number' => 'BANK-002',
            ]);

            Employee::create([
                'username' => 'mohamed.ali',
                'password' => Hash::make('123456'),
                'full_name' => 'محمد علي حسن',
                'phone' => '***********',
                'employee_type' => 'مشرف',
                'contract_type' => 'عقد دائم',
                'job_status' => 'نشط',
                'automatic_number' => 'EMP-003',
                'financial_number' => 'FIN-003',
                'state_cooperative_number' => 'COOP-003',
                'bank_account_number' => 'BANK-003',
            ]);
        }
    }
}
