<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First add the new columns without constraints
        Schema::table('students', function (Blueprint $table) {
            $table->string('username')->nullable(); // Initially allow NULL
            $table->string('phone')->nullable();
            $table->string('specialization')->nullable();
            $table->string('section')->nullable();
            $table->string('class')->nullable();
            $table->string('level')->nullable();
            $table->string('result')->nullable();
        });
        
        // Update existing records with temporary usernames
        $counter = 1;
        $students = DB::table('students')->get();
        
        foreach ($students as $student) {
            $username = 'stu' . str_pad($counter, 4, '0', STR_PAD_LEFT);
            DB::table('students')
                ->where('id', $student->id)
                ->update([
                    'username' => $username,
                    'phone' => $student->mobile_number ?? null,
                    'class' => $student->class_name ?? null,
                ]);
            $counter++;
        }
        
        // Now add the unique constraint and drop old columns
        Schema::table('students', function (Blueprint $table) {
            // Make username required and unique after populating data
            $table->string('username')->nullable(false)->unique()->change();
            
            // Set default password
            $table->string('password')->default(Hash::make('stu123'))->change();
            
            // Drop old columns
            $table->dropColumn('mobile_number');
            $table->dropColumn('class_name');
            $table->dropColumn('parent_mobile');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('students', function (Blueprint $table) {
            // Drop the new columns
            $table->dropColumn([
                'username',
                'phone',
                'specialization',
                'section',
                'class',
                'level',
                'result'
            ]);
        });

        Schema::table('students', function (Blueprint $table) {
            // Restore original columns
            $table->string('mobile_number')->after('full_name');
            $table->string('class_name')->after('nationality');
            $table->string('parent_mobile')->nullable()->after('class_name');
            
            // Remove default password constraint
            $table->string('password')->change();
        });
    }
};
