@extends('layouts.app')

@section('content')
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 style="color: var(--costa-del-sol);">Create New Notification</h2>
        <div>
            <a href="{{ route('notifications.index') }}" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left"></i> Back to Notifications
            </a>
        </div>
    </div>

    @if(session('error'))
        <div class="alert alert-danger">
            {{ session('error') }}
        </div>
    @endif

    <div class="card">
        <div class="card-header" style="background-color: var(--thistle-green);">
            <h5 style="color: var(--costa-del-sol);">Notification Details</h5>
        </div>
        <div class="card-body">
            <form action="{{ route('notifications.store') }}" method="POST" enctype="multipart/form-data">
                @csrf
                
                <div class="mb-3">
                    <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                    <input type="text" class="form-control @error('title') is-invalid @enderror" id="title" name="title" value="{{ old('title') }}" required>
                    @error('title')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="mb-3">
                    <label for="message" class="form-label">Message <span class="text-danger">*</span></label>
                    <textarea class="form-control @error('message') is-invalid @enderror" id="message" name="message" rows="5" required>{{ old('message') }}</textarea>
                    @error('message')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="mb-3">
                    <label for="priority" class="form-label">Priority <span class="text-danger">*</span></label>
                    <select class="form-select @error('priority') is-invalid @enderror" id="priority" name="priority" required>
                        <option value="low" {{ old('priority') == 'low' ? 'selected' : '' }}>Low</option>
                        <option value="medium" {{ old('priority') == 'medium' || !old('priority') ? 'selected' : '' }}>Medium</option>
                        <option value="high" {{ old('priority') == 'high' ? 'selected' : '' }}>High</option>
                    </select>
                    @error('priority')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="mb-3">
                    <label for="attachments" class="form-label">Attachments (Optional)</label>
                    <input type="file" class="form-control @error('attachments') is-invalid @enderror @error('attachments.*') is-invalid @enderror" id="attachments" name="attachments[]" multiple>
                    @error('attachments')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                    @error('attachments.*')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                    <small class="text-muted">Max file size: 10MB per file. You can select multiple files.</small>
                </div>
                
                <hr class="my-4">
                <h5 style="color: var(--costa-del-sol);">Recipients</h5>
                
                <div class="mb-4">
                    <label for="recipient_group_type" class="form-label">Select Recipient Group <span class="text-danger">*</span></label>
                    <select class="form-select @error('recipient_group_type') is-invalid @enderror" id="recipient_group_type" name="recipient_group_type" required>
                        <option value="">Select a group...</option>
                        <optgroup label="All Users">
                            <option value="all" {{ old('recipient_group_type') == 'all' ? 'selected' : '' }}>All Users (Students & Employees)</option>
                        </optgroup>
                        <optgroup label="Student Groups">
                            <option value="all_students" {{ old('recipient_group_type') == 'all_students' ? 'selected' : '' }}>All Students</option>
                            <option value="student_level" {{ old('recipient_group_type') == 'student_level' ? 'selected' : '' }}>By Level</option>
                            <option value="student_class" {{ old('recipient_group_type') == 'student_class' ? 'selected' : '' }}>By Class</option>
                        </optgroup>
                        <optgroup label="Employee Groups">
                            <option value="all_employees" {{ old('recipient_group_type') == 'all_employees' ? 'selected' : '' }}>All Employees</option>
                            <option value="employee_contract" {{ old('recipient_group_type') == 'employee_contract' ? 'selected' : '' }}>By Contract Type</option>
                            <option value="employee_status" {{ old('recipient_group_type') == 'employee_status' ? 'selected' : '' }}>By Job Status</option>
                        </optgroup>
                    </select>
                    @error('recipient_group_type')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                
                <!-- Student Level Filter -->
                <div class="filter-section mb-3" id="student_level_filter" style="display: none;">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">Select Student Levels</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                @foreach($levels as $level)
                                    <div class="col-md-4 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="selected_levels[]" value="{{ $level }}" id="level_{{ $loop->index }}"
                                                {{ (is_array(old('selected_levels')) && in_array($level, old('selected_levels'))) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="level_{{ $loop->index }}">
                                                {{ $level }}
                                            </label>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                            <div class="mt-2">
                                <button type="button" class="btn btn-sm btn-outline-primary select-all-btn" data-target="selected_levels[]">Select All</button>
                                <button type="button" class="btn btn-sm btn-outline-secondary deselect-all-btn" data-target="selected_levels[]">Deselect All</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Student Class Filter -->
                <div class="filter-section mb-3" id="student_class_filter" style="display: none;">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">Select Student Classes</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                @foreach($classes as $class)
                                    <div class="col-md-4 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="selected_classes[]" value="{{ $class }}" id="class_{{ $loop->index }}"
                                                {{ (is_array(old('selected_classes')) && in_array($class, old('selected_classes'))) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="class_{{ $loop->index }}">
                                                {{ $class }}
                                            </label>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                            <div class="mt-2">
                                <button type="button" class="btn btn-sm btn-outline-primary select-all-btn" data-target="selected_classes[]">Select All</button>
                                <button type="button" class="btn btn-sm btn-outline-secondary deselect-all-btn" data-target="selected_classes[]">Deselect All</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Employee Contract Type Filter -->
                <div class="filter-section mb-3" id="employee_contract_filter" style="display: none;">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">Select Contract Types</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                @foreach($contractTypes as $type)
                                    <div class="col-md-4 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="selected_contract_types[]" value="{{ $type }}" id="contract_{{ $loop->index }}"
                                                {{ (is_array(old('selected_contract_types')) && in_array($type, old('selected_contract_types'))) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="contract_{{ $loop->index }}">
                                                {{ $type }}
                                            </label>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                            <div class="mt-2">
                                <button type="button" class="btn btn-sm btn-outline-primary select-all-btn" data-target="selected_contract_types[]">Select All</button>
                                <button type="button" class="btn btn-sm btn-outline-secondary deselect-all-btn" data-target="selected_contract_types[]">Deselect All</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Employee Job Status Filter -->
                <div class="filter-section mb-3" id="employee_status_filter" style="display: none;">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">Select Job Statuses</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                @foreach($jobStatuses as $status)
                                    <div class="col-md-4 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="selected_job_statuses[]" value="{{ $status }}" id="job_status_{{ $loop->index }}"
                                                {{ (is_array(old('selected_job_statuses')) && in_array($status, old('selected_job_statuses'))) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="job_status_{{ $loop->index }}">
                                                {{ $status }}
                                            </label>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                            <div class="mt-2">
                                <button type="button" class="btn btn-sm btn-outline-primary select-all-btn" data-target="selected_job_statuses[]">Select All</button>
                                <button type="button" class="btn btn-sm btn-outline-secondary deselect-all-btn" data-target="selected_job_statuses[]">Deselect All</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mb-3 mt-4">
                    <p class="text-muted"><span class="text-danger">*</span> Required fields</p>
                </div>
                
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="button" class="btn btn-secondary me-md-2" onclick="window.history.back();">Cancel</button>
                    <button type="submit" class="btn btn-primary">Send Notification</button>
                </div>
            </form>
        </div>
    </div>
</div>

@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const recipientGroupType = document.getElementById('recipient_group_type');
        const filterSections = document.querySelectorAll('.filter-section');
        
        // Initial visibility based on selected option
        toggleFilterVisibility();
        
        // Handle change events
        recipientGroupType.addEventListener('change', toggleFilterVisibility);
        
        function toggleFilterVisibility() {
            const selectedValue = recipientGroupType.value;
            
            // Hide all filter sections first
            filterSections.forEach(section => {
                section.style.display = 'none';
            });
            
            // Show the appropriate filter section based on selection
            if (selectedValue === 'student_level') {
                document.getElementById('student_level_filter').style.display = 'block';
            } else if (selectedValue === 'student_class') {
                document.getElementById('student_class_filter').style.display = 'block';
            } else if (selectedValue === 'employee_contract') {
                document.getElementById('employee_contract_filter').style.display = 'block';
            } else if (selectedValue === 'employee_status') {
                document.getElementById('employee_status_filter').style.display = 'block';
            }
        }
        
        // Select/Deselect All functionality
        document.querySelectorAll('.select-all-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const checkboxes = document.querySelectorAll(`input[name="${this.dataset.target}"]`);
                checkboxes.forEach(checkbox => checkbox.checked = true);
            });
        });
        
        document.querySelectorAll('.deselect-all-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const checkboxes = document.querySelectorAll(`input[name="${this.dataset.target}"]`);
                checkboxes.forEach(checkbox => checkbox.checked = false);
            });
        });
    });
</script>
@endsection
