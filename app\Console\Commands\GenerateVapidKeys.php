<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Minishlink\WebPush\VAPID;

class GenerateVapidKeys extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'webpush:vapid {--show : Show the generated keys instead of updating .env}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate VAPID keys for web push notifications';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Generating VAPID keys for web push notifications...');

        try {
            // Generate VAPID keys
            $keys = VAPID::createVapidKeys();

            $publicKey = $keys['publicKey'];
            $privateKey = $keys['privateKey'];

            if ($this->option('show')) {
                // Just show the keys
                $this->info('VAPID Keys Generated:');
                $this->line('');
                $this->line('Public Key:');
                $this->line($publicKey);
                $this->line('');
                $this->line('Private Key:');
                $this->line($privateKey);
                $this->line('');
                $this->warn('Add these to your .env file:');
                $this->line('VAPID_PUBLIC_KEY=' . $publicKey);
                $this->line('VAPID_PRIVATE_KEY=' . $privateKey);
                $this->line('VAPID_SUBJECT=' . config('app.url'));
            } else {
                // Update .env file
                $this->updateEnvFile($publicKey, $privateKey);
                $this->info('VAPID keys have been generated and added to your .env file!');
                $this->line('');
                $this->warn('Important: Make sure to restart your application server to load the new environment variables.');
            }

            $this->line('');
            $this->info('✅ VAPID keys generated successfully!');

        } catch (\Exception $e) {
            $this->error('Failed to generate VAPID keys: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }

    /**
     * Update the .env file with VAPID keys.
     */
    private function updateEnvFile($publicKey, $privateKey)
    {
        $envPath = base_path('.env');

        if (!file_exists($envPath)) {
            $this->error('.env file not found!');
            return;
        }

        $envContent = file_get_contents($envPath);

        // Check if VAPID keys already exist
        if (strpos($envContent, 'VAPID_PUBLIC_KEY=') !== false) {
            // Update existing keys
            $envContent = preg_replace('/VAPID_PUBLIC_KEY=.*/', 'VAPID_PUBLIC_KEY=' . $publicKey, $envContent);
            $envContent = preg_replace('/VAPID_PRIVATE_KEY=.*/', 'VAPID_PRIVATE_KEY=' . $privateKey, $envContent);

            // Add VAPID_SUBJECT if it doesn't exist
            if (strpos($envContent, 'VAPID_SUBJECT=') === false) {
                $envContent .= "\nVAPID_SUBJECT=" . config('app.url');
            }
        } else {
            // Add new keys
            $envContent .= "\n# VAPID Keys for Web Push Notifications\n";
            $envContent .= "VAPID_PUBLIC_KEY=" . $publicKey . "\n";
            $envContent .= "VAPID_PRIVATE_KEY=" . $privateKey . "\n";
            $envContent .= "VAPID_SUBJECT=" . config('app.url') . "\n";
        }

        file_put_contents($envPath, $envContent);

        $this->line('Updated .env file with VAPID keys.');
    }
}
