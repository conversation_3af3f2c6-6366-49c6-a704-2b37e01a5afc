<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use App\Services\PushNotificationService;

class NotificationController extends Controller
{
    /**
     * Get all admin notifications
     */
    public function index()
    {
        try {
            Log::info('📋 Fetching admin notifications');

            $notifications = DB::table('notifications')
                ->orderBy('created_at', 'desc')
                ->get();

            Log::info("✅ Found {$notifications->count()} notifications");

            return response()->json([
                'success' => true,
                'message' => 'Notifications retrieved successfully',
                'data' => $notifications->map(function ($notification) {
                    // Calculate recipient count for each notification
                    $recipientCount = 0;
                    if ($notification->recipient_type === 'all') {
                        // Count all users (students + employees + admins)
                        $recipientCount = \App\Models\Student::count() + \App\Models\Employee::count() + \App\Models\User::count();
                    } elseif ($notification->recipient_type === 'students') {
                        $recipientCount = \App\Models\Student::count();
                    } elseif ($notification->recipient_type === 'employees') {
                        $recipientCount = \App\Models\Employee::count();
                    } elseif (is_array(json_decode($notification->recipient_ids))) {
                        $recipientCount = count(json_decode($notification->recipient_ids));
                    }

                    return [
                        'id' => $notification->id,
                        'title' => $notification->title,
                        'message' => $notification->message,
                        'sender_type' => $notification->sender_type,
                        'sender_id' => $notification->sender_id,
                        'sender_name' => $notification->sender_name,
                        'recipient_type' => $notification->recipient_type,
                        'recipient_ids' => $notification->recipient_ids,
                        'recipient_count' => $recipientCount,
                        'attachment_path' => $notification->attachment_path,
                        'attachment_name' => $notification->attachment_name,
                        'priority' => $notification->priority,
                        'status' => $notification->status,
                        'created_at' => $notification->created_at,
                        'updated_at' => $notification->updated_at,
                    ];
                }),
            ]);
        } catch (\Exception $e) {
            Log::error('❌ Error fetching notifications: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Error fetching notifications: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    /**
     * Send notification
     */
    public function send(Request $request)
    {
        try {
            Log::info('📤 Sending notification via API');
            Log::info('📤 Request data: ' . json_encode($request->all()));

            // Validate request
            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:255',
                'message' => 'required|string',
                'sender_id' => 'required|integer',
                'sender_name' => 'required|string|max:255',
                'sender_type' => 'string|max:50',
                'recipient_type' => 'required|string|max:50',
                'recipient_ids' => 'required|array',
                'priority' => 'string|in:low,medium,high',
                // Support both file uploads and direct paths
                'attachments' => 'nullable|array',
                'attachments.*' => 'nullable|file|max:10240', // 10MB max per file
                'attachment_paths' => 'nullable|array',
                'attachment_paths.*' => 'nullable|string',
                'attachment_names' => 'nullable|array',
                'attachment_names.*' => 'nullable|string',
                // Legacy support
                'attachment_path' => 'nullable|string',
                'attachment_name' => 'nullable|string',
            ]);

            if ($validator->fails()) {
                Log::warning('❌ Validation failed: ' . json_encode($validator->errors()));
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Handle file uploads
            $attachmentPaths = [];
            $attachmentNames = [];

            // Process uploaded files
            if ($request->hasFile('attachments')) {
                foreach ($request->file('attachments') as $file) {
                    if ($file->isValid()) {
                        // Generate unique filename
                        $originalName = $file->getClientOriginalName();
                        $extension = $file->getClientOriginalExtension();
                        $filename = time() . '_' . uniqid() . '.' . $extension;

                        // Store file in notifications directory
                        $path = $file->storeAs('notifications', $filename, 'public');

                        $attachmentPaths[] = $path;
                        $attachmentNames[] = $originalName;

                        Log::info("✅ File uploaded: {$originalName} -> {$path}");
                    }
                }
            }

            // Process direct paths (if provided instead of files)
            if ($request->has('attachment_paths') && is_array($request->attachment_paths)) {
                $attachmentPaths = array_merge($attachmentPaths, $request->attachment_paths);
            }
            if ($request->has('attachment_names') && is_array($request->attachment_names)) {
                $attachmentNames = array_merge($attachmentNames, $request->attachment_names);
            }

            // Legacy support for single attachment
            if ($request->has('attachment_path') && !empty($request->attachment_path)) {
                $attachmentPaths[] = $request->attachment_path;
                $attachmentNames[] = $request->attachment_name ?? basename($request->attachment_path);
            }

            // Handle case where we have attachment names but no paths (from Flutter)
            if (!empty($attachmentNames) && empty($attachmentPaths)) {
                // Generate default paths for attachment names
                foreach ($attachmentNames as $name) {
                    $attachmentPaths[] = 'notifications/' . $name;
                }
                Log::info("📎 Generated default paths for attachment names: " . json_encode($attachmentPaths));
            }

            // Create notification using Eloquent model to benefit from casts
            $notification = new \App\Models\Notification();
            $notification->title = $request->title;
            $notification->message = $request->message;
            $notification->sender_type = $request->sender_type ?? 'admin';
            $notification->sender_id = $request->sender_id;
            $notification->sender_name = $request->sender_name;
            $notification->recipient_type = $request->recipient_type;
            $notification->recipient_ids = $request->recipient_ids; // Will be cast to array
            // Use processed attachments
            $notification->attachment_path = !empty($attachmentPaths) ? $attachmentPaths : null;
            $notification->attachment_name = !empty($attachmentNames) ? $attachmentNames : null;
            $notification->priority = $request->priority ?? 'medium';
            $notification->status = 'sent';
            $notification->save();

            Log::info("✅ Notification saved with attachments: " . json_encode([
                'paths' => $attachmentPaths,
                'names' => $attachmentNames
            ]));

            $notificationId = $notification->id;

            Log::info("✅ Notification created with ID: {$notificationId}");

            // Get the created notification (refresh from database)
            $notification = \App\Models\Notification::find($notificationId);

            // Create individual notification recipients based on recipient_type
            $recipientCount = $this->createNotificationRecipients($notification);

            Log::info("✅ Created {$recipientCount} notification recipients");

            // Send push notifications
            try {
                $pushService = app(PushNotificationService::class);
                $pushResult = $pushService->sendToNotificationRecipients(
                    $notification->id,
                    $notification->title,
                    $notification->message,
                    [
                        'notification_id' => $notification->id,
                        'action_url' => '/notifications',
                        'priority' => $notification->priority,
                        'timestamp' => now()->toISOString()
                    ]
                );

                Log::info("📱 Push notifications sent: {$pushResult['sent']}, errors: " . count($pushResult['errors']));

                if (!empty($pushResult['errors'])) {
                    Log::warning("📱 Push notification errors: " . json_encode($pushResult['errors']));
                }
            } catch (\Exception $e) {
                Log::error("📱 Failed to send push notifications: " . $e->getMessage());
                // Don't fail the entire request if push notifications fail
            }

            return response()->json([
                'success' => true,
                'message' => 'Notification sent successfully',
                'data' => [
                    'id' => $notification->id,
                    'title' => $notification->title,
                    'message' => $notification->message,
                    'sender_type' => $notification->sender_type,
                    'sender_id' => $notification->sender_id,
                    'sender_name' => $notification->sender_name,
                    'recipient_type' => $notification->recipient_type,
                    'recipient_ids' => $notification->recipient_ids,
                    'recipient_count' => $recipientCount,
                    'attachment_path' => $notification->attachment_path,
                    'attachment_name' => $notification->attachment_name,
                    'priority' => $notification->priority,
                    'status' => $notification->status,
                    'created_at' => $notification->created_at,
                    'updated_at' => $notification->updated_at,
                ]
            ], 201);

        } catch (\Exception $e) {
            Log::error('❌ Error sending notification: ' . $e->getMessage());
            Log::error('❌ Stack trace: ' . $e->getTraceAsString());

            return response()->json([
                'success' => false,
                'message' => 'Error sending notification: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * Get student notifications
     */
    public function studentNotifications(Request $request)
    {
        try {
            // For now, return empty array - will implement later
            return response()->json([
                'success' => true,
                'message' => 'Student notifications retrieved successfully',
                'data' => []
            ]);
        } catch (\Exception $e) {
            Log::error('❌ Error fetching student notifications: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Error fetching student notifications: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    /**
     * Get employee notifications
     */
    public function employeeNotifications(Request $request)
    {
        try {
            // For now, return empty array - will implement later
            return response()->json([
                'success' => true,
                'message' => 'Employee notifications retrieved successfully',
                'data' => []
            ]);
        } catch (\Exception $e) {
            Log::error('❌ Error fetching employee notifications: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Error fetching employee notifications: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    /**
     * Mark notification as read
     */
    public function markAsRead(Request $request, $id)
    {
        try {
            // For now, just return success - will implement later
            return response()->json([
                'success' => true,
                'message' => 'Notification marked as read successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('❌ Error marking notification as read: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Error marking notification as read: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Redirect to send method
        return $this->send($request);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {
            $notification = DB::table('notifications')->where('id', $id)->first();

            if (!$notification) {
                return response()->json([
                    'success' => false,
                    'message' => 'Notification not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Notification retrieved successfully',
                'data' => $notification
            ]);
        } catch (\Exception $e) {
            Log::error('❌ Error fetching notification: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Error fetching notification: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            Log::info('🔄 Updating notification ID: ' . $id);
            Log::info('📤 Update request data: ' . json_encode($request->all()));

            // Find the notification
            $notification = \App\Models\Notification::find($id);
            if (!$notification) {
                return response()->json([
                    'success' => false,
                    'message' => 'Notification not found'
                ], 404);
            }

            // Validate request
            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:255',
                'message' => 'required|string',
                'sender_id' => 'required|integer',
                'sender_name' => 'required|string|max:255',
                'sender_type' => 'string|max:50',
                'recipient_type' => 'required|string|max:50',
                'recipient_ids' => 'required|array',
                'priority' => 'string|in:low,medium,high',
                'attachment_names' => 'nullable|array',
                'attachment_names.*' => 'nullable|string',
                'attachment_paths' => 'nullable|array',
                'attachment_paths.*' => 'nullable|string',
            ]);

            if ($validator->fails()) {
                Log::warning('❌ Validation failed: ' . json_encode($validator->errors()));
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Process attachments (same logic as send method)
            $attachmentPaths = [];
            $attachmentNames = [];

            // Process direct paths and names
            if ($request->has('attachment_paths') && is_array($request->attachment_paths)) {
                $attachmentPaths = array_merge($attachmentPaths, $request->attachment_paths);
            }
            if ($request->has('attachment_names') && is_array($request->attachment_names)) {
                $attachmentNames = array_merge($attachmentNames, $request->attachment_names);
            }

            // Handle case where we have attachment names but no paths (from Flutter)
            if (!empty($attachmentNames) && empty($attachmentPaths)) {
                foreach ($attachmentNames as $name) {
                    $attachmentPaths[] = 'notifications/' . $name;
                }
                Log::info("📎 Generated default paths for attachment names: " . json_encode($attachmentPaths));
            }

            // Update notification
            $notification->title = $request->title;
            $notification->message = $request->message;
            $notification->sender_type = $request->sender_type ?? 'admin';
            $notification->sender_id = $request->sender_id;
            $notification->sender_name = $request->sender_name;
            $notification->recipient_type = $request->recipient_type;
            $notification->recipient_ids = $request->recipient_ids;
            $notification->attachment_path = !empty($attachmentPaths) ? $attachmentPaths : null;
            $notification->attachment_name = !empty($attachmentNames) ? $attachmentNames : null;
            $notification->priority = $request->priority ?? 'medium';
            $notification->save();

            Log::info("✅ Notification updated successfully with ID: {$notification->id}");

            // Calculate recipient count
            $recipientCount = 0;
            if ($notification->recipient_type === 'all') {
                $recipientCount = \App\Models\Student::count() + \App\Models\Employee::count() + \App\Models\User::count();
            } elseif ($notification->recipient_type === 'students') {
                $recipientCount = \App\Models\Student::count();
            } elseif ($notification->recipient_type === 'employees') {
                $recipientCount = \App\Models\Employee::count();
            } elseif (is_array($notification->recipient_ids)) {
                $recipientCount = count($notification->recipient_ids);
            }

            return response()->json([
                'success' => true,
                'message' => 'Notification updated successfully',
                'data' => [
                    'id' => $notification->id,
                    'title' => $notification->title,
                    'message' => $notification->message,
                    'sender_type' => $notification->sender_type,
                    'sender_id' => $notification->sender_id,
                    'sender_name' => $notification->sender_name,
                    'recipient_type' => $notification->recipient_type,
                    'recipient_ids' => $notification->recipient_ids,
                    'recipient_count' => $recipientCount,
                    'attachment_path' => $notification->attachment_path,
                    'attachment_name' => $notification->attachment_name,
                    'priority' => $notification->priority,
                    'status' => $notification->status,
                    'created_at' => $notification->created_at,
                    'updated_at' => $notification->updated_at,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('❌ Error updating notification: ' . $e->getMessage());
            Log::error('❌ Stack trace: ' . $e->getTraceAsString());

            return response()->json([
                'success' => false,
                'message' => 'Error updating notification: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $deleted = DB::table('notifications')->where('id', $id)->delete();

            if (!$deleted) {
                return response()->json([
                    'success' => false,
                    'message' => 'Notification not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Notification deleted successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('❌ Error deleting notification: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Error deleting notification: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Bulk delete notifications
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function bulkDelete(Request $request)
    {
        try {
            Log::info('📤 Bulk deleting notifications via API');
            Log::info('📤 Request data: ' . json_encode($request->all()));
            
            $validator = Validator::make($request->all(), [
                'notification_ids' => 'required|array',
                'notification_ids.*' => 'integer|exists:notifications,id'
            ]);
            
            if ($validator->fails()) {
                Log::warning('❌ Validation failed: ' . json_encode($validator->errors()));
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }
            
            $ids = $request->notification_ids;
            
            // First delete recipients
            $recipientsDeleted = DB::table('notification_recipients')
                ->whereIn('notification_id', $ids)
                ->delete();
                
            Log::info("✅ Deleted {$recipientsDeleted} notification recipients");
            
            // Then delete notifications
            $count = DB::table('notifications')
                ->whereIn('id', $ids)
                ->delete();
                
            Log::info("✅ Deleted {$count} notifications");
            
            return response()->json([
                'success' => true,
                'message' => $count . ' notifications have been deleted successfully.',
                'data' => [
                    'deleted_count' => $count,
                    'recipients_deleted' => $recipientsDeleted
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error('❌ Error bulk deleting notifications: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Error bulk deleting notifications: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Bulk update notification status
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function bulkUpdateStatus(Request $request)
    {
        try {
            Log::info('📤 Bulk updating notification status via API');
            Log::info('📤 Request data: ' . json_encode($request->all()));
            
            $validator = Validator::make($request->all(), [
                'notification_ids' => 'required|array',
                'notification_ids.*' => 'integer|exists:notifications,id',
                'status' => 'required|string|in:draft,sent'
            ]);
            
            if ($validator->fails()) {
                Log::warning('❌ Validation failed: ' . json_encode($validator->errors()));
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }
            
            $ids = $request->notification_ids;
            $status = $request->status;
            
            // Update notifications status
            $count = DB::table('notifications')
                ->whereIn('id', $ids)
                ->update(['status' => $status]);
                
            Log::info("✅ Updated status of {$count} notifications to {$status}");
            
            // If status is 'sent', update recipients too
            $recipientsUpdated = 0;
            if ($status === 'sent') {
                $now = now();
                $recipientsUpdated = DB::table('notification_recipients')
                    ->whereIn('notification_id', $ids)
                    ->update([
                        'status' => 'sent',
                        'sent_at' => $now
                    ]);
                    
                Log::info("✅ Updated {$recipientsUpdated} notification recipients");
            }
            
            return response()->json([
                'success' => true,
                'message' => $count . ' notifications have been updated to "' . ucfirst($status) . '" status.',
                'data' => [
                    'updated_count' => $count,
                    'recipients_updated' => $recipientsUpdated,
                    'status' => $status
                ]
            ]);
            
        } catch (\Exception $e) {
            Log::error('❌ Error bulk updating notification status: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Error bulk updating notification status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create individual notification recipients based on notification type
     */
    private function createNotificationRecipients($notification)
    {
        $recipientCount = 0;

        try {
            // Handle both 'all' recipient_type and ['all'] in recipient_ids
            $shouldCreateAllRecipients = ($notification->recipient_type === 'all') ||
                                        (is_array($notification->recipient_ids) && in_array('all', $notification->recipient_ids));

            Log::info("🔍 Creating recipients for notification {$notification->id}", [
                'recipient_type' => $notification->recipient_type,
                'recipient_ids' => $notification->recipient_ids,
                'should_create_all' => $shouldCreateAllRecipients
            ]);

            if ($shouldCreateAllRecipients) {
                Log::info("🎯 Creating recipients for ALL students and employees");

                // Get all active students
                $students = \App\Models\Student::where('is_active', 1)->get();
                Log::info("📚 Found {$students->count()} active students");

                foreach ($students as $student) {
                    \App\Models\NotificationRecipient::create([
                        'notification_id' => $notification->id,
                        'recipient_id' => $student->id,
                        'recipient_type' => 'student',
                        'recipient_name' => $student->full_name,
                        'recipient_class' => $student->class ?? $student->student_class,
                        'read_at' => null,
                        'status' => 'sent',
                        'sent_at' => now(),
                    ]);
                    $recipientCount++;
                }

                // Get all employees (no job_status filter as requested)
                $employees = \App\Models\Employee::all();
                Log::info("👥 Found {$employees->count()} employees");

                foreach ($employees as $employee) {
                    \App\Models\NotificationRecipient::create([
                        'notification_id' => $notification->id,
                        'recipient_id' => $employee->id,
                        'recipient_type' => 'employee',
                        'recipient_name' => $employee->full_name,
                        'recipient_class' => $employee->employee_type,
                        'read_at' => null,
                        'status' => 'sent',
                        'sent_at' => now(),
                    ]);
                    $recipientCount++;
                }

            } elseif ($notification->recipient_type === 'students') {
                // Get all active students
                $students = \App\Models\Student::where('is_active', 1)->get();
                foreach ($students as $student) {
                    \App\Models\NotificationRecipient::create([
                        'notification_id' => $notification->id,
                        'recipient_id' => $student->id,
                        'recipient_type' => 'student',
                        'recipient_name' => $student->full_name,
                        'recipient_class' => $student->class ?? $student->student_class,
                        'read_at' => null,
                        'status' => 'sent',
                        'sent_at' => now(),
                    ]);
                    $recipientCount++;
                }

            } elseif ($notification->recipient_type === 'employees') {
                // Get all employees
                $employees = \App\Models\Employee::all();
                foreach ($employees as $employee) {
                    \App\Models\NotificationRecipient::create([
                        'notification_id' => $notification->id,
                        'recipient_id' => $employee->id,
                        'recipient_type' => 'employee',
                        'recipient_name' => $employee->full_name,
                        'recipient_class' => $employee->employee_type,
                        'read_at' => null,
                        'status' => 'sent',
                        'sent_at' => now(),
                    ]);
                    $recipientCount++;
                }

            } elseif (is_array($notification->recipient_ids)) {
                // Handle specific recipient IDs
                foreach ($notification->recipient_ids as $recipientId) {
                    if (is_numeric($recipientId)) {
                        // Assume it's a student ID for backward compatibility
                        $student = \App\Models\Student::find($recipientId);
                        if ($student) {
                            \App\Models\NotificationRecipient::create([
                                'notification_id' => $notification->id,
                                'recipient_id' => $student->id,
                                'recipient_type' => 'student',
                                'recipient_name' => $student->full_name,
                                'recipient_class' => $student->class ?? $student->student_class,
                                'read_at' => null,
                                'status' => 'sent',
                                'sent_at' => now(),
                            ]);
                            $recipientCount++;
                        }
                    }
                }
            }

            Log::info("✅ Successfully created {$recipientCount} notification recipients for notification {$notification->id}");

        } catch (\Exception $e) {
            Log::error("❌ Error creating notification recipients: " . $e->getMessage());
            Log::error("❌ Exception details: " . $e->getTraceAsString());
        }

        return $recipientCount;
    }
}
