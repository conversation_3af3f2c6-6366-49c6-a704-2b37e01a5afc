@extends('layouts.app')

@section('styles')
<style>
    .profile-card {
        border-radius: 15px;
        box-shadow: 0 8px 20px rgba(0,0,0,0.08);
        overflow: hidden;
        border: none;
    }
    
    .profile-header {
        padding: 1rem 1rem;
        background-color: var(--costa-del-sol);
        border-bottom: none;
        position: relative;
    }
    
    .profile-header::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 8px;
        background: linear-gradient(90deg, var(--locust), var(--costa-del-sol));
    }
    
    .profile-body {
        padding: 2rem;
    }
    
    .profile-title {
        color: white;
        font-weight: 600;
        margin-bottom: 0;
        display: flex;
        align-items: center;
    }
    
    .profile-title i {
        color: rgba(255, 255, 255, 0.85) !important;
        margin-right: 10px;
    }
    
    .form-section {
        padding: 1rem;
        margin-bottom: 0.8rem;
        border-radius: 8px;
        background-color: #fff;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }
    
    .form-section:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
    }
    
    .section-title {
        font-size: 0.9rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0.5rem;
        padding-bottom: 0.3rem;
        border-bottom: 1px solid #eee;
    }
    
    .section-title i {
        color: var(--locust);
        margin-right: 8px;
    }
    
    .form-control, .form-select {
        padding: 0.35rem 0.65rem;
        border-radius: 6px;
        border: 1px solid #d9dee3;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        font-size: 0.9rem;
    }
    
    .form-control:focus {
        border-color: var(--costa-del-sol);
        box-shadow: 0 0 0 0.25rem rgba(125, 145, 120, 0.15);
    }
    
    .form-control:disabled, .form-control[readonly] {
        background-color: #f8f9fa;
        opacity: 0.8;
    }
    
    .form-select {
        background-position: right 1rem center;
    }
    
    .form-select:focus {
        border-color: var(--costa-del-sol);
        box-shadow: 0 0 0 0.25rem rgba(125, 145, 120, 0.15);
    }
    
    .form-label {
        color: var(--costa-del-sol);
        font-weight: 500;
        margin-bottom: 0.2rem;
        font-size: 0.85rem;
    }
    
    .form-text {
        color: #6c757d;
        font-size: 0.85rem;
    }
    
    .primary-btn {
        background: linear-gradient(90deg, var(--costa-del-sol), var(--locust));
        color: white;
        border-radius: 30px;
        padding: 0.75rem 2rem;
        border: none;
        font-weight: 500;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        z-index: 1;
    }
    
    .primary-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, var(--locust), var(--costa-del-sol));
        transition: all 0.3s ease;
        z-index: -1;
    }
    
    .primary-btn:hover::before {
        left: 0;
    }
    
    .primary-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .secondary-btn {
        background-color: white;
        color: var(--costa-del-sol);
        border-radius: 30px;
        border: 1px solid var(--costa-del-sol);
        padding: 0.75rem 2rem;
        text-decoration: none;
        transition: all 0.3s ease;
        font-weight: 500;
    }
    
    .secondary-btn:hover {
        background-color: var(--costa-del-sol);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .field-help {
        color: #6c757d;
        font-size: 0.75rem;
        margin-top: 0.25rem;
    }
    
    .field-notice {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        background-color: #f8f9fa;
        border-radius: 4px;
        border-left: 3px solid #6c757d;
        font-size: 0.75rem;
        color: #6c757d;
        margin-top: 0.5rem;
    }
    
    /* Improve input group styling */
    .input-group-text {
        background-color: #f8f9fa;
        color: var(--costa-del-sol);
        border-color: #dee2e6;
    }
    
    /* Custom field states */
    .form-control.is-valid, .form-select.is-valid {
        border-color: var(--locust);
        background-image: none;
    }
    
    /* Clean alert styling */
    .alert {
        border-radius: 10px;
        border: none;
        padding: 1rem 1.25rem;
    }
    
    .alert-success {
        background-color: rgba(212, 237, 218, 0.7);
        color: #155724;
    }
    
    .alert-danger {
        background-color: rgba(248, 215, 218, 0.7);
        color: #721c24;
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
        .profile-header {
            padding: 1.5rem 1.5rem;
        }

        .profile-body {
            padding: 1.5rem;
        }
    }

    @media (max-width: 992px) {
        .container-fluid.main-content {
            padding-left: 1rem !important;
            padding-right: 1rem !important;
        }

        .col-md-9 {
            max-width: 100%;
            flex: 0 0 100%;
        }

        .profile-header {
            padding: 1.25rem;
        }

        .profile-body {
            padding: 1.25rem;
        }

        .form-section {
            padding: 0.75rem;
            margin-bottom: 0.75rem;
        }
    }

    @media (max-width: 768px) {
        .container-fluid.main-content {
            padding-left: 0.75rem !important;
            padding-right: 0.75rem !important;
        }

        /* Header responsive */
        .d-flex.justify-content-between.align-items-center {
            flex-direction: column;
            align-items: flex-start !important;
            gap: 1rem;
        }

        .d-flex.justify-content-between.align-items-center > div:last-child {
            align-self: flex-end;
        }

        /* Make secondary button smaller on mobile */
        .secondary-btn {
            padding: 0.4rem 1rem !important;
            font-size: 0.8rem !important;
            border-radius: 15px !important;
        }

        .secondary-btn i {
            font-size: 0.75rem;
        }

        /* Profile card responsive */
        .profile-card {
            border-radius: 12px;
            margin-bottom: 1rem;
        }

        .profile-header {
            padding: 1rem;
        }

        .profile-body {
            padding: 1rem;
        }

        .profile-title {
            font-size: 1rem;
        }

        .form-section {
            padding: 0.75rem;
            margin-bottom: 0.75rem;
        }

        .section-title {
            font-size: 0.85rem;
        }

        /* Form controls responsive */
        .form-control, .form-select {
            padding: 0.4rem 0.7rem;
            font-size: 0.85rem;
        }

        .form-label {
            font-size: 0.8rem;
            margin-bottom: 0.25rem;
        }

        /* Stack form fields on mobile */
        .row.mb-2 .col-md-4 {
            margin-bottom: 0.75rem;
        }

        .row.mb-2 .col-md-4:last-child {
            margin-bottom: 0;
        }

        /* Button responsive */
        .primary-btn {
            padding: 0.6rem 1.25rem;
            font-size: 0.9rem;
        }

        .field-notice {
            font-size: 0.7rem;
        }
    }

    @media (max-width: 576px) {
        .container-fluid.main-content {
            padding-left: 0.5rem !important;
            padding-right: 0.5rem !important;
        }

        /* Even smaller button for very small screens */
        .secondary-btn {
            padding: 0.3rem 0.8rem !important;
            font-size: 0.75rem !important;
            border-radius: 12px !important;
        }

        .secondary-btn i {
            font-size: 0.7rem;
        }

        /* Page title smaller */
        h2 {
            font-size: 1.4rem !important;
        }

        .profile-header {
            padding: 0.75rem;
        }

        .profile-body {
            padding: 0.75rem;
        }

        .profile-title {
            font-size: 0.9rem;
        }

        .section-title {
            font-size: 0.8rem;
        }

        .form-section {
            padding: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .form-control, .form-select {
            padding: 0.35rem 0.6rem;
            font-size: 0.8rem;
        }

        .form-label {
            font-size: 0.75rem;
        }

        .primary-btn {
            padding: 0.5rem 1rem;
            font-size: 0.85rem;
        }

        .field-notice {
            font-size: 0.65rem;
        }

        /* Alert responsive */
        .alert {
            padding: 0.5rem;
            font-size: 0.8rem;
        }

        .alert ul {
            margin-bottom: 0;
            padding-left: 1rem;
        }
    }

    /* Smooth transitions for responsive changes */
    .profile-card,
    .form-section,
    .form-control,
    .form-select,
    .primary-btn,
    .secondary-btn {
        transition: all 0.3s ease;
    }

    /* Improved focus states for accessibility */
    .form-control:focus,
    .form-select:focus,
    .primary-btn:focus,
    .secondary-btn:focus {
        outline: 2px solid var(--costa-del-sol);
        outline-offset: 2px;
    }
</style>
@endsection

@section('content')
<div class="container-fluid main-content px-4">
    <div class="row">
        <div class="col-md-9">
            <div class="d-flex justify-content-between align-items-start align-items-md-center mb-3 mt-2 flex-column flex-md-row gap-3 gap-md-0">
                <div class="flex-grow-1">
                    <h2 class="mb-1 h3 h2-md" style="color: var(--costa-del-sol); font-weight: 700;">
                        <i class="fas fa-user-edit me-2"></i> Employee Profile
                    </h2>
                    <p class="mb-0 text-muted small">
                        Update your personal and employment information
                    </p>
                </div>
                <div class="align-self-end align-self-md-auto">
                    <a href="{{ route('employee.dashboard') }}" class="secondary-btn">
                        <i class="fas fa-arrow-left me-1"></i>
                        <span class="d-none d-sm-inline">Back to </span>Dashboard
                    </a>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-12">
                    <!-- Profile Information Form -->
                    <div class="card profile-card mb-2">
                        <div class="profile-header">
                            <h5 class="profile-title"><i class="fas fa-user-edit"></i>Profile Information</h5>
                        </div>
                        <div class="profile-body">
                            @if(session('success'))
                                <div class="alert alert-success alert-dismissible fade show" role="alert">
                                    {{ session('success') }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            @endif
                            
                            @if($errors->any())
                                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                    <ul class="mb-0">
                                        @foreach($errors->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            @endif
                            
                            <form action="{{ route('employee.profile.update') }}" method="POST">
                                @csrf
                                @method('PUT')
                                
                                <div class="form-section">
                                    <h6 class="section-title"><i class="fas fa-id-card"></i>Basic Information</h6>
                                    
                                    <div class="row mb-3">
                                        <div class="col-lg-4 col-md-6 col-12 mb-3 mb-lg-0">
                                            <label for="username" class="form-label fw-semibold">
                                                <i class="fas fa-user me-1 text-muted"></i> Username
                                            </label>
                                            <input type="text" class="form-control" id="username" value="{{ $employee->username }}" readonly>
                                            <div class="field-notice">
                                                <i class="fas fa-info-circle me-1"></i> Username cannot be changed
                                            </div>
                                        </div>
                                        <div class="col-lg-4 col-md-6 col-12 mb-3 mb-lg-0">
                                            <label for="full_name" class="form-label fw-semibold">
                                                <i class="fas fa-id-card me-1 text-muted"></i> Full Name
                                            </label>
                                            <input type="text" class="form-control @error('full_name') is-invalid @enderror" id="full_name" name="full_name" value="{{ old('full_name', $employee->full_name ?? '') }}" placeholder="Enter your full name">
                                            @error('full_name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div class="col-lg-4 col-12">
                                            <label for="phone" class="form-label fw-semibold">
                                                <i class="fas fa-phone me-1 text-muted"></i> Phone Number
                                            </label>
                                            <input type="text" class="form-control @error('phone') is-invalid @enderror" id="phone" name="phone" value="{{ old('phone', $employee->phone) }}" placeholder="Enter phone number">
                                            @error('phone')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    
                                    <div class="row mb-3">
                                        <div class="col-lg-4 col-md-6 col-12 mb-3 mb-lg-0">
                                            <label for="employee_type" class="form-label fw-semibold">
                                                <i class="fas fa-briefcase me-1 text-muted"></i> Employee Type
                                            </label>
                                            <select class="form-select @error('employee_type') is-invalid @enderror" id="employee_type" name="employee_type">
                                                <option value="" disabled>Select Employee Type</option>
                                                @foreach($employeeTypes as $value => $label)
                                                    <option value="{{ $value }}" {{ old('employee_type', $employee->employee_type) == $value ? 'selected' : '' }}>{{ $label }}</option>
                                                @endforeach
                                            </select>
                                            @error('employee_type')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div class="col-lg-4 col-md-6 col-12 mb-3 mb-lg-0">
                                            <label for="contract_type" class="form-label fw-semibold">
                                                <i class="fas fa-file-contract me-1 text-muted"></i> Contract Type
                                            </label>
                                            <select class="form-select @error('contract_type') is-invalid @enderror" id="contract_type" name="contract_type">
                                                <option value="" disabled>Select Contract Type</option>
                                                @foreach($contractTypes as $value => $label)
                                                    <option value="{{ $value }}" {{ old('contract_type', $employee->contract_type) == $value ? 'selected' : '' }}>{{ $label }}</option>
                                                @endforeach
                                            </select>
                                            @error('contract_type')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div class="col-lg-4 col-12">
                                            <label for="job_status" class="form-label fw-semibold">
                                                <i class="fas fa-user-check me-1 text-muted"></i> Job Status
                                            </label>
                                            <select class="form-select @error('job_status') is-invalid @enderror" id="job_status" name="job_status">
                                                <option value="" disabled>Select Job Status</option>
                                                @foreach($jobStatuses as $value => $label)
                                                    <option value="{{ $value }}" {{ old('job_status', $employee->job_status) == $value ? 'selected' : '' }}>{{ $label }}</option>
                                                @endforeach
                                            </select>
                                            @error('job_status')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-section">
                                    <h6 class="section-title"><i class="fas fa-file-alt"></i>Employment Details</h6>

                                    <div class="row mb-3">
                                        <div class="col-lg-4 col-md-6 col-12 mb-3 mb-lg-0">
                                            <label for="financial_number" class="form-label fw-semibold">
                                                <i class="fas fa-calculator me-1 text-muted"></i> Financial Number
                                            </label>
                                            <input type="text" class="form-control @error('financial_number') is-invalid @enderror" id="financial_number" name="financial_number" value="{{ old('financial_number', $employee->financial_number) }}" placeholder="Enter financial number">
                                            @error('financial_number')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div class="col-lg-4 col-md-6 col-12 mb-3 mb-lg-0">
                                            <label for="automatic_number" class="form-label fw-semibold">
                                                <i class="fas fa-hashtag me-1 text-muted"></i> Automatic Number
                                            </label>
                                            <input type="text" class="form-control @error('automatic_number') is-invalid @enderror" id="automatic_number" name="automatic_number" value="{{ old('automatic_number', $employee->automatic_number) }}" placeholder="Enter automatic number">
                                            @error('automatic_number')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div class="col-lg-4 col-12">
                                            <label for="state_cooperative_number" class="form-label fw-semibold">
                                                <i class="fas fa-building me-1 text-muted"></i> State Cooperative Number
                                            </label>
                                            <input type="text" class="form-control @error('state_cooperative_number') is-invalid @enderror" id="state_cooperative_number" name="state_cooperative_number" value="{{ old('state_cooperative_number', $employee->state_cooperative_number) }}" placeholder="Enter cooperative number">
                                            @error('state_cooperative_number')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-section">
                                    <h6 class="section-title"><i class="fas fa-university"></i>Banking Information</h6>

                                    <div class="row mb-3">
                                        <div class="col-lg-4 col-md-6 col-12 mb-3 mb-lg-0">
                                            <label for="bank_account_number" class="form-label fw-semibold">
                                                <i class="fas fa-credit-card me-1 text-muted"></i> Bank Account Number
                                            </label>
                                            <input type="text" class="form-control @error('bank_account_number') is-invalid @enderror" id="bank_account_number" name="bank_account_number" value="{{ old('bank_account_number', $employee->bank_account_number) }}" placeholder="Enter account number">
                                            @error('bank_account_number')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div class="col-lg-4 col-md-6 col-12 mb-3 mb-lg-0">
                                            <label for="bank_name" class="form-label fw-semibold">
                                                <i class="fas fa-university me-1 text-muted"></i> Bank Name
                                            </label>
                                            <input type="text" class="form-control @error('bank_name') is-invalid @enderror" id="bank_name" name="bank_name" value="{{ old('bank_name', $employee->bank_name) }}" placeholder="Enter bank name">
                                            @error('bank_name')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div class="col-lg-4 col-12">
                                            <label for="bank_branch" class="form-label fw-semibold">
                                                <i class="fas fa-map-marker-alt me-1 text-muted"></i> Bank Branch
                                            </label>
                                            <input type="text" class="form-control @error('bank_branch') is-invalid @enderror" id="bank_branch" name="bank_branch" value="{{ old('bank_branch', $employee->bank_branch) }}" placeholder="Enter branch name">
                                            @error('bank_branch')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-section" style="margin-top: 1.5rem;">
                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <button type="submit" class="primary-btn flex-md-fill">
                                            <i class="fas fa-save me-2"></i>Update Profile
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <!-- Password Section -->
                    <div class="card profile-card">
                        <div class="profile-header">
                            <h5 class="profile-title"><i class="fas fa-shield-alt"></i>Security Settings</h5>
                        </div>
                        <div class="profile-body">
                            <div class="d-flex align-items-start mb-3">
                                <div class="flex-shrink-0 me-3">
                                    <i class="fas fa-lock fa-2x text-muted"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">Password Security</h6>
                                    <p class="mb-0 text-muted small">Keep your account secure by regularly updating your password. A strong password should include a mix of letters, numbers, and symbols.</p>
                                </div>
                            </div>
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="{{ route('employee.profile.change-password') }}" class="secondary-btn text-center flex-md-fill">
                                    <i class="fas fa-key me-2"></i>Change Password
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
